{"name": "entry-webapp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"cz": "git-cz", "ready": "yarn", "lint": "eslint ./src --ext js,jsx,ts,tsx --cache", "lint-fix": "eslint --fix --ext .js,jsx,ts,tsx src/", "clean:lib": "rimraf ./lib", "clean:dist": "rimraf ./dist", "generate-schema": "typescript-json-schema src/apis/onboardingMaterial.ts MaterialData --required", "predll": "npm run clean:lib", "----- webpack开发模式 -----": "", "local": "cross-env NODE_ENV=local webpack-dev-server  --config ./build/dev.config.js", "dev": "cross-env NODE_ENV=dev webpack-dev-server --config ./build/dev.config.js", "itTest": "cross-env NODE_ENV=itTest webpack-dev-server   --config ./build/dev.config.js", "itPre": "cross-env NODE_ENV=itPre webpack-dev-server   --config ./build/dev.config.js", "prod": "cross-env NODE_ENV=prod webpack-dev-server  --config ./build/dev.config.js", "----- vite开发模式 -----": "", "vite:local": "cross-env NODE_ENV=local vite  --config ./build/vite.config.js", "vite:dev": "cross-env NODE_ENV=test vite  --config ./build/vite.config.js", "vite:itTest": "cross-env NODE_ENV=itTest vite  --config ./build/vite.config.js", "vite:itPre": "cross-env NODE_ENV=itPre vite  --config ./build/vite.config.js", "----- 微前端本地开发模式 -----": "", "micro:local": "cross-env NODE_ENV=local webpack-dev-server  --config ./build/micro.config.js", "micro:dev": "cross-env NODE_ENV=dev webpack-dev-server --config ./build/micro.config.js", "micro:itTest": "cross-env NODE_OPTIONS=--max_old_space_size=8096 NODE_ENV=itTest webpack-dev-server   --config ./build/micro.config.js", "micro:itPre": "cross-env NODE_OPTIONS=--max_old_space_size=8096 NODE_ENV=itPre webpack-dev-server   --config ./build/micro.config.js", "micro:prod": "cross-env NODE_ENV=prod webpack-dev-server  --config ./build/micro.config.js", "----- 微前端+生成前端静态资源包 -----": "", "build:devdhr": "cross-env NODE_ENV=dev cross-env NODE_OPTIONS=--max_old_space_size=8192 webpack --progress --config ./build/microBuild.config.js", "build:dev": "cross-env NODE_ENV=itTest cross-env NODE_OPTIONS=--max_old_space_size=8192 webpack --progress --config ./build/microBuild.config.js", "build:test": "cross-env NODE_ENV=itTest cross-env NODE_OPTIONS=--max_old_space_size=8192 webpack --progress --config ./build/microBuild.config.js", "build:pre": "cross-env NODE_ENV=itPre cross-env NODE_OPTIONS=--max_old_space_size=8192 webpack --progress --config ./build/microBuild.config.js", "build:prod": "cross-env NODE_ENV=prod cross-env NODE_OPTIONS=--max_old_space_size=8192 webpack --progress --config ./build/microBuild.config.js"}, "pre-commit": ["lint"], "repository": {"type": "git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-import-replacement": "^0.1.1", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-decorators": "^7.16.4", "@babel/plugin-proposal-do-expressions": "^7.16.0", "@babel/plugin-proposal-export-default-from": "^7.16.0", "@babel/plugin-proposal-export-namespace-from": "^7.16.0", "@babel/plugin-proposal-function-bind": "^7.16.0", "@babel/plugin-proposal-function-sent": "^7.16.0", "@babel/plugin-proposal-json-strings": "^7.16.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-pipeline-operator": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.16.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.7", "@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@galaxy/dependency-collect": "^1.1.2", "@galaxy/dhr-standard": "0.0.13", "@originjs/vite-plugin-commonjs": "^1.0.2", "@pupu/apm-upload-sourcemap-plugin": "^2.0.1", "@types/history": "^4.7.9", "@types/node": "^20.2.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-transition-group": "^4.4.4", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.6.0", "@typescript-eslint/parser": "^5.6.0", "add-asset-html-webpack-plugin": "^3.2.0", "awesome-typescript-loader": "^5.2.1", "babel-eslint": "^9.0.0", "babel-loader": "^8.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "^3.3.4", "commitizen": "^4.2.6", "compression-webpack-plugin": "^9.0.1", "copy-webpack-plugin": "^5.1.2", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "cz-conventional-changelog": "^3.3.0", "cz-git": "^1.4.1", "eslint": "^8.41.0", "eslint-plugin-react": "^7.32.2", "file-system": "^2.2.2", "happypack": "^5.0.1", "html-webpack-deploy-plugin": "^3.0.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "json-schema-faker": "^0.5.3", "less": "^4.1.2", "less-loader": "^10.2.0", "lint-staged": "^13.1.0", "mini-css-extract-plugin": "^2.4.5", "mocker-api": "^2.9.4", "mockjs": "^1.1.0", "pre-commit": "^1.2.2", "prettier": "^2.8.8", "resolve-url-loader": "^4.0.0", "rimraf": "^3.0.2", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "style-resources-loader": "^1.5.0", "terser-webpack-plugin": "^5.3.10", "ts-auto-mock": "^3.7.0", "ts-loader": "^9.4.3", "type-fest": "^3.13.0", "typescript": "^4.5.2", "typescript-eslint": "^0.0.1-alpha.0", "typescript-json-schema": "^0.57.0", "vite": "^2.7.2", "vite-plugin-env-compatible": "^1.1.1", "vite-plugin-html": "^2.1.2", "webpack": "5.65.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "4.10.0", "webpack-dev-server": "^4.11.0"}, "dependencies": {"@amazebird/antd-field": "3.0.12", "@amazebird/antd-schema-form": "3.1.3", "@amazebird/antd-schema-table": "3.0.20", "@amazebird/schema-form": "3.1.3", "@ant-design/icons": "^4.7.0", "@ant-design/pro-form": "^1.52.13", "@ant-design/pro-layout": "6.31.4", "@babel/runtime-corejs3": "^7.22.5", "@galaxy/async-task-component": "0.3.0", "@galaxy/business-request": "3.4.1", "@galaxy/component": "0.4.0", "@galaxy/config-provider": "0.1.3", "@galaxy/dhr-style": "0.2.4-alpha.3", "@galaxy/dict": "3.9.0", "@galaxy/org-selector": "4.5.0", "@galaxy/rbac": "3.7.0", "@galaxy/rbac-component": "3.8.0", "@galaxy/uc": "3.3.9", "@galaxy/uc-component": "3.4.0", "@galaxy/upload": "3.4.8", "@galaxy/upload-component": "3.4.0", "@galaxy/user-selector": "4.7.0", "@galaxy/utils": "1.2.0", "@pupu/apm-browser": "^1.25.4", "@pupu/apm-tracing": "^1.21.3", "@pupu/elangsion-web": "^1.1.9", "@types/lodash-es": "^4.17.7", "ahooks": "^3.5.0", "antd": "^4.24.12", "axios": "^0.26.0", "babel-polyfill": "^6.26.0", "classnames": "^2.3.2", "crypto-js": "^4.1.1", "css-minimizer-webpack-plugin": "^5.0.1", "dayjs": "1.11.13", "immutability-helper": "^3.1.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.0", "normalize.css": "^8.0.1", "prop-types": "^15.7.2", "rc-field-form": "^1.22.1", "react": "18.2.0", "react-activation": "^0.12.4", "react-color": "2.19.3", "react-dnd": "15.1.2", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-loadable": "^5.5.0", "react-pdf": "^7.2.0", "react-router-dom": "^6.0.0", "react-sortablejs": "^6.0.0", "regenerator-runtime": "^0.13.11", "sortablejs": "^1.14.0", "terser-webpack-plugin": "^5.3.9", "tslib": "^2.6.2", "ua-parser-js": "^1.0.35", "uuid": "^9.0.0", "zustand": "^4.3.9"}, "lint-staged": {"*.{tsx,ts,jsx,js}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-git", "czConfig": "./node_modules/@galaxy/dhr-standard/dist/cz.config.js"}}, "resolutions": {"dayjs": "1.11.13"}, "engines": {"node": ">=20.18.0", "pnpm": ">=8.15.9"}, "packageManager": "pnpm@8.15.9+sha512.499434c9d8fdd1a2794ebf4552b3b25c0a633abcee5bb15e7b5de90f32f47b513aca98cd5cfd001c31f0db454bc3804edccd578501e4ca293a6816166bbd9f81"}