import base from './base'

const { projectName, loginUrl, publicPath, publicName } = base

export default {
  projectName,
  publicPath,
  baseRoute: `${publicName}`,
  environment: 'dev',
  loginUrl,
  // 企业微信
  appid: 'ww9dbeb2758faf497c',
  agentid: '1000082',
  authUrl: 'http://dhr.test.pupumall.net/admin/account/wechat_work',
  adminState: 'admin_wechat',
  // 请求前缀
  serverUrl: '//dhr.dev.pupumall.net', // k8s环境请求地址
  baseServerUrl: '//dhrapi.pre.pupufz.com',
  dmsConfig: {
    appId: ********, // 应用Id
    idHexStr: '394c28c162a17532d5552c79e3bf0a46', // 签名秘钥
    appVersion: '2.0.4', // 应用版本号
  },
  ucAppCode: '41031',
}
