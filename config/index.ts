import LocalConfig from './local'
import DevConfig from './dev'
import TestConfig from './test'
import ItTestConfig from './itTest'
import ItPreConfig from './itPre'
import ProdConfig from './prod'

const ENV = process.env.BUILD_ENV || 'itTest'

const configs = {
  dev: DevConfig,
  test: TestConfig,
  itTest: ItTestConfig,
  itPre: ItPreConfig,
  prod: ProdConfig,
}

const config = configs[ENV] || LocalConfig
export default config
