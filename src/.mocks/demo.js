// 引入mockjs，使用mockjs来进行数据生成；
const Mock = require('mockjs');
// const Random = Mock.Random;

module.exports = {
  // 直接使用地址，相当于GET方法，直接返回
  '/mock/user': {
    code: 0,
    data: {
      name: '刘沙',
      num: "213408"
    },
    msg: ''
  },
  '/mock/list': {
    code: 0,
    data: {
      items: [...new Array(10)].map((_, k) => ({
        id: k,
        name: k,
        email: k,
        birthday: k,
      })),
      total: 1000,
    },
    msg: '',
  },
  // 如果是get方法，并且后面加上id，例如getUserinfo/3478347
  // 需要前面加GET，方法中才能获取到请求的参数
  'GET mock/user/:id': (req, res) => {
    const { id } = req.params;
    return res.json({
      code: 0,
      data: {
        id,
      },
      msg: ''
    });
  },
  // 使用post进行登录权限的判断；
  'POST mock/user/login': (req, res) => {
    const { password, username } = req.body;
    if (password === 'admin' && username === 'admin') {
      return res.json({
        code: 0,
        data: {
          token: 'sdfsdfsdfdsf',
          id: 1,
          username: 'kenny',
          sex: 6,
        },
        msg: '登录成功！'
      });
    } else {
      return res.status(403).json({
        status: 'error',
        code: 403,
      });
    }
  }
};
