# 本地 mock 方法

> 本 mock 方案使用 mockjs 和 mocker-api。mockjs 用来做模拟数据生成，mocker-api 用来处理 api 的拦截和数据的返回。

## 使用方法

在 webpack 配置页面，引入

```
const apiMocker = require('mocker-api');
```

在 devServer 中增加如下配置

```
devServer: {
    // mocker-api
    before(app) {
      apiMocker(app, path.resolve(__dirname, '../src/.mocks/index'));
    },
},
```

第二个参数传入 mock 文件的地址，使用 path.resolve()去处理地址；

## 注意事情

使用的时候，需要注意的是，webpack 的拦截只会拦截请求向本机地址的请求，所以，请求的前缀要么都不加，要么加上本地地址的 webpack 服务地址。
例如：

```
export const getMockData = async params =>
services.get('http://127.0.0.1:8080/mockdemo/list');
```

## 其他

mockjs 的文档地址：  
https://github.com/nuysoft/Mock/wiki  
mock-api 文档地址：  
https://www.npmjs.com/package/mock-api
