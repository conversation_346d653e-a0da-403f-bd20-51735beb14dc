import { STASTUS_COLOR } from './color'
// 审核材料模块处理进度变量定义
export const PROCESS_PROGRESS_STATUS = {
  TO_BE_SUBMITTED: 0, // 待提交
  TO_BE_SUBMITTED_AGAIN: 1, // 待再次提交
  TO_BE_AUDITED: 2, // 待审核
  TO_BE_REVIEWED: 3, // 待复核
  TO_BE_READDICTED: 4, // 待重审
  AUDIT_PASSED: 5, // 审核通过
  AUDIT_FAILED: 6, // 审核不通过
  SPECIAL_APPROVAL_PASSED: 7, // 特批通过
  RECRUITMENT_ENDED: 8, // 结束招聘
}

export const EHR_PROGRESS_STATUS = {
  // 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功 默认0
  NONE: 0,
  TO_BE_ISSUED: 1,
  AUTO_RETRY: 2,
  ISSUE_FAILED: 3,
  ISSUE_SUCCESS: 4,
  ToBeArchived: 5, // 5-待代缴流程归档
}

// 审核材料模块处理进度状态
export const PROCESS_PROGRESS_STATUS_TEXT = {
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED]: '待提交',
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED_AGAIN]: '待再次提交',
  [PROCESS_PROGRESS_STATUS.TO_BE_AUDITED]: '待审核',
  [PROCESS_PROGRESS_STATUS.TO_BE_REVIEWED]: '待复核',
  [PROCESS_PROGRESS_STATUS.TO_BE_READDICTED]: '待重审',
  [PROCESS_PROGRESS_STATUS.AUDIT_PASSED]: '审核通过',
  [PROCESS_PROGRESS_STATUS.AUDIT_FAILED]: '审核不通过',
  [PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED]: '特批通过',
  [PROCESS_PROGRESS_STATUS.RECRUITMENT_ENDED]: '结束招聘',
}

// 审核材料模块处理进度状态对应的颜色
export const PROCESS_COLOR_STYLE = {
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED]: STASTUS_COLOR.YELLOW,
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED_AGAIN]: STASTUS_COLOR.YELLOW,
  [PROCESS_PROGRESS_STATUS.TO_BE_AUDITED]: STASTUS_COLOR.GREEN,
  [PROCESS_PROGRESS_STATUS.TO_BE_REVIEWED]: STASTUS_COLOR.GREEN,
  [PROCESS_PROGRESS_STATUS.TO_BE_READDICTED]: STASTUS_COLOR.GREEN,
  [PROCESS_PROGRESS_STATUS.AUDIT_PASSED]: STASTUS_COLOR.BLUE,
  [PROCESS_PROGRESS_STATUS.AUDIT_FAILED]: STASTUS_COLOR.RED,
  [PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED]: STASTUS_COLOR.BLUE,
  [PROCESS_PROGRESS_STATUS.RECRUITMENT_ENDED]: STASTUS_COLOR.RED,
}

// 审核材料模块由于mobx限制不支持style,因此需定义一些常量当作className的变量
export const PROCESS_COLOR_CLASS_NAME = {
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED]: 'yellow',
  [PROCESS_PROGRESS_STATUS.TO_BE_SUBMITTED_AGAIN]: 'yellow',
  [PROCESS_PROGRESS_STATUS.TO_BE_AUDITED]: 'green',
  [PROCESS_PROGRESS_STATUS.TO_BE_REVIEWED]: 'green',
  [PROCESS_PROGRESS_STATUS.TO_BE_READDICTED]: 'green',
  [PROCESS_PROGRESS_STATUS.AUDIT_PASSED]: 'blue',
  [PROCESS_PROGRESS_STATUS.AUDIT_FAILED]: 'red',
  [PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED]: 'blue',
  [PROCESS_PROGRESS_STATUS.RECRUITMENT_ENDED]: 'red',
}

export enum VIEW_MODE {
  DETAIL,
  EDIT,
  NONE,
}

// 审核材料模块审核状态定义
export enum FORM_MODE {
  MODIFY,
  DETAIL,
}

// 审核材料模块编辑态下的schemaForm mode定义
export const WATCH_MODES = {
  PERSON_BASE_INFO_MODE: 'person_baseInfo_mode',
  IDENTITY_INFO_MODE: 'identity_info_mode',
  EDUCATION_EXPERIENCE_MODE: 'education_experience_mode',
  RESIGNATION_CERTIFICATE_MODE: 'resignation_certificate_mode',
  HEALTH_INFO_MODE: 'health_info_mode',
  OTHER_INFO_MODE: 'other_info_mode',
  // 审核Form
  AUDIT_FORM_MODE: 'audit_form_mode',
  // 银行账户From
  BANK_INFO_FORM_MODE: 'bank_info_form_mode',
}

// 组件和审核材料模块编辑态下的schemaForm mode定义的映射
export const WATCH_MODES_MAPS = {
  personBaseInfo: WATCH_MODES.PERSON_BASE_INFO_MODE,
  identityInfo: WATCH_MODES.IDENTITY_INFO_MODE,
  educationExperience: WATCH_MODES.EDUCATION_EXPERIENCE_MODE,
  workExperiences: WATCH_MODES.RESIGNATION_CERTIFICATE_MODE,
  healthInfo: WATCH_MODES.HEALTH_INFO_MODE,
  otherInfo: WATCH_MODES.OTHER_INFO_MODE,
  bankCardInfo: WATCH_MODES.BANK_INFO_FORM_MODE,
}

// 用工形式
export const EMPLOYMENT_FORM = {
  FULL_TIME: '1', // 全职
  PART_TIME: '2', // 兼职
  INTERNSHIP: '3', // 实习
  PROJECT_COOPERATION: '4', // 项目合作
  SELF_EMPLOYED_THIRD_PARTY_CONTRACT: '5', // 自营员工第三方合同
  OUTSOURCING: '6', // 业务外包
  BASE_PRACTICE: '7', // 基地见习
}

// 员工类型
export const EMPLOYEE_TYPE = {
  // 实习
  INTERNSHIP: '1',
  // 试用
  PROBATION: '2',
  // 正式
  FORMAL: '3',
  // 社会兼职
  SOCIAL_PART_TIME: '4',
  // 学生兼职
  STUDENT_PART_TIME: '5',
  // 合作兼职
  COOPERATION_PART_TIME: '6',
  // 外包全职
  OUTSOURCING_FULL_TIME: '7',
  // 外包兼职
  OUTSOURCING_PART_TIME: '8',
}

export const ATTACHMENT_TYPE = {
  // 文件类型
  PHOTO: 0,
  FILE: 1,
}
