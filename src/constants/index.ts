// 时间常量
export const TIME_CONSTANTS = {
  YEAR: 365,
  THREE_MONTHS: 90,
  MONTH: 30,
  WEEK: 7,
  DAY: 1,
}

/* eslint-disable */
export const comRegexp = {
  mobile: /^[1][0-9]{10}$/, // 通讯商放宽号码段，所以前端也放宽手机号正则校验
  email: /^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/,
  // commonChar: /^([\u4e00-\u9fa5]|[a-zA-Z0-9]|[_#-]|[，。？！：；‘“”]|[,.?!:;'""])*$/g, // 汉字&英文&字母&标点符号
  zhChNumReg: /^[A-Za-z0-9\u2E80-\uFE4F]+$/,
  zhChNumSignReg:
    /^([\u2E80-\uFE4Fa-zA-Z0-9\s`～·~！!@@##$¥%%^……&&**（()）=+=+{【「{\\[\]}」】}｜、|;:；：'"‘“,<，《.>。》/?、？_——\\-])*$/,
  emojiReg:
    /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/,
  draftReg: /^draft_.*_.*$/, // 匹配草稿
}

export const isMicroApp = (function () {
  return !!window.__POWERED_BY_QIANKUN__
})()


// 是否为微前端环境
export const IS_MICROCONTEXT = !!window.__POWERED_BY_QIANKUN__

export const BASE_PATH = "/entry"

export const OTHER = '其他'

// 入职办理地简称类型
export const ENTRY_PLACE_NAME_TYPE = {
  IN_CONFIGURATION: 1,
  OTHER: 2
}

export const FOURTH_DEPARTMENT_CATEGORY = {
  COMPONY: 1,
  DEPARMENT: 4,
  REGION: 5,
  STORE: 7,
  MOBIEL_TEAMS: 8,
}

/**
 * httpError 调整为 bizError
 * 服务端返回的code不能识别该错误是常规业务错误或其他错误
*/
export const BIZ_ERRORS = [
  '当前状态无法进行此操作',
  '未知的请求错误',
  '请求已取消',
  '网络错误',
  '用户无权限访问该实体权限',
  '录用信息未发生变更',
  '审核失败，当前候选人不在可审核状态下',
  '候选人有更新内容',
  '人员已入职，无法查看入职材料详情',
  '当前入职材料信息正在处理，请稍后',
]
export const DICT_BM_YN = {
  YES: '1',
  NO: '0',
}

export const ID_CARD_TYPE = {
  ICARD: '02', // 临时身份证
  CHINA_IDCARD: '01', // 居民身份证
}

// 学历
export const EDU_TYPES = {
  PRIMARY_SCHOOL: '01', // 小学
  JUNIOR_SCHOOL: '02', // 初中
  SECONDARY_SCHOOL: '03', // 中专
  HIGH_SCHOOL: '04', // 高中
  JUNIOR_COLLEGE: '05', // 大专
  UNDERGRADUATE: '14', // 本科
  MASTER_DEGREE_CANDIDATE: '15', // 硕士研究生
  DOCTORAL_CANDIDATE: '16', // 博士研究生
}


// 系统配置类型
export const SYSTEM_CONFIG_TYPE: Record<string, 1 | 2 | 3 | 4 | 5> = {
  RULE: 1, // 规则配置
  BANK: 2, // 开户银行配置
  REVIEW_RULE: 3, // 复核规则配置
  ENTRY_HANDLE: 4, // 入职办理信息配置
  TIME_RULE: 5, // 时间规则配置
}

export const ALL_HEALTH_MATERIALS_TYPE = [
  { label: '个人照片', value: 1 },
  { label: '身份证', value: 2 },
  { label: '银行卡', value: 3 },
  { label: '毕业证书', value: 4 },
  { label: '学位证书', value: 5 },
  { label: '残疾证', value: 7 },
  { label: '退伍证', value: 9 },
  { label: '员工工牌', value: 10 },
  { label: '离职证明', value: 11 },
  { label: '体检报告', value: 12 },
  { label: '健康证', value: 13 },
  { label: '体检报告与健康证', value: 14 },
  { label: '其他健康类材料', value: 15 },
  { label: '临时身份证', value: 16 },
  { label: '压缩附件', value: 17 },
  { label: '哺乳期证明', value: 21 },
  { label: '谷丙转氨酶异常证明', value: 22 },
  { label: '健康证办理回执', value: 23 },
  { label: '体检报告和健康证办理回执', value: 25 },
  { label: '无', value: -1 },
]

//  O序列的健康证材料类型可选项
export const HEALTH_MATERIALS_TYPE_O = [
  { label: '健康证', value: 13 },
  { label: '哺乳期证明', value: 21 },
  { label: '谷丙转氨酶异常证明', value: 22 },
  { label: '健康证办理回执', value: 23 },
  { label: '无', value: -1 },
]

//  S序列的健康证材料类型可选项
export const HEALTH_MATERIALS_TYPE_S = [
  { label: '体检报告', value: 12 },
  { label: '健康证', value: 13 },
  { label: '体检报告与健康证', value: 14 },
  { label: '体检报告和健康证办理回执', value: 25 },
  { label: '健康证办理回执', value: 23 },
  { label: '无', value: -1 },
]

//  非O,S序列的健康证材料类型可选项
export const HEALTH_MATERIALS_TYPE_OTHER = [
  { label: '体检报告', value: 12 },
  { label: '体检报告与健康证', value: 14 },
]

// 政治面貌共青团员的code
export const MEMBER_DICT_VALUE = '04'
