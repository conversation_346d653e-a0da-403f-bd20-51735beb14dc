import { StateCreator } from 'zustand'
import { PageContainerStore, AuditBlockStore } from './types'

const createPageContainerStore: StateCreator<
  PageContainerStore & AuditBlockStore,
  [],
  [],
  PageContainerStore
> = (set) => ({
  extra: [],
  title: [],
  needBack: false,
  closely: false,
  setExtra: (extra: any = []) => {
    set({
      extra,
    })
  },
  setTitle: (title: any = []) => {
    set({
      title,
    })
  },
  setBack: (needBack: any = false) => {
    set({
      needBack,
    })
  },
  setClosely: (bool: boolean) => {
    set({
      closely: bool,
    })
  },
})

export default createPageContainerStore
