import { StateCreator } from 'zustand'
import { cloneDeep } from 'lodash-es'
import { EntryMaterialAuditInfo, EntryMaterialAuditRequired } from '@/types/entryMaterialsAudit'
import { AuditBlockStore, PageContainerStore } from './types'

const createAuditBlockStore: StateCreator<
  PageContainerStore & AuditBlockStore,
  [],
  [],
  AuditBlockStore
> = (set, get) => ({
  auditStatus: {} as EntryMaterialAuditInfo,
  _errorFields: {
    personPhoto: '',
    workCardPhoto: '',
    personBaseInfo: '',
    identityInfo: '',
    bankCardInfo: '',
    educationExperience: '',
    workExperiences: '',
    healthInfo: '',
    relationshipInfo: '',
    otherInfo: '',
  } as EntryMaterialAuditRequired,
  errorFields: {
    personPhoto: '',
    workCardPhoto: '',
    personBaseInfo: '',
    identityInfo: '',
    bankCardInfo: '',
    educationExperience: '',
    workExperiences: '',
    healthInfo: '',
    relationshipInfo: '',
    otherInfo: '',
  } as EntryMaterialAuditRequired,

  initAudit: (params: EntryMaterialAuditInfo) => {
    const copyParams = params
    Object.keys(params).forEach((key) => {
      if (params?.[key]?.noPassReasonDictItems) {
        copyParams[key].noPassReasonCodes = params[key].noPassReasonDictItems.map((item) => {
          const tmpPathCodes = item.pathCodes
          tmpPathCodes.shift()
          return tmpPathCodes
        })
      }
    })

    set({
      auditStatus: params,
    })
  },
  getAuditStatus: (key = null) => {
    if (key) {
      return get().auditStatus[key]
    }
    return get().auditStatus
  },
  setAuditStatus: (key, targetValue): void => {
    const copyAuditStatus = cloneDeep(get().auditStatus)
    copyAuditStatus[key] = targetValue
    set({
      auditStatus: copyAuditStatus,
    })
  },
  setErrorFields: (keys: string[]): void => {
    const copyErrorFields = cloneDeep(get()._errorFields)
    keys.forEach((key) => {
      copyErrorFields[key] = 'error'
    })
    set({
      errorFields: copyErrorFields,
    })
  },
})

export default createAuditBlockStore
