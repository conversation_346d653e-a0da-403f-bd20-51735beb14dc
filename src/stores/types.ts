import { EntryMaterialAuditInfo, EntryMaterialAuditRequired } from '@/types/entryMaterialsAudit'

export interface PageContainerStore {
  extra: any
  title: any
  needBack: boolean
  closely: boolean
  setExtra: (value: any) => void
  setTitle: (value: any) => void
  setBack: (value: boolean) => void
  setClosely: (value: boolean) => void
}

export interface AuditBlockStore {
  errorFields: EntryMaterialAuditRequired
  auditStatus: EntryMaterialAuditInfo
  _errorFields: EntryMaterialAuditRequired
  initAudit: (value: any) => any
  getAuditStatus: (value?: any) => any
  setAuditStatus: (key: any, value: any) => void
  setErrorFields: (key: any) => void
}
