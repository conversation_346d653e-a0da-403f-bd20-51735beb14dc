@import './global-variable.less';
// :global {

// :global {
html,
body {
  font-weight: var(--bodyFontWeight);
}
[class^='homePage'] {
  width: 1200px;
  margin: 0 auto;
}
// 根据DHR的标准，修改以下样式
.onboarding-input-number-group-addon {
  border: unset !important;
  background-color: transparent !important;
}
// }

// .onboarding-design-pro.onboarding-pro-basicLayout {
//   .onboarding-layout-sider {
//     &.onboarding-pro-sider {
//       background-color: @sideBgColor;
//     }
//     .onboarding-layout-sider-children {
//       // 顶部logo
//       .onboarding-pro-sider-logo {
//         color: @sideLogoFontColor;
//         .pupulogo {
//           color: @logColor;
//         }
//       }
//       // 菜单
//       .onboarding-menu {
//         &.onboarding-menu-dark {
//           background-color: @sideBgColor;
//         }
//         .onboarding-menu-item {
//           .onboarding-pro-menu-item {
//             .anticon {
//               font-size: @sideIconSize;
//               color: @sideFontColor;
//             }
//             .onboarding-pro-menu-item-title {
//               color: @sideFontColor;
//             }
//           }
//         }
//         .onboarding-menu-submenu {
//           &.onboarding-menu-submenu-active {
//             background-color: @sideActiveColor;
//           }
//           &.onboarding-menu-submenu-open {
//             border-right: @sideSelectedSubBorderWidth solid @sideSelectedSubBorderColor;
//           }
//           // &.onboarding-menu-submenu-selected {
//           // }
//           .onboarding-pro-menu-item {
//             .anticon {
//               font-size: @sideIconSize;
//               color: @sideFontColor;
//             }
//             .onboarding-pro-menu-item-title {
//               color: @sideFontColor;
//             }
//           }
//           .onboarding-menu-submenu-arrow {
//             &:before {
//               background: @sideFontColor;
//             }
//             &:after {
//               background: @sideFontColor;
//             }
//           }
//           &.onboarding-menu-submenu-open {
//             .onboarding-menu-submenu-arrow {
//               &:before {
//                 background: @sideFontColor;
//               }
//               &:after {
//                 background: @sideFontColor;
//               }
//             }
//           }
//           .onboarding-menu-sub {
//             background-color: @sideSubBgColor !important;
//           }
//         }
//       }
//     }
//   }
//   // a {
//   //   color: @primaryColor;
//   // }
//   .onboarding-switch {
//     &.onboarding-switch-checked {
//       .onboarding-switch-handle {
//         left: calc(57%);
//       }
//     }
//   }
// }
