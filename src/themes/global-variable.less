// SASS 全局变量
@import '~@galaxy/dhr-style/lib/dhr-theme-var.less';
@import '~@galaxy/dhr-style/lib/constant/var.less';

:root {
  --primaryColor: #1abb9c; //#5681f4; // #1890ff; // 主色调
  --sideBgColor: #2a3f54; //侧边栏背景色
  --sideSubBgColor: #304156; //侧边栏背景色
  --logColor: #32b50d;
  --sideLogoFontColor: #fff; //侧边栏背景色
  --sideIconSize: 18px;
  --sideActiveColor: transparent; //侧边栏激活颜色
  --sideSelectedColor: rgba(255, 255, 255, 0.05); //侧边栏选中颜色
  --sideFontColor: #c4cfda; //侧边栏选中颜色
  --sideActiveFontColor: #1890ff; //侧边栏选中颜色
  --sideSelectedSubBorderWidth: 5px; //菜单选中边框颜色
  --sideSelectedSubBorderColor: #1abb9c; //菜单选中边框颜色
  --sideSelectedBorderColor: transparent; //菜单选中边框颜色
  --sideSelectedFontColor: #fff; //侧边栏选中颜色
}
@primaryColor: var(--primaryColor); //#5681f4; // #1890ff; // 主色调
@bodyFontWeight: var(--bodyFontWeight); //#5681f4; // #1890ff; // 主色调
@sideBgColor: var(--sideBgColor); //用var来盛放变量名
@sideSubBgColor: var(--sideBgColor); //用var来盛放变量名
@logColor: var(--logColor); //顶部log颜色
@sideLogoFontColor: var(--sideLogoFontColor); //顶部log旁边的字体颜色
@sideIconSize: var(--sideIconSize); //侧边栏图标大小
@sideActiveColor: var(--sideActiveColor); //侧边栏激活颜色
@sideSelectedColor: var(--sideSelectedColor); //侧边栏选中颜色
@sideFontColor: var(--sideFontColor); //侧边栏字体颜色
@sideActiveFontColor: var(--sideActiveFontColor); //侧边栏字体激活颜色
@sideSelectedFontColor: var(--sideSelectedFontColor); //侧边栏选中颜色
@sideSelectedSubBorderWidth: var(
  --sideSelectedSubBorderWidth
); //侧边栏边选中时边框宽度
@sideSelectedSubBorderColor: var(
  --sideSelectedSubBorderColor
); //侧边栏边选中时边框颜色
@sideSelectedBorderColor: var(--sideSelectedBorderColor); //侧边栏边选中时颜色

