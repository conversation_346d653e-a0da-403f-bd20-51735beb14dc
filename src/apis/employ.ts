import http from '@/utils/request'

export type ModifyEmploymentInfoType = {
  departmentId?: number // 报到部门ID，是否虚拟岗位为『是』时必传
  employmentFormCode?: string // 用工形式，取值EHR码表（BM_35），是否虚拟岗位为『是』时必传，长度限制64字符
  employmentTypeCode?: string // 员工类型，取值EHR码表（BM_YGLX），是否虚拟岗位为『是』时必传，长度限制64字符
  entryCityCode: string // 入职办理地区，取值于EHR码表（BM_BZGSQY），长度限制64字符
  positionId?: number //  报到实体岗位ID，是否虚拟岗位为『是』时必传
  postRankCode?: string // 职级，取值于EHR码表（BM_ZJ），是否虚拟岗位为『是』时必传，长度限制64字符
  timeEntry: number // 预计报到日期，时间戳
  trialPeriod?: string // 试用期期限，仅输入0-6的整数，是否虚拟岗位为『是』时必传
}

export type GetEntryPlaceType = {
  entryAreaCode?: string // 入职办理地区, 码值 BM_BZGSQY
  isStore?: number // 是否是门店
  entryPlaceName?: string
  entryPlaceAddress?: string
}

// 【v1.3】修改录用信息
export const modifyEmploymentInfo = (
  entryMaterialId: number,
  data: ModifyEmploymentInfoType,
  config?,
): Promise<{ code: number; data: boolean; msg: string }> => {
  return http.patch(`/admin/entry/v1/entry_materials/${entryMaterialId}/employment`, data, config)
}

// 【v1.5】查询入职办理地
export const getEntryPlace = (
  data: GetEntryPlaceType,
  config?,
): Promise<{ code: number; data: any; msg: string }> => {
  return http.get(`/admin/entry/v1/entry_place`, data, config)
}
