import http from '@/utils/request'
import { PageResponse } from '@/types/common'

export type GetTemplateListParams = {
  /** 模板名称，长度限制40字符 */
  name?: string
  /** 模板类型，取值于码表（OMS_TEMPLATE_TYPE） */
  typeCode?: string
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  /** 排序，选填，格式:http://ip:port?sort=id,asc&sort=user_name,desc */
  sort?: string
}

export type TemplateData = {
  /** 正在使用的配置ID */
  configureId: number
  /** 正在使用的配置名称 */
  configureName: string
  /** 模板ID */
  id: number
  /** 模板是否正在被使用 */
  isUsing: boolean
  /** 短信模板 */
  msgTemplate: string
  /** 模板名称 */
  name: string
  /** 创建时间 */
  timeCreate?: number
  /** 模板类型-取值于码表（OMS_TEMPLATE_TYPE） */
  typeCode: string
}

/** 5001:获取模板列表 */
export const getTemplateList = (
  params: GetTemplateListParams,
  options?: any,
): Promise<PageResponse<TemplateData[]>> => http.get('/admin/entry/v1/templates', params, options)

export type GetTemplateParams = {
  /** 模板ID */
  id: number
}
/** 5002:获取模板详情 */
export const getTemplate = (parmas: GetTemplateParams, options?: any): Promise<TemplateData> =>
  http.get(`/admin/entry/v1/templates/${parmas.id}`, undefined, options).then((res) => res.data)

export type AddTemplateParams = {
  /** 短信模板 */
  msgTemplate: string
  /** 模板名称 */
  name: string
  /** 模板类型-取值于码表（OMS_TEMPLATE_TYPE） */
  typeCode: string
}

/** 5003:新建模板 */
export const addTemplate = (data: AddTemplateParams, options?: any): Promise<TemplateData> =>
  http.post('/admin/entry/v1/templates', data, options)

export type PutTemplateParams = AddTemplateParams & {
  /** 模板ID */
  id: number
}
/** 5004:编辑模板 */
export const putTemplate = (
  { id, ...data }: PutTemplateParams,
  options?: any,
): Promise<TemplateData> =>
  http.put(`/admin/entry/v1/templates/${id}`, data, options).then((res) => res.data)

export type DeleteTemplateParams = {
  /** 模板ID */
  id: number
}
/** 5005:删除模板 */
export const deleteTemplate = (params: DeleteTemplateParams, options?: any): Promise<boolean> =>
  http.delete(`/admin/entry/v1/templates/${params.id}`, undefined, options).then((res) => res.data)
