import http from '@/utils/request'

export type GetDminEntryV10AsyncTaskByIdParams = {
  /** id */
  id: number
}

export type AsyncTaskVO = {
  /** bizModule */
  bizModule?: string
  /** bizType */
  bizType?: number
  /** countOk */
  countOk?: number
  /** countTotal */
  countTotal?: number
  /** errorMessage */
  errorMessage?: string
  /** id */
  id?: number
  /** objectKey */
  objectKey?: string
  /** percentage */
  percentage?: number
  /** stage */
  stage?: number
  /** status */
  status: number
  /** timeCreate */
  timeCreate?: number
  /** timeUpdate */
  timeUpdate?: number
}

/** get */
export const getDminEntryV10AsyncTaskById = (
  { id }: GetDminEntryV10AsyncTaskByIdParams,
  options?: any,
): Promise<AsyncTaskVO> =>
  http.get(`/admin/entry/v1.0/async_task/${id}`, undefined, options).then((res) => res.data)
