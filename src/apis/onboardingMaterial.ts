import { PageResponse } from '@/types/common'
import request from '@/utils/request'

// swagger http://dhr-entry-svc.k8s.dhr.pre.pupufz.com/doc.html#/

// 1001:获取入职材料列表
export type GetMaterialListParams = {
  areaCode?: string // 所属区域，取值于EHR码表（BM_BZGSQY）
  completeSituation?: 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
  departmentId?: number // 部门ID
  ehrCandidateId?: number // EHR人才ID
  ehrProgress?: number // EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功
  employmentFormCodes?: string // 用工形式，取值EHR码表（BM_35）
  enterEntryTimeBegin?: number // 进入预入职时间范围-开始时间
  enterEntryTimeEnd?: number // 进入预入职时间范围-结束时间
  name?: string // 姓名
  positionCandidateId?: number // DHR候选人ID
  positionId?: number // 实体岗位ID
  positionName?: string // 实体岗位名称
  processProgresses?: string // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘
  recruitResponsibleUserNums?: string // 招聘负责人工号
  // status?: number // 0-进行中 1-已完成
  timeEntryBegin?: number // 预计报到开始时间
  timeEntryEnd?: number // 预计报到结束时间
  timeoutStatuses?: string // 超时状态 0-未超时 1-即将超时 2-已超时
  page?: number
  size?: number // 分页数量，最大200,示例值(20)
}

export interface MaterialData {
  age: number // 年龄
  areaCode: string // 所属区域，取值于EHR码表（BM_BZGSQY）
  auditNoPassIsPushResult: number // 审核不通过时是否已下发推送审核结果
  auditResult: 0 | 1 | 2 | 3 // 审核结果 0-无 1-审核通过 2-审核不通过 3-驳回修改
  bankCardTypeCode: string // 银行卡类型，取值于EHR码表（BM_Banktype）
  branchBankInfo: string // 支行信息
  completeSituation: 0 | 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
  contractEntityCode: string // 合同主体，取值EHR码表（BM_HTZT）
  currentAddress: string // 现居住地
  currentAddressName: string // 现居地址名称，用于前端懒加载使用
  currentAreaCode: string // 现居区，取值于全局字典（REGION_CODE）
  currentCityCode: string // 现居市，取值于全局字典（REGION_CODE）
  currentProvinceCode: string // 现居省，取值于全局字典（REGION_CODE）
  currentStreetCode: string // 现居街道，取值于全局字典（REGION_CODE）
  degreeCode: string // 学位，取值EHR码表（字典：BM_XW）
  departmentId: number // 报到部门ID
  departmentFullName: string // 部门全路径
  departmentIdPath: string // 部门全路径
  departmentName: string // 报到部门名称
  educationCode: string // 学历，取值EHR码表（字典：BM_XL）
  ehrCandidateId: number // EHR人才ID
  ehrProgress: 0 | 1 | 2 | 3 | 4 // EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功
  emergencyContactName: string // 紧急联系人姓名
  emergencyContactPhone: string // 紧急联系人电话
  employmentFormCode: string // 用工形式，取值EHR码表（BM_35）
  employmentStateCode: string // 人员类别，取值 EHR码表（BM_KK）
  employmentTypeCode: string // 员工类型，取值EHR码表（BM_YGLX）
  endRecruitReasonCode: string // 结束招聘原因，取值于入职字典（OMS_END_RECRUIT_REASON）
  endRecruitReasonRemark: string // 结束招聘原因说明
  graduateSchool: string // 毕业院校
  id: number // 入职材料ID
  idCardNum: string // 身份证号码
  isDisabledCode: string // 是否残障人士，取值于EHR码表（BM_YN）
  isEntry: 0 | 1 // 是否已入职 1-是 0-否
  isHealthCertificateCode: string // 是否有健康证，取值于EHR码表（BM_YN）
  isNeedAudit: 0 | 1 // 是否需要审核 1-是 0-否
  isNeedReview: 0 | 1 // 是否复核 1-是 0-否
  isVeteranCode: string // 是否退伍军人，取值于EHR码表（BM_YN）
  jobDuration: number // 作业净时长 按小时显示 例12.4h
  major: string // 专业
  memberRelationCode: string // 与本人关系，取值于EHR码表（BM_GX）
  memberRelationRemark?: string // 【v2.0.0】与本人关系备注
  name: string // 姓名
  noPassReasonCodes: string[] // 审核不通过原因列表，取值于入职字典（OMS_INFORMATION_FAILED_REASON）
  offerTemplateCode: string // offer适用模板，取值EHR码表（BM_69）
  positionBaseId: number // 报到基准岗位ID
  positionCandidateId: number // DHR候选人ID
  positionId: number // 报到实体岗位ID
  positionName: string // 报到实体岗位名称
  positionTypeCode: string // 岗位类型，取值于EHR码表（BM_Recruitment）
  postLevelCode: string // 职位，取值于EHR码表（BM_ZWDJ）
  postRankCode: string // 职级，取值于EHR码表（BM_ZJ）
  processProgress: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘
  recentAuditPerson: {
    // 最近审核人信息
    userName: string
    userNum: string
  }
  recentReviewPerson: {
    // 最近复核人
    userName: string
    userNum: string
  }
  recruitResponsiblePerson: {
    // 面试安排人/招聘负责人信息
    userName: string
    userNum: string
  }
  rejectReasonCodes: string[] // 复核驳回原因列表，取值于入职字典（OMS_INFORMATION_FAILED_REASON）
  rejectReason: string // 复核驳回原因说明
  residenceAddress: string // 身份证地址
  resumeStatusCode: string // 简历状态，取值于EHR码表（BM_ZPResumeStatus）
  spentTotalTime: number // 耗费总时长 按小时显示 例12.4h
  status: 0 | 1 // 状态 0-进行中 1-已完成
  studyModeCode: string // 学习形式，取值于EHR码表（BM_studymode）
  successStatusType: 1 | 2 | 3 | 4 | 5 //	已完成的数据类型 1-审核通过的数据 2-审核未通过但是特批通过成功通知到EHR的数据 3-审核未通过且成功提交结果到EHR的数据 4-手动结束招聘并成功通知结果到EHR的数据 5-处理超时自动结束招聘并成功通知结果到EHR的数据
  timeAuditComplete: number // 审核完成时间，处理进度最后一次变为审核通过/审核不通过/结束招聘的时间
  timeEhrReceive: number // EHR接收成功时间
  timeEntry: number // 入职时间/预计报到日期
  timeGraduate: number // 毕业日期
  timeHealthEnd: number // 健康证到期日期
  timeIdCardEnd: number // 身份证有效到期日期
  timeIdCardStart: number // 身份证有效开始日期
  timeRecentSubmit: number // 最近提交时间/最近一次自主入职小程序的提交时间
  timeoutStatus: 0 | 1 | 2 // 超时状态 0-未超时 1-即将超时 2-已超时
  trialPeriodCode: string // 试用期期限，取值于EHR码表（BM_63）
  workPlaceCode: string // 现工作地，取值于EHR码表（BM_73）
  isCanNoticeModifyEntryMaterial: boolean
  contactPhone: string // 联系电话
  recruitTypeCode: string // 招聘类型，取值于DHR码表（RECRUIT_TYPE）
  timeCreate: number // 进入预入职日期，时间戳
  positionCandidateStatus: string // 候选人状态
  dataSource: 'ehr' | 'employment' // 数据来源，通过ehr确定该条数据是试运行外
  isPositionCandidateNewestEntryMaterial: boolean // 是否该预入职条目是同候选人id的最新条目
  isVirtualPosition: 0 | 1 // 【V1.3】是否虚拟岗位 1-是 0-否 默认-1
  entryCityCode: string // 【V1.3】入职办理地区，取值于EHR码表（BM_BZGSQY）
  cooperateCompanyCode: string //
  entryPlaceName?: string // 【v1.5】入职办理地点
  entryPlaceNameType?: number // 【v1.5】入职办理地简称
  entryPlaceAddress?: string // 【v1.5】入职办理地简称类型
  fourthDepartmentName?: string // 【v1.5】报到部门链路上四级部门机构名称
  fourthDepartmentCategory?: number // 【v1.5】报到部门链路上四级部门机构类别
  entryInvestigationResult: number // 背调状态
  entryMaterialProcessTrackInfo?: string // 过程跟踪记录
  isPpTrainee?: number // 是否朴培生
}

//  1001:获取入职材料列表-进行中
export const getInProcessingMaterialList = (
  params: GetMaterialListParams,
): Promise<{ count: number; data: MaterialData[] }> =>
  request.get('/admin/entry/v1/entry_materials/processing', params, {
    decrypt: ['data[*].contactPhone'],
  })

//  1008:获取入职材料列表-已完成
export const getCompletedMaterialList = (
  params: GetMaterialListParams,
): Promise<{ count: number; data: MaterialData[] }> =>
  request.get('/admin/entry/v1/entry_materials/completed', params, {
    decrypt: ['data[*].contactPhone'],
  })

type ResendSMSParamsType = {
  /** 入职材料ID */
  id: number
}
/** 1005:通知填写入职材料 */
export const resendSMS = (params: ResendSMSParamsType, options?: any): Promise<boolean> =>
  request
    .post(`/admin/entry/v1/entry_materials/${params.id}/message/entry`, {}, options)
    .then((res) => res.data)

type ResendModifySMSParamsType = {
  /** 入职材料ID */
  id: number
  /** 信息修改模板ID */
  templateId: number
  /** 完成日期 时间戳 */
  timeComplete: number
}
/** 1006:通知修改入职材料 */
export const resendModifySMS = (
  params: ResendModifySMSParamsType,
  options?: any,
): Promise<boolean> =>
  request
    .post(`/admin/entry/v1/entry_materials/${params.id}/message/modify`, params, options)
    .then((res) => res.data)

type EndRecruitParams = {
  id: number
  endRecruitCode: string
  isNoticeEhr: boolean
  remark?: string
}

// 1007:结束招聘
export const endRecruit = ({ id, ...data }: EndRecruitParams): Promise<boolean> => {
  return request.post(`/admin/entry/v1/entry_materials/${id}/end`, data).then((res) => res.data)
}

export type LogVOObject = {
  /** 操作内容 */
  content?: string
  /** 操作时间 */
  operateTime?: number
  /** 操作人工号/手机号 */
  operator?: string
  /** 操作人姓名 */
  operatorName?: string
}

export type GetOperationLogListParams = {
  /** 入职材料ID */
  id: number
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  /** 排序，选填，格式:http://ip:port?sort=id,asc&sort=user_name,desc */
  sort?: string
}
/** 1001:获取入职材料的操作记录列表 */
export const getOperationLogList = (
  { id, ...data }: GetOperationLogListParams,
  options?: any,
): Promise<PageResponse<LogVOObject[]>> =>
  request.get(`/admin/entry/v1/entry_materials/${id}/logs`, data, options)

type EndReviewParams = {
  id: number
  endRecruitCode: string
  isAllowReSendEmployment: boolean
  remark?: string
}

// 1010:【v1.3】结束审核
export const endReview = ({ id, ...data }: EndReviewParams): Promise<boolean> => {
  return request
    .post(`/admin/entry/v1/entry_materials/${id}/audit/end`, data)
    .then((res) => res.data)
}
