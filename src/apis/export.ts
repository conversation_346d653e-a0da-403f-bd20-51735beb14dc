import http from '@/utils/request'

export type EntryMaterialExportRoObject = {
  /** 所属区域，取值于EHR码表（BM_BZGSQY） */
  areaCode?: string
  /** 完成情况 0-无 1-按时完成 2-超时完成 */
  completeSituation?: number
  /** 部门ID */
  departmentId?: number
  /** EHR人才ID */
  ehrCandidateId?: number
  /** EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功 5-待代缴流程归档 */
  ehrProgress?: number
  /** 用工形式列表，取值EHR码表（BM_35） */
  employmentFormCodes?: Array<string>
  /** 进入预入职时间范围-开始时间 */
  enterEntryTimeBegin: number
  /** 进入预入职时间范围-结束时间 */
  enterEntryTimeEnd: number
  /** 姓名 */
  name?: string
  /** DHR候选人ID */
  positionCandidateId?: number
  /** 实体岗位ID */
  positionId?: number
  /** 实体岗位名称 */
  positionName?: string
  /** 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘 */
  processProgresses?: Array<number>
  /** 招聘负责人工号 */
  recruitResponsibleUserNum?: string
  /** 状态 0-进行中 1-已完成 */
  status?: number
  /** 预计报到开始时间 */
  timeEntryBegin?: number
  /** 预计报到结束时间 */
  timeEntryEnd?: number
  /** 超时状态 0-未超时 1-即将超时 2-已超时 */
  timeoutStatuses?: Array<number>
}

/** 3002:导出入职材料时间明细信息（仅展示入参使用） */
export const postDminEntryV1EntryMaterialsTimeStatisticsExport = (
  data: EntryMaterialExportRoObject,
  options?: any,
): Promise<number> =>
  http
    .post('/admin/entry/v1/entry_materials/time_statistics_export', data, options)
    .then((res) => res.data)

/** 3001:导出入职材料（仅展示入参使用） */
export const postDminEntryV1EntryMaterialsExport = (
  data: EntryMaterialExportRoObject,
  options?: any,
): Promise<number> =>
  http.post('/admin/entry/v1/entry_materials/export', data, options).then((res) => res.data)
