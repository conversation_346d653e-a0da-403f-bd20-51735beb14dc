import http from '@/utils/request'

export type PushResultParams = {
  id: number
  auditResult: 1 | 2
  remark?: string
  screenshots?: {
    attachmentType: 0 | 1
    fileName?: string
    sideType?: number
    size?: number
    url: string
  }[]
}
/** 2004:推送审核结果 */
export const pushResult = (
  { id, ...data }: PushResultParams,
  options?: any,
): Promise<{ isSuccess: boolean; result: 1 | 2 }> =>
  http
    .post(`/admin/entry/v1/entry_materials/${id}/audit/push`, data, options)
    .then((res) => res.data)

export type BatchReviewRoObject = {
  reviews: {
    entryMaterialId: number
    noPassReasonCodes?: string[]
    rejectReason?: string
    reviewResult: 1 | 2
  }[]
}

/** 2003:批量复核，仅允许全部通过/全部不通过 */
export const batchReview = (
  data: BatchReviewRoObject,
  options?: any,
): Promise<{ code: string; data: boolean; message: string }[]> =>
  http.post('/admin/entry/v1/entry_materials/review/batch', data, options).then((res) => res.data)

export type PostDminEntryV1EntryMaterialsAuditRejectByEntryMaterialIdParams = {
  /** 入职材料ID */
  entryMaterialId: number
  /** 入职材料版本号 */
  version: number
}
/** 2005:驳回审核结果 */
export const rejectResult = (id: number, options?: any): Promise<boolean> =>
  http.post(`/admin/entry/v1/entry_materials/${id}/audit/reject`, {}, options)

export type PostDminEntryV1EntryMaterialsReviewByEntryMaterialIdParams = {
  /** 入职材料ID */
  entryMaterialId: number
}

export type ReviewRoObject = {
  id: number
  /** 审核不通过原因列表，取值于码表（OMS_INFORMATION_FAILED_REASON） */
  noPassReasonCodes?: string
  /** 驳回说明，最多100个字符 */
  rejectReason?: string
  /** 复核结果 1-认可审核结果 2-驳回审核结果 */
  reviewResult: 1 | 2
}

/** 2003:复核 */
export const review = (params: ReviewRoObject, options?: any): Promise<boolean> => {
  const { id, ...restParams } = params
  return http
    .post(`/admin/entry/v1/entry_materials/${id}/review`, restParams, options)
    .then((res) => res.data)
}
