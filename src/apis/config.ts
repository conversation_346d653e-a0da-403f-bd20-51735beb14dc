import http from '@/utils/request'

export type ConfigData = {
  /** 详见：https://doc.weixin.qq.com/doc/w3_AA4A4wZmACU9rp1rCOpQb0FdQNVvP?scode=AJ0ARQd1AAw0219N01AA4A4wZmACU */
  code: string
  /** 配置ID */
  id: number
  /** 具体配置名称 */
  name: string
  /** 具体配置名称 */
  orderNum: number
  /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置 */
  type: 1 | 2 | 3
  /** 值 */
  value: string
}

export type GetConfigParams = {
  /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置 */
  type?: number
}
/** 4001:获取配置 */
export const getConfig = (data: GetConfigParams, options?: any): Promise<ConfigData[]> =>
  http.get('/admin/entry/v1/configures', data, options).then((res) => res.data)

export type EntryConfigureRoObject = {
  /** 详见：https://doc.weixin.qq.com/doc/w3_AA4A4wZmACU9rp1rCOpQb0FdQNVvP?scode=AJ0ARQd1AAw0219N01AA4A4wZmACU */
  code: string
  /** 具体配置名称 */
  name?: string
  /** 排序字段 */
  orderNum?: number
  /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置 */
  type: number
  /** 值 */
  value?: string
}

/** 4002:编辑配置 */
export const updateConfig = (
  data: Array<EntryConfigureRoObject>,
  options?: any,
): Promise<ConfigData[]> =>
  http.post('/admin/entry/v1/configures', data, options).then((res) => res.data)
