import request from '@/utils/request'

export type GetDminEntryV1LogsConfigureParams = {
  /** 操作人姓名/工号 */
  operatorUserNumOrName?: string
  /** 操作开始时间 */
  timeOperateBegin?: number
  /** 操作结束时间 */
  timeOperateEnd?: number
  /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置 */
  types?: string
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  /** 排序，选填，格式:http://ip:port?sort=id,asc&sort=user_name,desc */
  sort?: string
}

export type LogVOObject = {
  /** 操作内容 */
  content?: string
  /** 模板ID */
  id?: number
  /** 操作时间 */
  operateTime?: number
  /** 操作人 */
  operator?: string
  /** 操作人工号 */
  operatorName?: string
  /** 配置类型 */
  type?: 1 | 2 | 3
}

export const getDminEntryV1LogsConfigure = (
  data: GetDminEntryV1LogsConfigureParams,
): Promise<{ data: LogVOObject[]; count: number }> =>
  request.get('/admin/entry/v1/logs/configure', data)
