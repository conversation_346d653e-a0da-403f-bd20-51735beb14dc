/**
 * @name 材料审核模块 API
 */

import services from '@/utils/request'
import { ResponseData } from '@/types/common'
import config from '@/config'
import {
  EntryMaterialDetail,
  EntryMaterialAuditInfo,
  EntryMaterialSubmit,
} from '@/types/entryMaterialsAudit'

const mockHttp = ''
const dictHttp = config.environment === 'prod' ? '' : 'https://dhrapi.pre.pupufz.com'

// 获取入职材料详情
export const getEntryMaterialDetail = (
  entryMaterialId: number,
  headerConfig?: Record<string, any>,
): Promise<ResponseData<EntryMaterialDetail>> =>
  services.get(`${mockHttp}/admin/entry/v1/entry_materials/${entryMaterialId}`, {}, headerConfig)

// 获取入职材料审核信息
export const getEntryMaterialAuditInfo = (
  entryMaterialId: number,
): Promise<ResponseData<EntryMaterialAuditInfo>> =>
  services.get(`${mockHttp}/admin/entry/v1/entry_materials/${entryMaterialId}/audit`)

// 提交入职材料和入职材料审核信息
export const submitEntryMaterialAuditInfo = (params: {
  entryMaterialId: number
  version: number | string
  params: EntryMaterialSubmit
}): Promise<ResponseData<EntryMaterialDetail>> =>
  services.post(
    `${mockHttp}/admin/entry/v1/entry_materials/${params.entryMaterialId}/audit?version=${params.version}`,
    {
      ...params.params,
    },
    {
      noMessage: true,
    },
  )

// 获取正在审核该入职材料的人员名单列表
export const getAuditingPersonList = (params: {
  entryMaterialId: number
}): Promise<ResponseData<any>> =>
  services.post(
    `${mockHttp}/admin/entry/v1/entry_materials/${params.entryMaterialId}/audit/person`,
    {
      params,
    },
    {
      noMessage: true,
    },
  )

// 获取STS临时授权
export const getStsToken = async (params) => services.get('/admin/entry/v1.1/cs/sts_token', params)

/**
 * 字典，异步查询
 * TODO 字典服务暂不支持，后期可替换成字典服务
 */
export const getAsyncDict = async (
  typeCodes: string,
  scope: string,
  {
    parentId,
    codes,
    itemLevelLt,
  }: { parentId?: number; codes?: string[] | number[]; itemLevelLt?: number },
): Promise<Record<string, any>> =>
  services.post(
    `${dictHttp}/open/basic/v1/dicts/query?is_no_page=true&page=1&size=200`,
    {
      codes,
      dictTypeCodes: [typeCodes],
      parentIds: Number.isFinite(parentId) ? [parentId] : [],
      itemLevelLt,
    },
    {
      headers: {
        ClientCode: scope,
      },
    },
  )
