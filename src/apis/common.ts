// 项目公共基础能力
import request from '@/utils/request'

type PersonnelStandardData = {
  basePositionCode: string
  departmentName: string
  id: number
  postLevelCode: string
  probationPeriod: number
}

type EhrDictArea = {
  code: string
  id: number
  name: string
  parentId: number
}

export type GetPositionListDataType = {
  beginId?: number // 实体岗位起始id
  ids?: number[] // 实体岗位id（精准匹配），集合范围1-100
  isDeleted?: boolean // 是否作废（false-未作废，true-已作废）
  isEnabled?: boolean // 是否启用（忽略，暂无使用）
  nameLike?: string // 实体岗位名称，模糊搜索
  nodeIds?: number[] // 节点id集合，集合范围1-100
  parentIds?: number[] // 实体岗位父节点id集合，集合范围1-50
  postBaseCodes?: string[] // 基准岗位编码集合，集合范围1-50
  postBaseIds?: number[] // 基准岗位id集合，集合范围1-50
  page: number
  size: number
}

type PostRecordType = {
  bizKey: String
  bizParam: Record<string, any>
  content: String
  type: number
}

/**
 * 4:【v1.3】查询人员试用期标准表
 * @param positionId 实体岗位id
 * @returns
 */
export const getPersonnelStandard = (positionId?: number): Promise<PersonnelStandardData> => {
  return request.get('/admin/entry/v1/personnel_standard', {
    positionId,
  })
}

/**
 * 查询EHR编制区域码表信息（BM_BZGSQY）
 * @returns
 */
export const getEhrDictArea = (): Promise<{ code: number; data: EhrDictArea[]; msg: string }> => {
  return request.get('/admin/entry/v1/ehr_dict/area')
}

// 10000403:根据条件查询实体岗位信息列表
export const getPositionList = (
  data: GetPositionListDataType,
): Promise<{ count: number; data: any; code: number }> => {
  return request.get(`/admin/entry/v1/position`, data)
}

// 【v1.7】保存通用记录
export const postRecord = (
  data: PostRecordType,
): Promise<{ msg: string; data: boolean; code: number }> => {
  return request.post(`/admin/entry/v1/record`, data)
}
