import React, { useEffect, useRef } from 'react'
import userService from '@/services/user'
import { useLocation } from 'react-router-dom'
import dmsService from '@/services/dms'

const dmsWrapper = (WrappedComponent) => {
  return (props) => {
    useEffect(() => {
      if (userService.info) {
        dmsService.login((userService.info.num || userService.info.userName)!)
      }
    }, [])

    // 如果是刷新、第一次进入页面，不触发savePrevPageContext
    const isFirst = useRef<boolean>(true)
    const location = useLocation()

    useEffect(() => {
      if (isFirst.current) {
        isFirst.current = false
        return
      }
      dmsService.setPrevPageContext()
    }, [location.pathname])

    return <WrappedComponent {...props} />
  }
}

export default dmsWrapper
