// 入职材料审核

interface TreeDictItemPathVO {
  pathCodes: string[]
}
export interface EntryMaterialAudit {
  itemAuditResult: number
  noPassReason?: string
  noPassReasonCodes?: string[] // 前端保存及提交的数据
  noPassReasonDictItems?: TreeDictItemPathVO[] // 只在获取的时候返回
}

export type SpecialApprovalInfoType = {
  remark: string
  screenshots: CertificateInfo[]
}

// 获取入职材料审核信息
export interface EntryMaterialAuditInfo {
  bankCardInfo: EntryMaterialAudit // 银行卡信息
  educationExperience: EntryMaterialAudit // 教育经历
  healthInfo: EntryMaterialAudit // 健康信息
  identityInfo: EntryMaterialAudit // 身份信息
  otherInfo: EntryMaterialAudit // 其他信息
  personBaseInfo: EntryMaterialAudit // 个人基本信息
  personPhoto: EntryMaterialAudit // 个人照片
  relationshipInfo: EntryMaterialAudit // 家庭成员信息
  resignationCertificate: EntryMaterialAudit // 离职证明
  workCardPhoto: EntryMaterialAudit // 工牌照片
  specialApprovalInfo: SpecialApprovalInfoType // 特批通过材料
}

// 教育经历类型
type EducationExperience = {
  degreeCode: string // 学位（BM_XW）
  educationCode: string // 学历（BM_XL）
  graduateSchool: string // 毕业院校
  major: string // 专业
  studyModeCode: string // 学习方式（BM_STUDYMODE）
  timeGraduate: number // 毕业时间
  // 2.1新增字段
  isDualDegree: number // 是否双学位
  secondMajor: string // 第二专业
  timeEnrollment: number // 开始日期
  timeExpectedGraduate: number // 预计毕业日期
  timeGraduateIsNow: number // 毕业日期是否为至今 1-是 0-否 默认0
  timeCreate?: number // 数据插入数据库的时间
}

// 工作经历类型
type WorkExperience = {
  companyName: string // 企业名称
  departmentName: string // 所在部门名称
  enterpriseScale: string // 企业规模
  entryMaterialId: number // 入职材料ID
  id: number // 主键ID
  jobDescription: string // 所从事的工作内容
  positionName: string // 所担任职位
  rank: string // 职级
  referenceName: string // 证明人姓名
  referenceTelephone: string // 证明人电话
  resignationReason: string // 离职原因
  timeBegin: number // 开始日期
  timeEnd: number // 结束日期
  timeEndIsNow: number // 结束日期是否至今
  timeEndWork: number // 最后工作结束日期
  recruitWorkExperienceId: number // 招聘工作经历主键ID
}

// 入职材料入职信息
type EntryInfo = {
  disabilityCertificateRankCode?: string // 残疾证等级（BM_CJRZDJ）
  disabilityCertificateTypeCode?: string // 残疾证类型（BM_CZZM）
  disabilityDegreeCode?: string // 残疾等级（BM_CZDJ）
  isDisabledCode: string // 是否残疾人士（BM_YN）
  isHealthCertificateCode: string // 是否有健康证（BM_YN）
  isVeteranCode: string // 是否退役军人（BM_YN）
  timeHealthEnd: number // 健康证到期时间
}

// 身份证信息
type IdentityInfo = {
  currentAddress: string // 现居住址
  currentAddressName: string // 现居住地名称
  currentAreaCode: string // 现居区，取值于全局字典（REGION_CODE）
  currentCityCode: string // 现居市，取值于全局字典（REGION_CODE）
  currentProvinceCode: string // 现居省，取值于全局字典（REGION_CODE）
  currentStreetCode: string // 现居街道，取值于全局字典（REGION_CODE）
  houseRegisterTypeCode: number | string // 户口性质（BM_HP）
  idCardIssuingAuthority: string // 签发机关
  politicalStatusCode: string // 政治面貌（BM_AT）
  residenceAddress: string // 身份证地址
  timeIdCardEnd: number // 身份证到期时间
  timeIdCardStart: number // 身份证开始时间
}

// 证件信息
type CertificateInfo = {
  attachmentType: number
  entryMaterialId: number
  fileName: string
  fileType: number
  id: number
  previewId: string
  sideType: number
  size: number
  url: string
}

type BankCardInfo = {
  bankAccount: string
  bankPlaceCityCode: string
  bankPlaceProvinceCode: string
  branchBankInfo: string
}
// 获取入职材料详情

export type EducationExperienceItem = EducationExperience & {
  diplomas: CertificateInfo[] // 学历证书附件
  entryMaterialId: number // 入职材料id
  graduationCertificates: CertificateInfo[] // 毕业证书附件
  educationReports: EntryOtherAttachments[] // 学历验证报告
  id: number // id
}

export interface EntryMaterialDetail {
  bankCardInfo: {
    // 银行卡信息
    bankAccount: string // 银行账号
    bankCardNoNumPhoto: CertificateInfo // 银行卡非卡号面照
    bankCardNumPhoto: CertificateInfo // 银行卡卡号面照
    bankCardTypeCode: string // 银行卡类型 （BM_Banktype）
    // === 调整处 bankPlaceCode: string // 开户行所在地 (OMS_OPEN_ADDRESS)
    bankPlaceCityCode: string // 开户地-市，取值于EHR码表（OMS_OPEN_ADDRESS）
    bankPlaceProvinceCode: string // 开户地-省，取值于EHR码表（OMS_OPEN_ADDRESS）
    branchBankInfo: string // 支行信息
    entryMaterialId: number // 入职材料id
    id: number // id
    payee: string // 收款人
    savingsCardInfo: string // 储蓄卡信息
  }
  // 入职材料入职信息
  entryInfo: EntryInfo & {
    dataSource: string
    otherAttachments: EntryOtherAttachments[]
    areaCode: string // 区域 (BM_BZGSQY)
    companyAccumulationFund: string // 原公司公积金账号
    contractEntityCode: string // 合同主体，取值EHR码表（BM_HTZT）
    contractPeriod: string // 合同期限
    contractTypeCode: number // 合同类型，取值于EHR码表（BM_VAA）
    departmentId: number // 报道部门id
    departmentIdPath: string // 部门全路径
    departmentName: string // 报道部门名称
    disabilityCertificatePhotos: CertificateInfo[] // 残疾证附件
    ehrCandidateId: number // Ehr人才id
    emergencyContactName: string // 紧急联系人姓名
    emergencyContactPhone: string // 紧急联系人电话
    employmentFormCode: string // 用工形式 （BM_35）
    employmentStateCode: string // 人员类别（BM_KK）
    employmentTypeCode: string // 员工类型（BM_YGLX）
    examinationReport: string // 体检报告
    healthMaterialsAccessories: CertificateInfo[] // 健康材料附件
    healthMaterialsType: string // 健康材料类型
    id: number // 入职材料id
    insuranceAscription: string // 五险一金归属公司
    // interviewConfirmationId: number // 面试确认id
    isExistInterestConflictCode: number // 是否存在利益冲突 （BM_YN）
    isNeedBadgeCode: string // 是否需要工牌 （BM_YN）
    // isPayInsuranceCode: number // 是否缴纳农保或居民社保 （BM_YN）
    isOsCode: string // 是否OS序列，取值于EHR码表（BM_YN）
    isTechDepartment: number // 是否技术部门 （BM_YN）
    mainSequenceCode: string // 主序列，取值于EHR码表（BM_Rank）
    memberRelationCode: string // 与本人关系（BM_GX）
    memberRelationRemark: string
    offerTemplate: string // offer模板
    positionBaseId: number // 报到基准岗位id
    positionCandidateId: number // DHR候选人id
    positionId: number // 报到实体岗位id
    positionName: string // 报到实体岗位名称
    positionTypeCode: string // 岗位类型（BM_Recruitment）
    postLevelCode: string // 职位（BM_ZWDJ）
    postRankCode: string // 职级（BM_ZJ）
    recruitResponsibleUserNum: string // 面试安排人/招聘负责人工号
    resumeStatusCode: string // 简历状态(BM_ZPResumeStatus)
    timeContractEnd: number // 合同结束时间
    timeContractSigned: number // 合同签订时间
    timeEntry: number // 报道时间
    trialPeriodCode: string // 试用期期限，取值于EHR码表（BM_63）
    veteranCertificatePhotos: CertificateInfo[] // 退役证附件
    workCardPhoto: CertificateInfo // 工牌照片
    workPlaceCode: string // 现在工作地点（BM_73）
    disabilityTypeCode: string // 残疾类别，取值于EHR码表（BM_CJLB），长度限制64字符
    disabilityRemark: string // 特殊人员信息备注
    disabilityCertPeriodCode: string // 残疾证证件有效期，取值于EHR码表（BM_CJZYXQ），长度限制64字符
    disabilityCertificateNum: string // 残疾证件编号
    disabilityCertVersionCode: string // 残疾证版本，取值于EHR码表（BM_CJZBB），长度限制64字符
    timeDisabilityCertificate: number // 残疾证件签发时间
    disabilityNatureCode: string // 残疾性质，取值于EHR码表（BM_CJXZ），长度限制64字符
    timeCertificateProvide: number // 【v2.0.0】材料证明出具日期
    timeValidPeriodEnd: number // 【v2.0.0】材料有效期结束日期
  }
  // 教育经历
  educationExperiences: EducationExperienceItem[]
  // 工作经历
  workExperiences: WorkExperience & {
    leaveCertificates: EntryOtherAttachments[] // 离职证明
  }
  identityInfo: IdentityInfo & {
    // 身份证信息
    age: number // 年龄
    contactPhone: string // 联系电话
    email: string // 邮箱
    entryMaterialId: number // 入职材料id
    ethnicCode: string // 民族（BM_AE）
    genderCode: string // 性别（BM_AX）
    id: number // id
    idCardEmblemPhoto: CertificateInfo // 身份证反面照
    idCardFacePhoto: CertificateInfo // 身份证正面照
    idCardNum: number // 身份证号码
    idCardTypeCode: string // 证件类型（BM_ZJLX）
    interestsOrSpecialties: string // 特长爱好
    maritalStatusCode: string // 婚育状况（BM_BG）
    name: string // 姓名
    nationalityCode: string // 国籍（BM_66）
    nativePlaceAreaCode: string // 籍贯-区，取值于入职字典（OMS_NATIVE_PLACE）
    nativePlaceCityCode: string // 籍贯-市，取值于入职字典（OMS_NATIVE_PLACE）
    nativePlaceProvinceCode: string // 籍贯-省，取值于入职字典（OMS_NATIVE_PLACE）
    personalPhoto: CertificateInfo // 个人照片
    temporaryEmblemPhoto: CertificateInfo // 临时身份证反面照
    temporaryFacePhoto: CertificateInfo // 临时身份证正面照
    timeBirth: number // 出生日期
    usedName: string // 曾用名
    otherDocumentFacePhoto: CertificateInfo // 【v1.3】其他证件人像面照
    otherDocumentEmblemPhoto: CertificateInfo // 【v1.3】其他证件国徽面照
  }
  processInfo: {
    // 入职材料流程信息
    completeSituation: 0 | 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
    ehrProgress: number // Ehr流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功 默认0
    entryMaterialId: number // 入职材料id
    id: number // id
    isEntry: number // 是否入职 0-否 1-是
    isNeedAudit: number // 是否需要审核 0-否 1-是
    jobDuration: number // 作业净时长 单位：分钟
    processProgress: number // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘 默认0
    recentAuditUserName: string // 最近审核人姓名
    recentAuditUserNum: string // 最近审核人工号/手机号
    recentReviewUserNum: string // 最近一次复核人工号
    spentTotalTime: number // 总耗时 单位：分钟
    status: number // 状态 0-进行中 1-已完成 默认0
    successStatusType: number // 已完成的数据类型
    timeAuditComplete: number // 审核完成时间
    timeEhrReceive: number // Ehr接收成功时间
    timeRecentSubmit: number // 最近一次提交时间
    timeoutStatus: number // 超时状态 0-未超时 1-即将超时 2-已超时 默认0
    version: number // 乐观锁
  }
  relationshipInfos: {
    // 亲属关系信息
    contactPhone: string // 联系电话
    entryMaterialId: number // 入职材料id
    id: number // id
    memberName: string // 成员姓名
    memberRelationCode: string // 与本人关系（BM_GX）
    memberRelationRemark: string
    type: 1 | 2 // 1-主要成员 0-其他成员
    workUnitAndPosition: string // 工作单位及职务
  }[]
  otherInfo: {
    rejectReason: string // 复合驳回原因说明
    rejectReasonCodes: string[] // 复合驳回原因列表 取字典（OMS_INFORMATION_FAILED_REASON）
    entryMaterialProcessTrackInfo: string // 过程跟踪信息
    isHasWorkExperienceModule // 是否展示工作经历模块
  }
}

// 提交入职材料和入职材料审核信息
export interface EntryMaterialAuditGroups {
  auditResult: number // 审核结果
  bankCardInfo: EntryMaterialAudit // 银行卡信息
  educationExperiences: EntryMaterialAudit // 教育经历
  healthInfo: EntryMaterialAudit // 健康信息
  identityInfo: EntryMaterialAudit // 身份信息
  otherInfo: EntryMaterialAudit // 其他信息
  personBaseInfo: EntryMaterialAudit // 个人基本信息
  personPhoto: EntryMaterialAudit // 个人照片
  rejectModifyTemplateId?: number // 驳回修改模板id
  relationshipInfo: EntryMaterialAudit // 家庭成员信息
  resignationCertificate: EntryMaterialAudit // 离职证明
  timeRequireComplete?: number | any // 要求完成时间
  version?: number // 乐观锁
  workCardPhoto: EntryMaterialAudit // 工牌照片
}

export interface EntryMaterialSubmits {
  educationExperiences: EducationExperience[] // 教育经历
  entryInfo: EntryInfo // 入职信息
  identityInfo: IdentityInfo // 身份信息
  bankCardInfo: BankCardInfo // 支行信息
}

export interface EntryMaterialSubmit {
  entryMaterialAudit: EntryMaterialAuditGroups
  entryMaterialSubmit: EntryMaterialSubmits
}

// 审核材料模块处理进度状态
export type ProcessProgressType = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8

// 审核材料模块ehr流转状态
export type EhrProgressType = 0 | 1 | 2 | 3 | 4

// 审核材料必填校验
type AnchorItemStatus = '' | 'error' | 'success'
export interface EntryMaterialAuditRequired {
  personPhoto: AnchorItemStatus
  workCardPhoto: AnchorItemStatus
  personBaseInfo: AnchorItemStatus
  identityInfo: AnchorItemStatus
  bankCardInfo: AnchorItemStatus
  educationExperience: AnchorItemStatus
  workExperiences: AnchorItemStatus
  healthInfo: AnchorItemStatus
  relationshipInfo: AnchorItemStatus
  otherInfo: AnchorItemStatus
}
export interface EntryDraftContent {
  entryMaterialAudit: Record<string, any>
  entryMaterialSubmit: Record<string, any>
  entryMaterialMode: Record<string, any>
  entryMaterialId: number
  version: number
  processProgress: number
  userInfo: {
    name: string
    num: number | string
    nameAndNum: string
    updateTime: number
  }
}

export interface SaveDraftParams {
  formInstance: any
  formData: EntryMaterialDetail
  updateCallback: any
  updateFormData: EntryMaterialDetail
}

export interface EntryOtherAttachments {
  attachmentType: number
  entryMaterialId?: number
  fileName: string
  fileType: number
  id?: number
  previewId?: string
  sideType?: number
  size?: number
  url: string
  fileUrl?: string
  uid: string
  name?: string
}
