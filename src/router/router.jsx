import React, { useEffect } from 'react'
import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom'
import AsyncComponent from '@/utils/AsyncComponent'
import authWrapper from '@/mainApp/decorators/authWrapper'
import rbacWrapper from '@/mainApp/decorators/rbacWrapper'
import RouterClass from '@/services/router'
import { CacheScope, CacheContainer } from '@/components/cacheRoute'
import { IS_MICROCONTEXT } from '@/constants'
import base from '@/router/route/base'
import main from '@/router/route/main'
import BaseContainer from '@/components/BaseContainer'
import dmsWrapper from '@/decorators/dmsWrapper'
import DHRContainer from '@/components/DHRContainer'

const MicroLayout = authWrapper(dmsWrapper(AsyncComponent(() => import('@/layouts/MicroLayout'))))
const BaseLayout = dmsWrapper(AsyncComponent(() => import('@/layouts/BaseLayout')))
const Login = AsyncComponent(() => import('@/mainApp/login-manage/login'))
const Page403 = AsyncComponent(() => import('@/pages/exception/403'))
const Page404 = AsyncComponent(() => import('@/pages/exception/404'))

const routes = RouterClass.routes([...base, ...main])

const BaseRouter = authWrapper(
  rbacWrapper(({ router }) => {
    return (
      <Routes>
        <Route path={RouterClass.path('/')} element={<BaseLayout router={router} />}>
          {RouterClass.toList(router.routes).map((item) => {
            let element = null

            if (!router.hasAccess(item.path)) {
              element = <Page403 />
            }

            if (!element && item.component) {
              element = (
                <DHRContainer key={item.path} needBack={item.needBack} defaultTitle={item.name}>
                  <item.component />
                </DHRContainer>
              )
            }

            return (
              <Route
                key={`${item.path}${item.name}`}
                path={item.path}
                element={<CacheContainer {...item}>{element}</CacheContainer>}
              />
            )
          })}
          <Route path="*" element={<Page404 />} />
        </Route>
      </Routes>
    )
  }),
)

const Router = () => {
  const router = new RouterClass(routes)

  if (IS_MICROCONTEXT) {
    return (
      <BrowserRouter>
        <CacheScope>
          <Routes>
            <Route path={RouterClass.path('/')} element={<MicroLayout />}>
              {RouterClass.toList(router.routes).map((item) => {
                let element = null
                if (!router.hasAccess(item.path)) {
                  element = <Page403 />
                }

                if (!element && item.component) {
                  element = (
                    <DHRContainer key={item.path} needBack={item.needBack} defaultTitle={item.name}>
                      <item.component />
                    </DHRContainer>
                  )
                }

                return (
                  <Route
                    key={`${item.path}${item.name}`}
                    path={item.path}
                    element={<CacheContainer {...item}>{element}</CacheContainer>}
                  />
                )
              })}
            </Route>
          </Routes>
        </CacheScope>
      </BrowserRouter>
    )
  }

  return (
    <BrowserRouter>
      <CacheScope>
        <Routes>
          <Route path={RouterClass.path('/login')} element={<Login />} />
          <Route path="*" element={<BaseRouter router={router} />} />
        </Routes>
      </CacheScope>
    </BrowserRouter>
  )
}

export default Router
