import React from 'react'
import User from '@/mainApp/user'
import { RouteType } from '@/services/router'

const base: RouteType<string | React.FC>[] = [
  {
    name: '个人中心',
    path: '/user',
    component: User,
    hideInMenu: true,
  },
  {
    name: '403',
    path: '/403',
    component: '/exception/403',
    hideInMenu: true,
  },
  {
    name: '404',
    path: '/404',
    component: '/exception/404',
    hideInMenu: true,
  },
  {
    name: '500',
    path: '/500',
    component: '/exception/500',
    hideInMenu: true,
  },
]

export default base
