import React from 'react'
import { RouteType } from '@/services/router'
import { ContainerOutlined } from '@ant-design/icons'

const main: RouteType<string>[] = [
  {
    path: '/',
    component: '/portal/portal-main/index',
    name: '欢迎',
    hideInMenu: true,
  },
  {
    name: '预入职',
    icon: <ContainerOutlined />,
    path: '/preEntry',
    component: '/preEntry',
    hideChildrenInMenu: true,
    permission: true,
    cache: true,
    routes: [
      {
        path: '/preEntry/entryMaterialsAuditForm',
        name: '材料审核',
        icon: <ContainerOutlined />,
        component: '/entryMaterialsAudit',
        hideInMenu: true,
        needBack: true,
      },
      {
        path: '/preEntry/log',
        name: '操作记录',
        hideInMenu: true,
        component: '/preEntry/log',
        needBack: true,
      },
    ],
  },
  {
    name: '模板管理',
    icon: <ContainerOutlined />,
    path: '/templateManage',
    component: '/templateManage',
    hideChildrenInMenu: true,
    permission: true,
    // TODO 暂时屏蔽 影响页面 useEffect 卸载函数执行
    // cache: true,
    routes: [
      {
        path: '/templateManage/add',
        name: '新建模板',
        component: '/templateManage/add',
      },
      {
        path: '/templateManage/edit',
        name: '编辑模板',
        component: '/templateManage/edit',
      },
    ],
  },
  {
    name: '系统配置',
    icon: <ContainerOutlined />,
    path: '/systemConfig',
    component: '/systemConfig',
    hideChildrenInMenu: true,
    permission: true,
  },
]

export default main
