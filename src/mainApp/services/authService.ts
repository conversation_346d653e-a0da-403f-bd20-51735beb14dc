import config from '@/config'
import { BASE_PATH, IS_MICROCONTEXT } from '@/constants'

export const login = () => {
  const redirectUrl = sessionStorage.getItem('redirectUrl')
  if (redirectUrl) {
    sessionStorage.removeItem('redirectUrl')
    window.location.href = redirectUrl
  } else {
    window.location.href = `${BASE_PATH}/`
  }
}

export const logout = () => {
  sessionStorage.setItem('redirectUrl', window.location.href)
  window.location.href = IS_MICROCONTEXT ? config.loginUrl : `${BASE_PATH}${config.loginUrl}`
}
