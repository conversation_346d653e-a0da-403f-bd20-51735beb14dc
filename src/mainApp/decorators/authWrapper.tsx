import React, { useEffect, useState } from 'react'
import uc from '@galaxy/uc'
import { logout } from '@/mainApp/services/authService'
import userService from '@/services/user'

export default function authWrapper(WrappedComponent) {
  function Auth(props) {
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      ;(async () => {
        const isLogin = await uc.isLogin()
        if (isLogin) {
          const userInfo = await uc.getUserInfo()
          // 内部人员
          if (userInfo.userType === 11) {
            userService.info = {
              id: `${userInfo.id}`,
              name: userInfo.name,
              num: userInfo.num,
              phone: userInfo.phone,
              email: userInfo.email,
              departmentName: userInfo.departmentName,
              positionName: userInfo.postName,
              // @ts-ignore uc-sdk声明错误
              userName: userInfo.userName,
            }
          }
          // 外部人员
          if (userInfo.userType === 12 || userInfo.userType === 21) {
            userService.info = {
              id: `${userInfo.id}`,
              name: userInfo.name,
              phone: userInfo.phone,
              userName: userInfo.userName,
            }
          }
          setLoading(false)
          return
        }
        logout()
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Auth
}
