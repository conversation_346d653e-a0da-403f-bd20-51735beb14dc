import React, { useEffect, useState } from 'react'
import rbac from '@galaxy/rbac'
import config from '@/config'

const { ucAppCode } = config

// dhr-rbac 配置
const rbacConfig = {
  id: 476,
  name: '编制权限',
  systemOrComponentId: 116,
  systemOrComponentType: 3,
  tenantId: 82,
}

// uc-rbac 配置
const ucRbacConfig = {
  id: '9510d50e6e353ebeb2b9d2ec8dd45f8e',
  name: '实体权限-系统管理员',
}

// 是否对接UC-RBAC
const isUc = true

export default function rbacWrapper(WrappedComponent) {
  function Rbac(props) {
    const { router } = props
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      let entity = rbac.getEntity()
      if (!entity) {
        if (isUc) {
          rbac.setEntity(ucRbacConfig)
        } else {
          rbac.setEntity(rbacConfig)
        }
        entity = ucRbacConfig
      }
      ;(async () => {
        try {
          await rbac.getMenuResource({
            id: entity.id,
            appId: ucAppCode,
          })

          await rbac.getOperationResource({
            id: entity.id,
            appId: ucAppCode,
          })

          router.updateAccessRoutes()
        } catch (error) {
          console.error('获取RBAC资源失败')
        }

        setLoading(false)
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Rbac
}
