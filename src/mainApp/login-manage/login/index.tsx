import React from 'react'
import { message } from 'antd'
import { login } from '@/mainApp/services/authService'
import { Login as LoginCom } from '@galaxy/uc-component'
import style from './style.module.less'

const Login: React.FC = () => {
  const onLoginSuccess = async () => {
    message.success('登录成功', 3)
    login()
  }

  const onLoginError = (error) => {
    // eslint-disable-next-line no-console
    console.error(error)
  }

  return (
    <div className={style.loginContainer}>
      <div className={style.loginPageContainer}>
        <LoginCom onSuccess={onLoginSuccess} onError={onLoginError} />
        <div className={style.copyRight}>
          推荐使用
          <a
            style={{
              color: '#fff',
            }}
            href="https://imgs.pupuapi.com/appfile/ChromeSetup.exe"
          >
            谷歌浏览器，点此下载
            <img
              style={{
                marginTop: '-3px',
              }}
              alt=""
              src={require('@/assets/Vector.png')}
            />
          </a>
        </div>
      </div>
    </div>
  )
}
export default Login
