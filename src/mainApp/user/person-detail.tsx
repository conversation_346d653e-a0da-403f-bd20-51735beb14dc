/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect } from 'react'
import { Form, Button, Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { logout as ucLogout } from '@galaxy/uc'
import { useNavigate } from 'react-router-dom'
import Router from '@/services/router'
import userService from '@/services/user'
import { useStore } from '@/stores'
import style from './style.module.less'

const { confirm } = Modal

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

const personDetail: React.FC = () => {
  const setExtra = useStore((state) => state.setExtra)
  const navigate = useNavigate()

  const formatPhone = (string: string | number, n = 4) => {
    const formatReg = new RegExp('(\\w{3})(\\d+)(\\d{4})', 'g')
    if (string) {
      return `${string}`.replace(/\s/g, '').replace(formatReg, '$1****$3')
    }
    return ''
  }

  // 注销
  const logout = () => {
    // 清除cookie，重定向至登陆页
    ucLogout()
    window.location.href = Router.path('/login')
  }

  const btns = (
    <>
      <Button
        type="primary"
        onClick={() => {
          confirm({
            title: '是否确定退出？',
            icon: <ExclamationCircleOutlined />,
            // content: '是否确定退出？',
            onOk() {
              logout()
            },
          })
        }}
      >
        退出登录
      </Button>
      <Button
        onClick={() => {
          navigate(-1)
        }}
      >
        返回
      </Button>
    </>
  )

  useEffect(() => {
    setExtra(btns)
    return () => {
      setExtra([])
    }
  }, [])

  return (
    <Form className={style.personDetail} name="validate_other" {...formItemLayout}>
      {typeof userService.info?.id === 'string' && (
        <Form.Item label="账号ID">
          <span className="ant-form-text">{userService.info.id}</span>
        </Form.Item>
      )}
      {typeof userService.info?.name === 'string' && (
        <Form.Item label="员工姓名">
          <span className="ant-form-text">{userService.info.name}</span>
        </Form.Item>
      )}
      {typeof userService.info?.num === 'string' && (
        <Form.Item label="工号">
          <span className="ant-form-text">{userService.info.num}</span>
        </Form.Item>
      )}
      {typeof userService.info?.phone === 'string' && (
        <Form.Item label="手机号码">
          <span className="ant-form-text">{formatPhone(userService.info.phone)}</span>
        </Form.Item>
      )}
      {typeof userService.info?.email === 'string' && (
        <Form.Item label="电子邮箱">
          <span className="ant-form-text">{userService.info.email}</span>
        </Form.Item>
      )}
      {typeof userService.info?.departmentName === 'string' && (
        <Form.Item label="所属部门">
          <span className="ant-form-text">{userService.info.departmentName}</span>
        </Form.Item>
      )}
      {typeof userService.info?.positionName === 'string' && (
        <Form.Item label="岗位">
          <span className="ant-form-text">{userService.info.positionName}</span>
        </Form.Item>
      )}
    </Form>
  )
}

export default personDetail
