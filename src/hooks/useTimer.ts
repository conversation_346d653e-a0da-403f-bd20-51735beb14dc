import { throttle } from 'lodash'
import { useCallback, useEffect, useRef } from 'react'

export type CallbackType = (data: { duration: number; start: number; end: number }) => void

// 计时器，记录页面访问时间，触发时机在浏览器回退、浏览器关闭、浏览器收起、tab切换、页面关闭、路由切换、页面刷新七个场景。
function useTimer(callback: CallbackType) {
  // 记录一开始进入的时间节点
  const begin = useRef<number>(Date.now())

  // 发送埋点数据前计算时长
  const getTime = useCallback((end: number): number => {
    return end - begin.current
  }, [])

  const cb = useCallback(
    throttle(() => {
      const end = Date.now()
      callback({
        duration: getTime(end),
        start: begin.current,
        end,
      })
    }, 1000),
    [getTime],
  )

  useEffect(() => {
    // const beforeunload = () => {
    //   cb()
    // }
    // // 浏览器关闭、页面关闭、页面刷新，通过beforeunload触发
    // window.addEventListener('beforeunload', beforeunload)

    const visibilitychange = () => {
      if (document.hidden) {
        cb()
      } else {
        // 页面重新显示时，重新监听页面时间
        // 开始时间重置
        begin.current = Date.now()
      }
    }
    // 浏览器关闭、页面关闭、tab切换、浏览器收起、页面刷新，通过visibilitychange触发
    document.addEventListener('visibilitychange', visibilitychange)
    return () => {
      // window.removeEventListener('beforeunload', beforeunload)
      document.removeEventListener('visibilitychange', visibilitychange)
      // 路由切换
      cb()
    }
  }, [])
}

export default useTimer
