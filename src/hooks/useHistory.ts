import { useContext } from 'react'
import { UNSAFE_DataRouterContext as DataRouterContext } from 'react-router-dom'

export default function useHistory() {
  const context = useContext(DataRouterContext)
  const navigator = context?.navigator
  const state = context?.router?.state
  const subscribe = context?.router?.subscribe

  return {
    get location() {
      return state?.location
    },
    get action() {
      return state?.historyAction
    },
    listen: subscribe,
    ...navigator,
  }
}
