import { Item, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { SchemaColumnType } from '@amazebird/antd-schema-table'
import React from 'react'
import { Col, Row } from 'antd'
import style from './style.module.less'

export type Columns = { dataIndex: string; title: string }[]

type Iprops = {
  columns: Columns
  content: string
  value?: Record<string, any>
}

const ShowCondition: React.FC<Iprops> = ({ columns, content, value }) => {
  return (
    <div>
      {content && <span className={style.remark}>{content}</span>}
      {columns.map((item) => (
        <Row key={item.dataIndex} className={style.row}>
          <Col span={8} className={style.label}>
            {`${item.title}：`}
          </Col>
          <Col span={16} className={style.content}>
            {value?.[item.dataIndex] || '不限'}
          </Col>
        </Row>
      ))}
    </div>
  )
}

export default ShowCondition
