import { Item, SchemaForm } from '@amazebird/antd-schema-form'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { SchemaTableProps } from '@amazebird/antd-schema-table'
import { debounce } from 'lodash-es'
import dayjs from 'dayjs'
import ReModal from '@/components/ReModal'
import moment from 'moment'
import { TaskController } from '@galaxy/async-task-component'
import ShowCondition, { Columns } from './showCondition'
import ShowCount from './ShowCount'

type OpenConfig = {
  columns: SchemaTableProps['searchColumns']
  getCount: () => Promise<number>
  onOk?: (taskController: TaskController<any>) => Promise<void>
  title: string
  content?: string
}

export type ActionType = {
  open: (values: Record<string, any>) => void
}

function useExport(config: OpenConfig): { modal: React.ReactNode; action: ActionType } {
  const [open, setOpen] = useState<boolean>(false)

  const form = SchemaForm.createForm()

  const [okButtonDisabled, setOkButtonDisabled] = useState<boolean>(true)

  const getCount = useCallback(
    debounce(async () => {
      form.setFieldsValue({
        count: undefined,
      })
      setOkButtonDisabled(true)
      // 将showcount value设置成-1，当成是loading状态
      form.setFieldsValue({
        count: -1,
      })
      const count = await config?.getCount().finally(() => {
        form.setFieldsValue({
          count: undefined,
        })
      })
      form.setFieldsValue({
        count,
      })
      setOkButtonDisabled(count === 0)
    }, 100),
    [],
  )

  useEffect(() => {
    if (open === false) {
      form.resetFields()
    } else {
      getCount()
    }
  }, [open])

  const columns = useMemo<Columns>(() => {
    return (config.columns ?? [])?.map((item) => {
      return {
        dataIndex: item.dataIndex!,
        title: item.title!,
      }
    })
  }, [config.columns])

  const taskController = useMemo(() => new TaskController(), [])
  const modal = (
    <ReModal
      title={config?.title}
      open={open}
      onCancel={() => {
        taskController.cancelTask()
        setOpen(false)
      }}
      okButtonProps={{
        disabled: okButtonDisabled,
      }}
      onOk={async () => {
        try {
          await config.onOk?.(taskController)
          setOpen(false)
        } catch (error) {
          console.log(error)
        }
      }}
      width={600}
    >
      <SchemaForm
        form={form}
        labelCol={{
          span: 6,
        }}
        wrapperCol={{
          span: 18,
        }}
        schema={{
          condition: {
            label: '导出条件',
            component: ShowCondition,
            props: {
              content: config?.content,
              columns,
            },
          },
          count: {
            label: '导出行数',
            component: ShowCount,
          },
        }}
      >
        <Item field="condition" />
        <Item field="count" />
      </SchemaForm>
    </ReModal>
  )

  const action = useMemo<ActionType>(
    () => ({
      open: (condition) => {
        setOpen(true)
        form.setFieldsValue({
          condition,
        })
      },
    }),
    [],
  )

  return {
    modal,
    action,
  }
}

export default useExport
