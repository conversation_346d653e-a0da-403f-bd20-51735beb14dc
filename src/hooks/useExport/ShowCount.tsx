import { LoadingOutlined } from '@ant-design/icons'
import { Spin } from 'antd'
import React from 'react'

type Iprops = {
  value: number
}

const antIcon = <LoadingOutlined style={{ fontSize: 14 }} spin />

const ShowCount: React.FC<Iprops> = ({ value }) => {
  // 暂时先用-1代表loading状态
  if (value === -1) {
    return (
      <span>
        <Spin indicator={antIcon} /> 行
      </span>
    )
  }

  if (typeof value === 'number') {
    return <span>{`${value} 行`}</span>
  }
  return <span>&nbsp;&nbsp;&nbsp;行</span>
}

export default ShowCount
