import { SchemaColumnType, SchemaTableProps } from '@amazebird/antd-schema-table'
import { useMemo } from 'react'
import { SYSTEM_CONFIG_TYPE } from '@/constants'

const typeOptions = [
  { label: '规则配置', value: 1 },
  { label: '开户银行配置', value: 2 },
  { label: '复核规则配置', value: 3 },
  { label: '时间规则配置', value: SYSTEM_CONFIG_TYPE.TIME_RULE },
]

function useColumns(): [SchemaTableProps['searchColumns'], SchemaColumnType] {
  const columns = useMemo(
    () => [
      {
        dataIndex: 'operateTime',
        title: '操作时间',
        width: 200,
        cell: 'DateTime',
      },
      { dataIndex: 'type', title: '配置类型', width: 100, options: typeOptions },
      { dataIndex: 'content', title: '操作内容', width: 400 },
      { dataIndex: 'operator', title: '操作人', width: 150 },
    ],
    [],
  )
  const searchColumns = useMemo(
    () => [
      {
        key: 'timeOperate',
        dataIndex: 'timeOperate',
        title: '操作时间',
        component: 'RangePicker',
        props: {
          format: 'YYYY-MM-DD HH:mm:ss',
          showTime: true,
        },
      },
      {
        key: 'type',
        dataIndex: 'type',
        title: '配置类型',
        component: 'Select',
        options: typeOptions,
        props: { mode: 'multiple' },
      },
      {
        key: 'operatorUserNumOrName',
        dataIndex: 'operatorUserNumOrName',
        title: '操作人',
        component: 'Input',
        props: { maxLength: 40, allowClear: true },
        placeholder: '请输入操作人姓名或工号',
      },
    ],
    [],
  )

  return [searchColumns, columns]
}

export default useColumns
