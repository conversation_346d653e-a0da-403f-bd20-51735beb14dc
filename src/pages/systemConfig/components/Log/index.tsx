import React from 'react'
import { SchemaTable, SchemaTableProps } from '@amazebird/antd-schema-table'
import { set } from 'lodash-es'
import logService from '@/services/log'
import useColumns from './useColumns'
import style from './style.module.less'

const Log = () => {
  const [searchColumns, columns] = useColumns()

  const requestData: SchemaTableProps['request'] = async ({ filter, pagination }) => {
    try {
      const { timeOperate, ...restFilter } = filter
      const $filter = restFilter
      if (timeOperate) {
        set($filter, 'timeOperateBegin', timeOperate[0].valueOf())
        set($filter, 'timeOperateEnd', timeOperate[1].valueOf())
      }
      set($filter, 'page', pagination.current)
      set($filter, 'size', pagination.pageSize)

      const { data, count } = await logService.getConfigLogList($filter)

      return {
        data,
        total: count,
      }
    } catch (error) {
      return {
        data: [],
        total: 0,
      }
    }
  }

  return (
    <div className={style.log}>
      <SchemaTable
        columns={columns}
        searchColumns={searchColumns}
        request={requestData}
        searchProps={{ labelWidth: 80 }}
        pagination={{ defaultPageSize: 10, showQuickJumper: true }}
        rowKey={(record, index) => JSON.stringify(record) + index}
        toolbarOptions={{ setting: false }}
      />
    </div>
  )
}

export default Log
