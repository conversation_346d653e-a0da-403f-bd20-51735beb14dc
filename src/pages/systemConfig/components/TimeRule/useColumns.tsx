import React, { useMemo } from 'react'
import QuestionTooltip from '@/components/QuestionTooltip'

// 展示模型统一配置
const viewTableConfig = {
  type: 'Number',
  props: {
    suffix: '天',
  },
}

// 编辑模式统一配置
const editTableConfig = {
  placeholder: '请输入',
  required: [true, '请输入'],
  props: {
    min: 1,
    max: 999,
    addonAfter: '天',
  },
}

function useColumns() {
  return useMemo(() => {
    return [
      {
        title: '一级部门',
        dataIndex: 'departmentName',
        width: 250,
      },
      {
        title: (
          <QuestionTooltip
            mark="O序列自动结束招聘时间"
            pointTips={[
              '控制O序列候选人预入职超期后自动结束预入职流程的时间；',
              '每天早上8点定时任务处理，当前日期-预入职审核条目的超期日期≥所输入配置天数时，自动结束预入职。',
            ]}
          />
        ),
        dataIndex: 'o',
        width: 200,
        cell: { ...viewTableConfig },
        editable: {
          component: 'InputNumber',
          ...editTableConfig,
        },
      },
      {
        title: (
          <QuestionTooltip
            mark="S序列自动结束招聘时间"
            pointTips={[
              '控制S序列候选人预入职超期后自动结束预入职流程的时间；',
              '每天早上8点定时任务处理，当前日期-预入职审核条目的超期日期≥所输入配置天数时，自动结束预入职。',
            ]}
          />
        ),
        dataIndex: 's',
        width: 200,
        cell: { ...viewTableConfig },
        editable: {
          component: 'InputNumber',
          ...editTableConfig,
        },
      },
      {
        title: (
          <QuestionTooltip
            mark="非O/S序列自动结束招聘时间"
            pointTips={[
              '控制非O/S序列候选人预入职超期后自动结束预入职流程的时间；',
              '每天早上8点定时任务处理，当前日期-预入职审核条目的超期日期≥所输入配置天数时，自动结束预入职。',
            ]}
          />
        ),
        dataIndex: 'nonOs',
        width: 200,
        cell: { ...viewTableConfig },
        editable: {
          component: 'InputNumber',
          ...editTableConfig,
        },
      },
    ]
  }, [])
}

export default useColumns
