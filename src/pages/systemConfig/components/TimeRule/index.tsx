import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Spin, message } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { Layout, SchemaForm } from '@amazebird/antd-schema-form'
import configService, { UpdateDataParams } from '@/services/config'
import { isEqual } from 'lodash'
import ReModal from '@/components/ReModal'
import ReButton from '@/components/ReButton'
import { SYSTEM_CONFIG_TYPE } from '@/constants'
import style from './style.module.less'
import useColumns from './useColumns'

type ValueType = {
  code: string
  departmentName?: string
  o?: string
  s?: string
  nonOs?: string
}

const TimeRule = () => {
  const [loading, setLoading] = useState<boolean>(true)

  const columns = useColumns()

  const [dataSource, setDataSource] = useState<ValueType[]>([])

  const getConfig = async () => {
    const data = await configService.getData({ type: SYSTEM_CONFIG_TYPE.TIME_RULE })
    const result: ValueType[] = []
    Object.values(data)?.forEach((item) => {
      try {
        const { department_name, o, s, non_os } = JSON.parse(item.value)
        result.push({
          ...item,
          departmentName: department_name,
          o,
          s,
          nonOs: non_os,
        })
      } catch (error) {
        result.push({ departmentName: '', o: '', s: '', nonOs: '', ...item })
      }
    })
    setDataSource(result)
  }

  // 获取配置
  useEffect(() => {
    getConfig().finally(() => {
      setLoading(false)
    })
  }, [])

  const [editable, setEditable] = useState<
    | false
    | {
        errorType: 'inline' | 'tooltip'
        onValuesChange?: any
        scrollToFirstError?: boolean
      }
  >(false)

  const action = SchemaTable.createAction()
  const form: any = SchemaForm.createForm()

  const changeEditableState = () => {
    if (editable) {
      action?.cancelEdit()
      setEditable(false)
    } else {
      action?.editTable()
      setEditable({
        errorType: 'inline',
      })
    }
  }

  return (
    <div className={style.timeRule}>
      <div className={style.main}>
        <Spin spinning={loading}>
          <div className={style.title}>时间规则配置</div>
          <SchemaTable
            action={action}
            columns={columns}
            dataSource={dataSource}
            editable={editable}
            editForm={form}
            pagination={false}
            rowKey="code"
          />
        </Spin>
      </div>
      <div className={style.footer}>
        <Layout align="left">
          {editable === false ? (
            <Button
              type="primary"
              onClick={() => {
                changeEditableState()
              }}
            >
              编辑
            </Button>
          ) : (
            <div className={style.footer}>
              <Button
                onClick={() => {
                  const data = action?.getEditData()
                  if (isEqual(data, dataSource)) {
                    changeEditableState()
                    return
                  }
                  ReModal.confirm({
                    title: '提示',
                    content: '已修改的配置将不会保存，确定取消吗？',
                    onOk: () => {
                      changeEditableState()
                    },
                  })
                }}
                className={style.footer__button}
              >
                取消
              </Button>
              <ReButton
                type="primary"
                onClick={async () => {
                  await form.validateFields()
                  setLoading(true)
                  try {
                    const data = action?.getEditData()
                    const result: UpdateDataParams[] = []

                    data.forEach((item) => {
                      result.push({
                        type: SYSTEM_CONFIG_TYPE.TIME_RULE,
                        code: item.code,
                        name: '时间规则配置',
                        value: JSON.stringify({
                          department_name: item.departmentName,
                          o: item.o,
                          s: item.s,
                          non_os: item.nonOs,
                        }),
                      })
                    })

                    await configService.updateData(result)
                    changeEditableState()
                    message.success('配置信息提交成功')
                    await getConfig()
                  } catch (error) {
                    console.log(error)
                  }
                  setLoading(false)
                }}
              >
                提交
              </ReButton>
            </div>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default TimeRule
