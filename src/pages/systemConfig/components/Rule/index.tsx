import React, { useEffect, useRef, useState } from 'react'
import {
  FORM_MODE,
  Item,
  Layout as SchemaLayout,
  Reset,
  SchemaForm,
  Submit,
} from '@amazebird/antd-schema-form'
import { But<PERSON>, Spin, message, Card } from 'antd'
import configService from '@/services/config'
import ReModal from '@/components/ReModal'
import { compare } from '@/utils/utils'
import moment from 'moment'
import style from './style.module.less'
import '@amazebird/antd-field/es/select/index'
import '@amazebird/antd-field/es/time-picker/index'
import '@amazebird/antd-field/es/input/index'
import '@amazebird/antd-field/es/input-number/index'
import useColumns from './columns'
import { ruleConfigMap } from './constant'

// eslint-disable-next-line max-lines-per-function
const Rule = () => {
  const form = SchemaForm.createForm()
  const [mode, setMode] = useState<string>(FORM_MODE.DETAIL)
  const defaultValue = useRef<Record<string, any>>()
  const [loading, setLoading] = useState<boolean>(true)
  const { schema } = useColumns()

  const getConfig = async () => {
    const data = await configService.getRuleData({ type: 1 })
    defaultValue.current = data
    form.setFieldsValue(data)
  }

  useEffect(() => {
    getConfig().finally(() => {
      setLoading(false)
    })
  }, [])

  return (
    <SchemaForm
      style={{ height: '100%' }}
      form={form}
      schema={schema}
      mode={mode}
      labelCol={{
        span: 6,
      }}
      wrapperCol={{
        span: 18,
      }}
    >
      <div className={style.rule}>
        <div className={style.main}>
          <Spin spinning={loading}>
            <div className={style.header}>信息补录通知配置</div>
            <div className={style.contentItem}>
              <Item field="A" />
              <Item field="B" />
              <Item field="C" />
            </div>
            <div className={style.header}>审核通过通知配置</div>
            <div className={style.contentItem}>
              <Item field="D" />
              <Item field="E" />
              <Item field="F" />
            </div>
            <div className={style.header}>提醒通知配置</div>
            <div className={style.contentItem}>
              <Item field="G" />
              <Item field="H" />
              <Item field="I" />
              <Item field="J" />
            </div>
            <div className={style.header}>其他规则配置</div>
            <div className={style.contentItem}>
              <Item field="L" />
            </div>
          </Spin>
        </div>
        <div className={style.footer}>
          <SchemaLayout align="left">
            {mode === FORM_MODE.MODIFY ? (
              <>
                <Reset
                  htmlType="button"
                  onClick={() => {
                    const data = form.getFieldsValue(true)
                    if (compare(defaultValue.current, data)) {
                      ReModal.confirm({
                        title: '提示',
                        content: '已修改的配置将不会保存，确定取消吗？',
                        onOk: () => {
                          form.setFieldsValue(defaultValue.current)
                          setMode(FORM_MODE.DETAIL)
                        },
                      })
                      return
                    }
                    setMode(FORM_MODE.DETAIL)
                  }}
                >
                  取消
                </Reset>
                <Submit
                  onFinish={async (values) => {
                    setLoading(true)
                    try {
                      const result = Object.keys(values).map((key) => {
                        const value = values[key] ?? ''
                        const otherParams = ruleConfigMap[key]
                        return {
                          ...otherParams,
                          value,
                        }
                      })
                      await configService.updateRuleData(result)
                      message.success('配置信息提交成功')
                      setMode(FORM_MODE.DETAIL)
                      await getConfig()
                    } catch (error) {
                      console.log(error)
                    }
                    setLoading(false)
                  }}
                />
              </>
            ) : (
              <Button
                type="primary"
                onClick={() => {
                  setMode(FORM_MODE.MODIFY)
                }}
              >
                编辑
              </Button>
            )}
          </SchemaLayout>
        </div>
      </div>
    </SchemaForm>
  )
}

export default Rule
