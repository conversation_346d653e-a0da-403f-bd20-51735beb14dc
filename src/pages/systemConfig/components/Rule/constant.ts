export const ruleConfigMap = {
  // O序列通知模板
  A: {
    type: 1, // 配置类型，跟code一起使用确保唯一
    code: '9', // 配置码值，跟type一起使用确保唯一
    name: 'O序列-信息补录短信模板', // 配置名称
  },
  // S序列通知模板
  B: {
    type: 1,
    code: '10',
    name: 'S序列-信息补录短信模板',
  },
  // 非O/S序列通知模板
  C: {
    type: 1,
    code: '11',
    name: '非O/S序列-信息补录短信模板',
  },
  // O序列通知模板
  D: {
    type: 1,
    code: '12',
    name: 'O序列-审核通过通知短信模板',
  },
  // S序列通知模板
  E: {
    type: 1,
    code: '13',
    name: 'S序列-审核通过通知短信模板',
  },
  // 非O/S序列通知模板
  F: {
    type: 1,
    code: '14',
    name: '非O/S序列-审核通过通知短信模板',
  },
  // 信息补录提醒短信模板
  G: {
    type: 1,
    code: '3',
    name: '信息补录提醒短信模板',
  },
  // 驳回修改提醒短信模板
  H: {
    type: 1,
    code: '4',
    name: '驳回修改提醒短信模板',
  },
  // 信息补录提醒时间
  I: {
    type: 1,
    code: '5',
    name: '信息补录提醒时间',
  },
  // 驳回修改提醒时间
  J: {
    type: 1,
    code: '6',
    name: '驳回修改提醒时间',
  },
  // 超期自动结束招聘
  K: {
    type: 1,
    code: '7',
    name: '超期自动结束招聘天数',
  },
  // 不通知的材料不合规原因
  L: {
    type: 1,
    code: '8',
    name: '不通知的材料不合规原因',
  },
}
