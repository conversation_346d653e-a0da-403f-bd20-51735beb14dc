.rule {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main {
  background-color: #fff;
  flex: 1;
  margin-bottom: 10px;
  overflow: auto;
}

.header {
  font-size: 16px;
  font-weight: bold;
  padding: 10px 24px;
  border-bottom: 1px solid #F5F5F5;
}

.contentItem {
  padding: 10px 24px;
  margin: 10px;
}

.footer {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  background: #fff;
  box-shadow: inset 0px 1px 0px rgb(0 0 0 / 6%);
  border-radius: 0px 0px 2px 2px;
  z-index: 2;
  height: 48px;
  padding-right: 24px;
}
