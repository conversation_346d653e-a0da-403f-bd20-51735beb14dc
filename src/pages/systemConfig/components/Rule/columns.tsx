/* eslint-disable max-lines-per-function */
import React, { useMemo } from 'react'
import { FORM_MODE, SchemaType, Observer } from '@amazebird/antd-schema-form'
import { Cascader } from 'antd'
import DictText from '@/components/DictText'
import { Cascader as Dict } from '@galaxy/dict'
import templateService from '@/services/template'
import QuestionTooltip from '@/components/QuestionTooltip'

const useColumns = () => {
  const schema = useMemo<SchemaType>(
    () => ({
      // O序列通知模板
      A: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="O序列通知模板"
            pointTips={[
              '控制O序列的候选人信息补录短信对应的模板；',
              '当生成候选人预入职审核条目后，自动给O序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_001',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择O序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择O序列通知模板',
        },
      },
      // S序列通知模板
      B: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="S序列通知模板"
            pointTips={[
              '控制S序列的候选人信息补录短信对应的模板；',
              '当生成候选人预入职审核条目后，自动给S序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_001',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择S序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择S序列通知模板',
        },
      },
      // 非O/S序列通知模板
      C: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="非O/S序列通知模板"
            pointTips={[
              '控制非O/S序列的候选人信息补录短信对应的模板；',
              '当生成候选人预入职审核条目后，自动给非O/S序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_001',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择非O/S序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择非O/S序列通知模板',
        },
      },
      // O序列通知模板
      D: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="O序列通知模板"
            pointTips={[
              '控制O序列的候选人审核通过通知短信对应的模板；',
              '当候选人预入职审核通过，并下发ehr成功后，自动给O序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_005',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择O序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择O序列通知模板',
        },
        // required: [true, '请选择O序列通知模板'],
      },
      // S序列通知模板
      E: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="S序列通知模板"
            pointTips={[
              '控制S序列的候选人审核通过通知短信对应的模板；',
              '当候选人预入职审核通过，并下发ehr成功后，自动给S序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_005',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择S序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择S序列通知模板',
        },
        // required: [true, '请选择S序列通知模板'],
      },
      // 非O/S序列通知模板
      F: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="非O/S序列通知模板"
            pointTips={[
              '控制非O/S序列的候选人审核通过通知短信对应的模板；',
              '当候选人预入职审核通过，并下发ehr成功后，自动给非O/S序列候选人手机发送所选模板短信内容',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_005',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: [true, '请选择非O/S序列通知模板'],
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择非O/S序列通知模板',
        },
        // required: [true, '请选择非O/S序列通知模板'],
      },
      // 信息补录提醒短信模板
      G: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="信息补录提醒短信模板"
            pointTips={[
              '控制候选人的信息补录提醒短信对应的模板；',
              '在预入职日期10天前开始给未提交信息补录的候选人每天发送信息补录提醒短信，催促候选人尽快完成信息补录',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_003',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择信息补录提醒短信模板',
        },
        required: [true, '请选择信息补录提醒短信模板'],
      },
      // 驳回修改提醒短信模板
      H: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="驳回修改提醒短信模板"
            pointTips={[
              '控制候选人的驳回修改提醒短信模板内容；',
              '当给候选人发送驳回修改短信后，给未提交的候选人每天发送信息补录提醒，催促候选人尽快完成信息补录',
            ]}
          />
        ),
        component: 'Select',
        options: async () => {
          const { data } = await templateService.getList({
            typeCode: 'OMS_TT_004',
            noPage: true,
          })
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        props: {
          showSearch: true,
          optionFilterProp: 'label',
          placeholder: '请选择驳回修改提醒短信模板',
        },
        required: [true, '请选择驳回修改提醒短信模板'],
      },
      // 信息补录提醒时间
      I: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="信息补录提醒时间"
            pointTips={[
              '控制信息补录通知提醒短信的发送时间；',
              '预入职日期10天前开始，在每天的配置时间，给未提交信息补录的候选人发送信息补录提醒短信，催促候选人尽快完成信息补录',
            ]}
          />
        ),
        component: 'TimePicker',
        required: [true, '请选择信息补录提醒时间'],
        props: {
          format: 'H:mm',
          style: { width: '100%' },
          placeholder: '请选择信息补录提醒时间',
        },
      },
      // 驳回修改提醒时间
      J: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="驳回修改提醒时间"
            pointTips={[
              '控制驳回修改提醒短信的发送时间；',
              '给候选人发送驳回修改短信后，在每天的配置时间，给未提交的候选人每天发送驳回修改提醒短信，催促候选人尽快完成信息补录',
            ]}
          />
        ),
        component: 'TimePicker',
        required: [true, '请选择驳回修改提醒时间'],
        props: {
          format: 'H:mm',
          style: { width: '100%' },
          placeholder: '请选择驳回修改提醒时间',
        },
      },
      // 不通知的材料不合规原因
      L: {
        label: (
          <QuestionTooltip
            // iconStyle={{ color: 'var(--primary-color)' }}
            mark="不通知的材料不合规原因"
            pointTips={[
              '控制不需要通知候选人的材料不符合要求原因；',
              '材料审核时若选择了此处内容，对应原因内容将不会通知候选人。',
            ]}
          />
        ),
        component: Observer({
          watch: '#mode',
          action: (mode) => {
            if (mode === FORM_MODE.MODIFY) {
              return Dict
            }
            return ({ value }) => {
              if (Array.isArray(value)) {
                return (
                  <DictText
                    text={{
                      sets: value.map((item) => ({
                        code: item,
                        dictCode: 'OMS_INFORMATION_FAILED_REASON',
                        scope: 'ENTRY',
                        fullName: true,
                      })),
                      transfer: (result) => result,
                    }}
                    render={(text) => {
                      if (Array.isArray(text)) {
                        return (
                          <div>
                            {text.map((item, index) => (
                              // eslint-disable-next-line react/no-array-index-key
                              <span key={index} style={{ display: 'block' }}>
                                {`${item}${text.length - 1 !== index ? '、' : ''}`}
                              </span>
                            ))}
                          </div>
                        )
                      }
                      return <span style={{ width: '100%' }}>{text}</span>
                    }}
                  />
                )
              }
              return '--'
            }
          },
        }),
        props: {
          code: 'OMS_INFORMATION_FAILED_REASON',
          scope: 'ENTRY',
          multiple: 50,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          placeholder: '请选择不通知的材料不合规原因',
          displayRender: (label) => {
            return label.join('/')
          },
        },
      },
    }),
    [],
  )
  return { schema }
}

export default useColumns
