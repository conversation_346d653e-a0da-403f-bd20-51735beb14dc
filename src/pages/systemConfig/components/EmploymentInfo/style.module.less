.employmentInfo {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__result {
    background-color: #fff;
    padding: 24px;
    margin-bottom: 8px;
  }

  &__tip {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 20px;
  }

  &__upload {
    // min-height: 600px;
    background-color: #fff;
    padding: 24px;
  }
}

.main {
  padding: 24px;
  background-color: #fff;
  flex: 1;
  margin-bottom: 10px;
  overflow: auto;
}

.footer {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  background: #fff;
  box-shadow: inset 0px 1px 0px rgb(0 0 0 / 6%);
  border-radius: 0px 0px 2px 2px;
  z-index: 2;
  height: 48px;
  padding-right: 24px;
}
