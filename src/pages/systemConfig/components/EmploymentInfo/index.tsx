// todo 未联调
import React, { useEffect, useState } from 'react'
import { FileImport } from '@galaxy/async-task-component'
import configService, { EmployInfoType, UpdateDataParams } from '@/services/config'
import { Button } from 'antd'
import dayjs from 'dayjs'
import style from './style.module.less'

const EmploymentInfo = () => {
  const [data, setData] = useState<EmployInfoType | null>(null)
  const getConfig = () => {
    return configService.getEmploymentInfo()
  }

  useEffect(() => {
    getConfig().then((res) => {
      setData(res)
    })
  }, [])

  const [editing, setEditing] = useState<boolean>(false)

  const result = (
    <>
      <span>当前配置文件： </span>
      {data ? (
        <>
          <Button type="link">下载最新数据</Button>
          <span>更新时间: {dayjs(data.timeUpdate).format('YYYY.MM.DD HH:mm')}</span>
        </>
      ) : (
        <span>未配置入职办理信息</span>
      )}
    </>
  )

  const view = (
    <>
      <div className={style.main}>{result}</div>
      <div className={style.footer}>
        <Button
          type="primary"
          onClick={() => {
            setEditing(true)
          }}
        >
          编辑
        </Button>
      </div>
    </>
  )

  const edit = (
    <>
      <div className={style.main} style={{ backgroundColor: 'transparent', padding: 0 }}>
        {data && <div className={style.employmentInfo__result}>{result}</div>}
        <div className={style.employmentInfo__upload}>
          <FileImport
            description={
              <div className={style.employmentInfo__tip}>
                <span>注意事项</span>
                <br />
                <span>1. 下载模板或最新数据，按格式要求编辑表格内容后上传。</span>
                <br />
                <span>2. 各字段均为必填，并注意检查数据正确性，否则将会提交失败。</span>
                <br />
                <span>3. 请注意配置不遗漏、不重复，否则将造成补录信息中变量读取有误。</span>
                <br />
                <span>4. 一次最多上传5000条数据。</span>
              </div>
            }
            template={{
              onDownloadTemplate: () => {},
            }}
            taskParams={{
              service: 'admin/recruit',
              taskType: 1,
              taskModule: 'admin-recruit',
              taskArgs: {
                tenantId: 82,
              },
            }}
            oss={{
              service: 'admin/recruit',
            }}
          />
        </div>
      </div>
      <div className={style.footer}>
        <Button
          onClick={() => {
            setEditing(false)
          }}
        >
          取消
        </Button>
        <Button type="primary">提交</Button>
      </div>
    </>
  )

  return <div className={style.employmentInfo}>{editing ? edit : view}</div>
}

export default EmploymentInfo
