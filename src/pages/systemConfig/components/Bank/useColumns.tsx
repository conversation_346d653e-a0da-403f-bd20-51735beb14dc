import { useMemo } from 'react'

export type DataType = {
  code: string // 唯一值
  name: string
  selfEmployeeBank?: string
  thirdEmployeeBank?: string
}

function useColumns() {
  return useMemo(() => {
    return [
      {
        title: '工作地',
        dataIndex: 'name',
        width: 50,
      },
      {
        title: '自营员工开户银行',
        dataIndex: 'selfEmployeeBank',
        width: 250,
        editable: {
          component: 'Input',
          requiredMark: true,
          rules: [
            { required: true, message: '请输入自营员工开户银行' },
            { max: 50, message: '长度限制50' },
          ],
        },
      },
      {
        title: '第三方员工开户银行',
        dataIndex: 'thirdEmployeeBank',
        width: 250,
        editable: {
          component: 'Input',
          requiredMark: true,
          rules: [
            { required: true, message: '请输入第三方员工开户银行' },
            { max: 50, message: '长度限制50' },
          ],
        },
      },
    ]
  }, [])
}

export default useColumns
