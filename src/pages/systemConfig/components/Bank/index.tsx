import React, { useEffect, useState } from 'react'
import { Button, Spin, message } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { service } from '@galaxy/dict'
import { Layout, SchemaForm } from '@amazebird/antd-schema-form'
import configService, { UpdateDataParams } from '@/services/config'
import { isEqual } from 'lodash'
import ReModal from '@/components/ReModal'
import ReButton from '@/components/ReButton'
import QuestionTooltip from '@/components/QuestionTooltip'
import style from './style.module.less'
import useColumns from './useColumns'

type ValueType = {
  code: string
  name?: string
  selfEmployeeBank?: string
  thirdEmployeeBank?: string
}

type CodesType = { name: string; code: string }[]

const Bank = () => {
  const [codes, setCodes] = useState<CodesType>([])
  const [loading, setLoading] = useState<boolean>(true)

  // 获取工作地字典
  useEffect(() => {
    const getCode = async () => {
      const res = await service.getDictionaryItem('BM_73', 'EHR')
      if ('BM_73' in res) {
        const result = res.BM_73.map((item) => ({
          name: item.name,
          code: item.code,
        }))
        setCodes(result)
      }
    }
    getCode()
  }, [])

  const columns = useColumns()

  const [dataSource, setDataSource] = useState<ValueType[]>([])

  const getConfig = async () => {
    const data = await configService.getData({ type: 2 })
    const result: ValueType[] = []
    codes.forEach((item) => {
      const code = item.code

      try {
        const { self_employee_bank: selfEmployeeBank, third_employee_bank: thirdEmployeeBank } =
          JSON.parse(data[code].value)
        result.push({
          ...item,
          selfEmployeeBank,
          thirdEmployeeBank,
        })
      } catch (error) {
        result.push({ ...item })
      }
    })
    setDataSource(result)
  }

  // 获取配置
  useEffect(() => {
    if (codes.length) {
      getConfig().finally(() => {
        setLoading(false)
      })
    }
  }, [codes])

  const [editable, setEditable] = useState<
    | false
    | {
        errorType: 'inline' | 'tooltip'
        onValuesChange?: any
        scrollToFirstError?: boolean
      }
  >(false)

  const action = SchemaTable.createAction()
  const form: any = SchemaForm.createForm()

  const changeEditableState = () => {
    if (editable) {
      // const data = action?.getEditData()
      // 回填前需要设置一下表格数据
      // setDataSource(data)
      action?.cancelEdit()
      setEditable(false)
    } else {
      action?.editTable()
      setEditable({
        errorType: 'inline',
      })
    }
  }

  return (
    <div className={style.bank}>
      <div className={style.main}>
        <Spin spinning={loading}>
          <div className={style.title}>
            <QuestionTooltip
              // iconStyle={{ color: 'var(--primary-color)' }}
              mark="开户银行配置"
              pointTips={[
                '控制给候选人发送短信的{开户银行}字段的短信内容；',
                '当预入职给候选人发送的短信内容中包含了{开户银行}字段内容时，依据候选人的工作地及用工形式，填充所配置的短信内容',
              ]}
              colon
            />
          </div>
          <SchemaTable
            action={action}
            columns={columns}
            dataSource={dataSource}
            editable={editable}
            editForm={form}
            pagination={false}
            rowKey="code"
          />
        </Spin>
      </div>
      <div className={style.footer}>
        <Layout align="left">
          {editable === false ? (
            <Button
              type="primary"
              onClick={() => {
                changeEditableState()
              }}
            >
              编辑
            </Button>
          ) : (
            <div className={style.footer}>
              <Button
                onClick={() => {
                  const data = action?.getEditData()
                  if (isEqual(data, dataSource)) {
                    changeEditableState()
                    return
                  }
                  ReModal.confirm({
                    title: '提示',
                    content: '已修改的配置将不会保存，确定取消吗？',
                    onOk: () => {
                      changeEditableState()
                    },
                  })
                }}
                className={style.footer__button}
              >
                取消
              </Button>
              <ReButton
                type="primary"
                onClick={async () => {
                  await form.validateFields()
                  setLoading(true)
                  try {
                    const data = action?.getEditData()
                    const result: UpdateDataParams[] = []

                    data.forEach((item) => {
                      result.push({
                        type: 2 as const,
                        code: item.code,
                        name: '开户银行配置',
                        value: JSON.stringify({
                          self_employee_bank: item.selfEmployeeBank,
                          third_employee_bank: item.thirdEmployeeBank,
                        }),
                      })
                    })

                    await configService.updateData(result)
                    changeEditableState()
                    message.success('配置信息提交成功')
                    await getConfig()
                  } catch (error) {
                    console.log(error)
                  }
                  setLoading(false)
                }}
              >
                提交
              </ReButton>
            </div>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default Bank
