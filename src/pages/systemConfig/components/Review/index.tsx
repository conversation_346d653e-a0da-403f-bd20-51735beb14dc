import React, { useEffect, useState } from 'react'
import { But<PERSON>, <PERSON>, message } from 'antd'
import { SchemaTable, SchemaTableProps } from '@amazebird/antd-schema-table'
import { Layout, SchemaForm } from '@amazebird/antd-schema-form'
import { service } from '@galaxy/dict'
import configService, { UpdateDataParams } from '@/services/config'
import { isEqual } from 'lodash'
import ReModal from '@/components/ReModal'
import ReButton from '@/components/ReButton'
import QuestionTooltip from '@/components/QuestionTooltip'
import useColumns from './useColumns'
import style from './style.module.less'

type ValueType = {
  code: string
  name?: string
  os?: boolean
  nonOs?: boolean
}

type CodesType = { name: string; code: string }[]

const Review = () => {
  const action = SchemaTable.createAction()
  const form: any = SchemaForm.createForm()

  const [editable, setEditable] = useState<SchemaTableProps['editable']>(false)

  const columns = useColumns()

  const [dataSource, setDataSource] = useState<ValueType[]>([])

  const [codes, setCodes] = useState<CodesType>([])
  const [loading, setLoading] = useState<boolean>(true)

  // 获取用工形式字典
  useEffect(() => {
    const getCode = async () => {
      const res = await service.getDictionaryItem('BM_35', 'EHR')
      if ('BM_35' in res) {
        const result = res.BM_35.map((item) => ({
          name: item.name,
          code: item.code,
        }))
        setCodes(result)
      }
    }
    getCode()
  }, [])

  const getConfig = async () => {
    const data = await configService.getData({ type: 3 })
    const result: ValueType[] = []

    codes.forEach((item) => {
      const code = item.code

      try {
        const { os, non_os: nonOs } = JSON.parse(data[code].value)
        result.push({
          ...item,
          os,
          nonOs,
        })
      } catch (error) {
        result.push({ ...item })
      }
    })
    setDataSource(result)
  }

  // 获取配置
  useEffect(() => {
    if (codes.length) {
      getConfig().finally(() => {
        setLoading(false)
      })
    }
  }, [codes])

  const changeEditableState = () => {
    if (editable) {
      action?.cancelEdit()
      setEditable(false)
    } else {
      action?.editTable()
      setEditable({
        errorType: 'inline',
      })
    }
  }

  return (
    <div className={style.review}>
      <div className={style.main}>
        <Spin spinning={loading}>
          <div className={style.title}>
            <QuestionTooltip
              // iconStyle={{ color: 'var(--primary-color)' }}
              mark="复核规则配置"
              pointTips={[
                '控制候选人是否需要预入职复核；',
                '当候选人预入职初审通过/不通过时，依据用工形式及OS序列判断候选人是否需要预入职复核，当候选人的用工形式及OS序列对应的结果为需复核时，则该候选人会进入复核流程。',
              ]}
              colon
            />
          </div>
          <SchemaTable
            action={action}
            columns={columns}
            dataSource={dataSource}
            editable={editable}
            editForm={form}
            pagination={false}
            rowKey="code"
          />
        </Spin>
      </div>
      <div className={style.footer}>
        <Layout align="left">
          {editable === false ? (
            <Button
              type="primary"
              onClick={() => {
                changeEditableState()
              }}
            >
              编辑
            </Button>
          ) : (
            <div className={style.footer}>
              <Button
                onClick={() => {
                  const data = action?.getEditData()
                  if (isEqual(data, dataSource)) {
                    changeEditableState()
                    return
                  }
                  ReModal.confirm({
                    title: '提示',
                    content: '已修改的配置将不会保存，确定取消吗？',
                    onOk: () => {
                      changeEditableState()
                    },
                  })
                }}
                className={style.footer__button}
              >
                取消
              </Button>
              <ReButton
                type="primary"
                onClick={async () => {
                  await form.validateFields()
                  setLoading(true)

                  try {
                    const data = action?.getEditData()
                    const result: UpdateDataParams[] = []

                    data.forEach((item) => {
                      result.push({
                        type: 3 as const,
                        code: item.code,
                        name: '复核规则配置',
                        value: JSON.stringify({
                          os: item.os,
                          non_os: item.nonOs,
                        }),
                      })
                    })

                    await configService.updateData(result)
                    changeEditableState()
                    message.success('配置信息提交成功')
                    await getConfig()
                  } catch (error) {
                    console.log(error)
                  }
                  setLoading(false)
                }}
              >
                提交
              </ReButton>
            </div>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default Review
