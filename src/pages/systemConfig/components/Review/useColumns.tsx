import { useMemo } from 'react'

export type DataType = {
  code: string // 唯一值
  name: string
  os: boolean
  nonOs: boolean
}

function useColumns() {
  return useMemo(() => {
    return [
      {
        title: '用工形式',
        dataIndex: 'name',
        width: 50,
      },
      {
        title: 'OS序列复核规则',
        dataIndex: 'os',
        component: 'Select',
        width: 250,
        options: [
          {
            label: '复核',
            value: true,
          },
          {
            label: '无需复核',
            value: false,
          },
        ],
        editable: {
          component: 'Select',
          options: [
            {
              label: '复核',
              value: true,
            },
            {
              label: '无需复核',
              value: false,
            },
          ],
          props: {
            allowClear: false,
          },
          requiredMark: true,
          rules: [{ required: true, message: '请选择OS序列复核规则' }],
        },
      },
      {
        title: '非OS序列复核规则',
        dataIndex: 'nonOs',
        component: 'Select',
        width: 250,
        options: [
          {
            label: '复核',
            value: true,
          },
          {
            label: '无需复核',
            value: false,
          },
        ],
        editable: {
          component: 'Select',
          options: [
            {
              label: '复核',
              value: true,
            },
            {
              label: '无需复核',
              value: false,
            },
          ],
          props: {
            allowClear: false,
          },
          requiredMark: true,
          rules: [{ required: true, message: '请选择非OS序列复核规则' }],
        },
      },
    ]
  }, [])
}

export default useColumns
