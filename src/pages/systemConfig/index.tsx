import { Layout, Menu } from 'antd'
import type { MenuProps } from 'antd'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import asyncComponent from '@/utils/AsyncComponent'
import dmsService from '@/services/dms'
import { SETTING } from '@/constants/dms/pageName'
import useTimer, { CallbackType } from '@/hooks/useTimer'
import { PAGE_VIEW } from '@/constants/dms/eventName'
import style from './style.module.less'
import Review from './components/Review'

const { Sider, Content } = Layout

const Rule = asyncComponent(() => import('@/pages/systemConfig/components/Rule'))

const Log = asyncComponent(() => import('@/pages/systemConfig/components/Log'))

const Bank = asyncComponent(() => import('@/pages/systemConfig/components/Bank'))

const TimeRule = asyncComponent(() => import('@/pages/systemConfig/components/TimeRule'))

// const EmploymentInfo = asyncComponent(
//   () => import('@/pages/systemConfig/components/EmploymentInfo'),
// )

const config = [
  {
    key: 'rules',
    label: '规则配置',
    component: <Rule />,
  },
  {
    key: 'timeRules',
    label: '时间规则配置',
    component: <TimeRule />,
  },
  {
    key: 'bank',
    label: '开户银行配置',
    component: <Bank />,
  },
  {
    key: 'recheck',
    label: '复核规则配置',
    component: <Review />,
  },
  // 需求变更，暂时注释掉（未联调，请勿简单开启）
  // {
  //   key: 'employmentInfo',
  //   label: '入职办理信息配置',
  //   component: <EmploymentInfo />,
  // },
  {
    key: 'log',
    label: '配置更改日志',
    component: <Log />,
  },
]

const SystemConfig = () => {
  const [key, setKey] = useState<string>(() => {
    return config[0].key
  })

  useEffect(() => {
    dmsService.injectPageContext({
      pageName: SETTING,
      obmSettingTab: key,
    })
  }, [key])

  const pageView = useCallback<CallbackType>(({ duration, start, end }) => {
    dmsService.trace(PAGE_VIEW, {
      timeBehavior: end,
      pageStayTime: duration,
      timeBehaviour__when__: start,
    })
  }, [])
  useTimer(pageView)

  const handleSelect: MenuProps['onSelect'] = ({ key: $key }) => {
    setKey($key)
  }

  const content = useMemo(() => {
    const conf = config.find((item) => item.key === key)
    const component = conf?.component ?? <div>未找到组件</div>
    return component
  }, [key])

  return (
    <Layout className={style.layout}>
      <Sider theme="light" className={style.sider}>
        <Menu
          selectedKeys={[key]}
          mode="inline"
          theme="light"
          items={config}
          onSelect={handleSelect}
        />
      </Sider>
      <Content>
        <div className={style.content}>{content}</div>
      </Content>
    </Layout>
  )
}

export default SystemConfig
