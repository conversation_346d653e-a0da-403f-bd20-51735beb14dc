import InsertText from '@/components/InsertText'
import {
  Item,
  Reset,
  SchemaForm,
  SchemaType,
  Submit,
  Layout,
  Observer,
} from '@amazebird/antd-schema-form'
import React, { useEffect, useMemo } from 'react'
import '@amazebird/antd-field'
import { v4 } from 'uuid'
import { Card } from 'antd'
import Modal from '@/components/ReModal'
import { useNavigate } from 'react-router-dom'
import { TreeSelect as Dict } from '@galaxy/dict'
import templateService, { DataType } from '@/services/template'
import QuestionTooltip from '@/components/QuestionTooltip'
import { compare } from '@/utils/utils'
import style from './style.module.less'

export type { DataType }

export enum MODE {
  ADD,
  EDIT,
}

type Iprops = {
  defaultValue?: Partial<DataType>
  loading?: boolean
  mode: MODE
}

const Form: React.FC<Iprops> = ({ defaultValue, loading, mode }) => {
  const form = SchemaForm.createForm()

  useEffect(() => {
    if (defaultValue) {
      form.setFieldsValue(defaultValue)
    }
  }, [])

  const schema: SchemaType = useMemo(() => {
    const smsContentId = `smsContent-${v4()}`
    return {
      id: {
        component: 'Text',
      },
      name: {
        label: '模版名称',
        component: 'Input',
        required: true,
        props: {
          maxLength: 40,
        },
      },
      typeCode: {
        label: '模板类型',
        component: Dict,
        disabled: mode === MODE.EDIT,
        props: {
          code: 'OMS_TEMPLATE_TYPE',
          scope: 'ENTRY',
          placeholder: '请选择模板类型',
          allowClear: true,
        },
        required: true,
      },
      content: {
        label: (
          <QuestionTooltip
            mark="短信内容"
            pointTips={[
              '至多输入1000字符，支持中英文、大小写、标点符号；',
              '不支持【】，可能会与签名混淆；',
              '暂不支持如★、 ※、 →、 ●等特殊符号，可能会导致短信出现乱码。',
            ]}
          />
        ),
        component: 'Input.TextArea',
        props: {
          id: smsContentId,
          rows: 8,
          allowClear: true,
          maxLength: 1000,
        },
        required: [true, '请输入短信内容'],
      },
      insertText: {
        label: '插入字段',
        component: InsertText,
        props: Observer({
          watch: 'typeCode',
          action: (value) => {
            return {
              getContainer: () => {
                return document.getElementById(smsContentId)
              },
              onInsert: (text) => {
                form.setFieldsValue({
                  content: text,
                })
              },
              tags: [
                {
                  label: '应聘者姓名',
                  value: '{应聘者姓名}',
                },
                {
                  label: '报到岗位',
                  value: '{报到岗位}',
                },
                {
                  label: '预计报到日期',
                  value: '{预计报到日期}',
                },
                {
                  label: '工作城市',
                  value: '{工作城市}',
                },
                {
                  label: '合作公司全称',
                  value: '{合作公司全称}',
                },
                {
                  label: '入职小程序链接',
                  value: '{入职小程序链接}',
                },
                {
                  label: '发送日期',
                  value: '{发送日期}',
                },
              ].concat(
                // 仅在「审核通过通知模板」中可用。
                value === 'OMS_TT_005'
                  ? {
                      label: '入职办理地点',
                      value: '{入职办理地点}',
                    }
                  : [
                      {
                        label: '补录信息完成时间',
                        value: '{补录信息完成时间}',
                      },
                      {
                        label: '开户银行',
                        value: '{开户银行}',
                      },
                    ],
              ),
            }
          },
        }),
      },
    }
  }, [])

  const navigate = useNavigate()

  // 取消
  const handleCancel = () => {
    const value = form.getFieldsValue()
    // 检查是否有修改过
    if (compare(defaultValue, value)) {
      Modal.confirm({
        title: '提示',
        content: '短信模板未保存，确定取消吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          navigate(-1)
        },
      })
    } else {
      navigate(-1)
    }
  }

  return (
    <Card loading={loading} className={style.formContainer}>
      <SchemaForm schema={schema} form={form} initialValues={defaultValue}>
        <Item hidden field="id" />
        <Item field="name" />
        <Item field="typeCode" />
        <Item field="content" />
        <Item field="insertText" />
        <div className={style.footer}>
          <Layout align="left">
            <Reset htmlType="button" style={{ marginLeft: 40 }} onClick={handleCancel}>
              取消
            </Reset>
            <Submit
              onFinish={async () => {
                const data = await form.validateFields()
                await templateService.update(data)
                navigate(-1)
              }}
            />
          </Layout>
        </div>
      </SchemaForm>
    </Card>
  )
}

export default Form
