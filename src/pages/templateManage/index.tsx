import { SchemaTable } from '@amazebird/antd-schema-table'
import React, { useEffect } from 'react'
import { Button } from 'antd'
import { useActivate } from '@/components/cacheRoute'
import templateService from '@/services/template'
import { useNavigate } from 'react-router-dom'
import { useStore } from '@/stores'
import useColumns from './useColumns'
import style from './style.module.less'

const TemplateManage: React.FC = () => {
  const action = SchemaTable.createAction()
  const [searchColumns, columns] = useColumns(action)

  const { setExtra } = useStore()

  const navigate = useNavigate()

  useEffect(() => {
    setExtra([
      <Button
        key="add"
        type="primary"
        onClick={() => {
          navigate('add')
        }}
      >
        新建模板
      </Button>,
    ])

    return () => {
      setExtra([])
    }
  }, [])

  useActivate(() => {
    action?.refresh()
  })

  return (
    <div className={style.templateManage}>
      <SchemaTable
        action={action}
        columns={columns}
        request={async ({ pagination, filter }) => {
          const res = await templateService.getList({
            page: pagination.current,
            size: pagination.pageSize,
            ...filter,
          })
          return res
        }}
        searchColumns={searchColumns}
      />
    </div>
  )
}

export default TemplateManage
