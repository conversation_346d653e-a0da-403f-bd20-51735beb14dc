import DictText from '@/components/DictText'
import { SchemaColumnType, SchemaTableProps } from '@amazebird/antd-schema-table'
import { TreeSelect as Dict } from '@galaxy/dict'
import { Permission } from '@galaxy/rbac'
import { Button } from 'antd'
import React, { useMemo } from 'react'
import type { TemplateData } from '@/services/template'
import ReModal from '@/components/ReModal'
import templateService from '@/services/template'
import type { Action } from '@amazebird/antd-schema-table/es/core/createAction'
import { useNavigate } from 'react-router-dom'
import RouterService from '@/services/router'

function useColumns(action: Action): [SchemaTableProps['searchColumns'], SchemaColumnType] {
  const search = useMemo<SchemaTableProps['searchColumns']>(() => {
    return [
      {
        title: '模板名称',
        dataIndex: 'name',
        component: 'Input',
        props: {
          maxLength: 40,
          allowClear: true,
        },
      },
      {
        title: '模板类型',
        dataIndex: 'typeCode',
        component: Dict,
        placeholder: '请选择模板类型',
        props: {
          code: 'OMS_TEMPLATE_TYPE',
          scope: 'ENTRY',
          allowClear: true,
        },
      },
    ]
  }, [])

  const navigate = useNavigate()

  const table = useMemo<SchemaColumnType>(() => {
    return [
      {
        title: '模板ID',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '模板名称',
        dataIndex: 'name',
        width: 650,
      },
      {
        title: '模板类型',
        dataIndex: 'typeName',
        cell: ({ text }) => {
          return <DictText text={text} />
        },
        width: 200,
      },
      {
        title: '创建时间',
        dataIndex: 'timeCreate',
        width: 200,
        cell: 'DateTime',
      },
      {
        title: '操作',
        key: 'operator',
        width: 100,
        align: 'left',
        fixed: 'right',
        cell: ({ record }: { record: TemplateData }) => {
          return [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                if (typeof record.id === 'undefined') {
                  return
                }
                navigate(
                  RouterService.path('/templateManage/edit', {
                    id: `${record.id}`,
                  }),
                )
              }}
            >
              编辑
            </Button>,
            <Button
              key="del"
              type="link"
              size="small"
              onClick={() => {
                if (record.isUsing) {
                  ReModal.error({
                    title: '删除失败',
                    content: `该模板已被${record.configureName}使用，请更换使用模板后删除`,
                  })
                  return
                }
                ReModal.confirm({
                  title: '删除提示',
                  content: `模板删除后不可恢复，确定要删除模板 ${record.name}吗？`,
                  onOk: async () => {
                    await templateService
                      .delete(record.id)
                      .then(() => {
                        action.refresh({
                          resetPage: true,
                        })
                      })
                      .catch(() => {})
                  },
                })
              }}
              // style={{ color: 'var(--dhr-color-yellow)' }}
            >
              删除
            </Button>,
          ]
        },
      },
    ]
  }, [])

  return [search, table]
}

export default useColumns
