import React, { useEffect, useMemo, useState } from 'react'
import '@amazebird/antd-field'
import { useSearchParams } from 'react-router-dom'
import TemplateService from '@/services/template'
import Form, { MODE } from '../components/form'
import type { DataType } from '../components/form'

const Edit: React.FC = () => {
  const [searchParams] = useSearchParams()

  const [defaultValue, setDefaultValue] = useState<DataType>()
  const [loading, setLoading] = useState(true)

  const id = useMemo<number | undefined>(() => {
    const idstr = searchParams.get('id')
    if (idstr) {
      const result = Number(idstr)
      if (!Number.isNaN(result)) {
        return result
      }
    }
    return undefined
  }, [])

  useEffect(() => {
    if (typeof id === 'number') {
      TemplateService.get(id)
        .then((data) => {
          setDefaultValue(data)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [id])

  return <Form defaultValue={defaultValue} loading={loading} mode={MODE.EDIT} />
}

export default Edit
