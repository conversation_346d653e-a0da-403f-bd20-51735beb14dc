import { getPermission } from '@galaxy/rbac'
import { CompletedTimeExportBtn, CompletedExportBtn } from '@/constants/rbac/preEntry'
import userService from '@/services/user'
import BaseConfig from './BaseConfig'

class CompletedConfig extends BaseConfig {
  timeDetailExportContent = '【已完成】tab数据：'

  persistenceKey: string = `entry::preEntry::completedList::${
    userService.info?.num ?? userService.info?.userName
  }`

  getColumnsState = () => {
    return {
      persistenceType: 'localStorage' as const,
      persistenceKey: this.persistenceKey,
      defaultValue: {
        // offer 适用模板
        offerTemplateName: {
          show: false,
        },
        // 招聘负责人
        recruitResponsiblePerson: {
          show: false,
        },
        // 职级
        postRankName: {
          show: false,
        },
        // 试用期期限
        trialPeriodName: { show: false },
        // 结束招聘原因
        endRecruitReasonName: {
          show: false,
        },
        contactPhone: {
          show: false,
        },
        recruitTypeName: {
          show: false,
        },
        timeCreate: {
          show: false,
        },
        entryCityName: {
          show: false,
        },
        entryPlaceName: {
          show: false,
        },
        entryPlaceAddress: {
          show: false,
        },
      },
    }
  }

  checkTimeDetailExportPermission = () => {
    return getPermission(CompletedTimeExportBtn)
  }

  checkExportPermission = () => {
    return getPermission(CompletedExportBtn)
  }
}

export default CompletedConfig
