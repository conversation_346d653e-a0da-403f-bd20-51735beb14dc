import { Item, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { message } from 'antd'
import React, { useImperativeHandle, useMemo, useState } from 'react'
import dmsService from '@/services/dms'
import materialService from '@/services/preEntry/material'
import templateService from '@/services/template'
import { RESEND_MODIFY_SMS } from '@/constants/dms/eventName'
import { createAction } from '@amazebird/antd-schema-table'
import ExtraDateFooter from '@/components/ExtraDateFooter'
import moment from 'moment'
import ReModal from '@/components/ReModal'

type Iprops = {
  action: ReturnType<typeof createAction>
  id: number
}

export type ActionType = {
  open: () => void
}

// 重发修改通知
const Resend: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, id }, ref) => {
  const [open, setOpen] = useState<boolean>(false)

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
  }))

  const form = SchemaForm.createForm()

  const disabledDate = (currentDate: moment.Moment) => {
    // 当天
    const currentDay = moment().startOf('day')
    // 365天
    const lastDay = moment().add(365, 'day').endOf('day')
    return moment(currentDate).isBefore(currentDay) || moment(currentDate).isAfter(lastDay)
  }

  const schema = useMemo<SchemaType>(() => {
    return {
      templateId: {
        label: '信息修改模板',
        component: 'Select',
        options: async () => {
          const { data } = await templateService
            .getList({
              typeCode: 'OMS_TT_002',
              noPage: true,
            })
            .catch(() => {
              return { data: [] }
            })
          if (data.length > 0) {
            // 默认选中第一个
            form.setFieldsValue({
              templateId: data[0].id,
            })
          }
          return data.map((item) => ({
            value: item.id,
            label: item.name,
          }))
        },
        required: true,
      },
      endTime: {
        label: '完成日期',
        component: 'DatePicker',
        required: true,
        props: {
          showTime: { format: 'HH:mm' },
          showNow: false,
          format: 'YYYY-MM-DD HH:mm',
          disabledDate,
          showToday: false,
          renderExtraFooter: () => (
            <ExtraDateFooter
              onClick={(targetDate) => {
                if (!disabledDate(targetDate)) {
                  form.setFieldsValue({
                    endTime: targetDate,
                  })
                }
              }}
            />
          ),
          style: { width: '100%' },
        },
      },
    }
  }, [])

  return (
    <div>
      <ReModal
        title="重发修改短信"
        open={open}
        onCancel={() => {
          setOpen(false)
        }}
        onOk={async () => {
          const data = await form.validateFields()
          const isSuccess = await materialService
            .resendModifySMS(
              {
                id,
                templateId: data.templateId,
                endTime: moment(data.endTime).valueOf(),
              },
              {
                noMessage: true,
              },
            )
            .catch(() => {
              return false
            })
          if (isSuccess) {
            message.success('信息修改短信已重发')
            // 提交成功后
            dmsService.trace(RESEND_MODIFY_SMS)
            setOpen(false)
          } else {
            message.error('信息修改短信重发失败，请稍后再试')
          }
        }}
        width={600}
      >
        <SchemaForm form={form} schema={schema}>
          <Item field="templateId" />
          <Item field="endTime" />
        </SchemaForm>
      </ReModal>
    </div>
  )
})

export default Resend
