import { Item, Observer, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { createAction } from '@amazebird/antd-schema-table'
import { message } from 'antd'
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react'
import { Upload } from '@galaxy/upload-component'
import auditService from '@/services/preEntry/audit'
import ReModal from '@/components/ReModal'

type Iprops = {
  action: ReturnType<typeof createAction>
  content: string
  id: number
}

export type ActionType = {
  open: () => void
  push: () => Promise<void>
}

const Push: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, content, id }, ref) => {
  const form = SchemaForm.createForm()
  const [open, setOpen] = useState<boolean>(false)

  const push = async () => {
    await auditService.pushResult({
      id,
    })
    message.success('候选人信息已下发')
    setOpen(false)
    action.refresh()
  }

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
    push,
  }))

  const schema = useMemo<SchemaType>(() => {
    return {
      result: {
        label: '预入职审核结果',
        component: 'Radio',
        required: true,
        options: [
          {
            label: '不通过',
            value: 1,
          },
          {
            label: '特批通过',
            value: 2,
          },
        ],
      },
      screenshot: {
        label: '单据截图',
        component: Upload,
        required: true,
        props: {
          accept: 'image/png,image/jpeg,image/webp',
          listType: 'picture-card',
          fileSizeLimit: 5,
          multiple: true,
          maxNum: 5,
          oss: {
            service: '/admin/entry',
          },
          remark: '仅支持.png .jpg .jpeg .webp格式，单张5M以内',
        },
        visible: Observer({
          watch: 'result',
          action: (result) => {
            if (result === 2) {
              return true
            }
            return false
          },
        }),
      },
      tip: {
        label: '说明',
        component: 'Input.TextArea',
        props: {
          maxLength: 100,
        },
        visible: Observer({
          watch: 'result',
          action: (result) => {
            if (result === 2) {
              return true
            }
            return false
          },
        }),
      },
    }
  }, [])

  return (
    <div>
      <ReModal
        title="审核结果推送"
        open={open}
        onCancel={() => {
          setOpen(false)
        }}
        onOk={async () => {
          const data = await form.validateFields()
          const { result } = await auditService.pushResult({
            id,
            auditResult: data.result,
            remark: data.tip,
            screenshots: data.screenshot?.map?.((item) => ({
              attachmentType: 0,
              fileName: item.name,
              url: item.uuid,
            })),
          })
          if (result === 2) {
            message.success('提交成功！该候选人有五险一金代缴流程尚未归档，归档后将自动下发数据')
          } else {
            message.success('候选人信息已下发')
          }
          setOpen(false)
          action.refresh()
        }}
        width={600}
      >
        <SchemaForm
          initialValues={{ preserve: true }}
          form={form}
          schema={schema}
          labelCol={{
            xs: { span: 24 },
            sm: { span: 5 },
          }}
          wrapperCol={{
            xs: { span: 24 },
            sm: { span: 19 },
          }}
        >
          {content}
          <Item field="result" />
          <Item field="screenshot" />
          <Item field="tip" />
        </SchemaForm>
      </ReModal>
    </div>
  )
})

export default Push
