import { SchemaColumnType, SchemaTableProps, createAction } from '@amazebird/antd-schema-table'
import { ArrayElement } from '@/types/common'
import { STASTUS_COLOR } from '@galaxy/dhr-style'
import { IsE<PERSON>ry, IsNeedAudit, ProcessProgress } from '@/services/preEntry/list'
import { Badge, Button, Tooltip } from 'antd'
import React from 'react'
import { omit } from 'lodash-es'
import SpecialPass from '@/components/SpecialPass'
import DictText from '@/components/DictText'
import { InfoCircleOutlined } from '@ant-design/icons'
import RouterService from '@/services/router'
import { NavigateFunction } from 'react-router-dom'
import { Permission, getPermission } from '@galaxy/rbac'
import {
  CompletedCheckBtn,
  CompletedDepartmentSearch,
  CompletedLogBtn,
  ModifyEmploymentInfoBtn,
  TraceRecordBtn,
} from '@/constants/rbac/preEntry'
import { VIEW_CANDIDATE } from '@/constants/dms/eventName'
import dmsService from '@/services/dms'
import ModifyInformationModal, {
  ActionType as ModifyActionType,
} from '@/components/ModifyInformation'
import { OTHER, ENTRY_PLACE_NAME_TYPE, FOURTH_DEPARTMENT_CATEGORY } from '@/constants'
import BaseConfig, { TrialPeriodMap, emptyText } from './BaseColumnConfig'
import TraceRecord, { ActionType as TraceRecordActionType } from './TraceRecord'
import MoreDropdown from '../../../MoreDropdown'
import style from '../../style.module.less'

const completeSituationOptions = [
  {
    label: '按时完成',
    value: 1,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '超时完成',
    value: 2,
    color: STASTUS_COLOR.RED,
  },
]

const processProgressOptions = [
  {
    label: '审核不通过',
    value: ProcessProgress.ReviewFailed,
    color: STASTUS_COLOR.RED,
  },
  {
    label: '审核通过',
    value: ProcessProgress.Approved,
    color: STASTUS_COLOR.BLUE,
  },
  {
    label: '特批通过',
    value: ProcessProgress.SpecialApproval,
    color: STASTUS_COLOR.BLUE,
  },
  {
    label: '结束预入职',
    value: ProcessProgress.EndRecruitment,
    color: STASTUS_COLOR.RED,
  },
]

class CompletedConfig extends BaseConfig {
  column: Map<string, ArrayElement<SchemaColumnType>> = new Map()

  searchColumn: Map<string, ArrayElement<SchemaTableProps['searchColumns']>> = new Map()

  modifyRef: Record<string, ModifyActionType> = {}

  traceRecordRef: Record<string, TraceRecordActionType> = {}

  constructor(config: {
    action: ReturnType<typeof createAction>
    navigate: NavigateFunction
    records: any[]
  }) {
    super({ ...config, type: 1 })
    this.initSearchColumn()
    this.initColumn()
  }

  protected initSearchColumn() {
    super.initSearchColumn()
    this.searchColumn.set('processProgresses', {
      dataIndex: 'processProgresses',
      title: '处理结果',
      component: 'Select',
      placeholder: '请选择处理结果',
      options: processProgressOptions,
      props: {
        mode: 'multiple',
        labelInValue: true,
      },
    })
    this.searchColumn.set('completeSituation', {
      dataIndex: 'completeSituation',
      title: '完成情况',
      component: 'Select',
      placeholder: '请选择完成情况',
      options: completeSituationOptions,
      props: {
        allowClear: true,
        labelInValue: true,
      },
    })
  }

  protected initColumn() {
    super.initColumn()
    this.column.set('processProgress', {
      dataIndex: 'processProgress',
      title: '处理结果',
      cell: ({ text, record }) => {
        const config = processProgressOptions.find((item) => item.value === text)
        // 找不到配置项，text值异常，暂时返回emptyText
        if (!config) {
          return emptyText
        }
        // 特批通过，特殊处理
        if (text === 7) {
          return (
            <Badge
              color={config.color}
              text={
                <span style={{ color: config.color }}>
                  <SpecialPass id={record.id} />
                </span>
              }
            />
          )
        }
        return (
          <Badge
            color={config.color}
            text={<span style={{ color: config.color }}>{config.label}</span>}
          />
        )
      },
      width: 150,
    })
    this.column.set('endRecruitReasonName', {
      dataIndex: 'endRecruitReasonName',
      title: '结束预入职原因',
      cell: ({ text, record }) => {
        return (
          <DictText
            text={text}
            render={(content) => {
              if (record.endRecruitReasonRemark) {
                return (
                  <span>
                    <span>{content}</span>
                    <Tooltip title={record.endRecruitReasonRemark}>
                      <InfoCircleOutlined className={style.endRecruit__remark} />
                    </Tooltip>
                  </span>
                )
              }
              return <span>{content}</span>
            }}
          />
        )
      },
      width: 150,
    })
    this.column.set('timeAuditComplete', {
      dataIndex: 'timeAuditComplete',
      title: '审核完成时间',
      cell: {
        type: 'DateTime',
        props: {
          format: 'YYYY-MM-DD HH:mm',
        },
      },
      sorter: true,
      width: 150,
    })
    this.column.set('completeSituation', {
      dataIndex: 'completeSituation',
      title: '完成情况',
      cell: 'Status',
      options: completeSituationOptions,
      width: 100,
    })
    this.column.set('operator', {
      title: '操作',
      key: 'operator',
      width: 200,
      align: 'left' as const,
      fixed: 'right' as const,
      cell: ({ record }) => {
        return [
          <Permission key="check" code={CompletedCheckBtn}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsService.trace(VIEW_CANDIDATE)
                this.navigate(
                  RouterService.path(`/preEntry/entryMaterialsAuditForm`, {
                    id: record.id,
                    listIds: this.records
                      .filter(
                        (item) =>
                          item.isEntry === IsEntry.NO && item.isNeedAudit === IsNeedAudit.YES,
                      )
                      .map((item) => item.id)
                      .join(','),
                  }),
                )
              }}
              disabled={!(record.isEntry === IsEntry.NO && record.isNeedAudit === IsNeedAudit.YES)}
            >
              查看
            </Button>
          </Permission>,
          record.dataSource === 'employment' ? (
            <Permission key="modify" code={ModifyEmploymentInfoBtn}>
              <>
                <Button
                  type="link"
                  size="small"
                  // 同时满足以下条件：1. 「候选人状态」=“预入职审核中“ || "待入职" 2. 该预入职条目是同候选人id的最新条目
                  disabled={
                    !(
                      record.isPositionCandidateNewestEntryMaterial &&
                      ['ATS_CS_035', 'ATS_CS_036'].includes(record.positionCandidateStatus)
                    )
                  }
                  onClick={() => {
                    this.modifyRef[record.id]?.open?.()
                  }}
                >
                  修改录用信息
                </Button>
                <ModifyInformationModal
                  action={this.action}
                  dataSource={{
                    entryDepartmentId: {
                      value: record.departmentId,
                      label: record.departmentName,
                    },
                    entryPositionId: {
                      value: record.positionId,
                      label: record.positionName,
                      positionBaseId: record.positionBaseId,
                      areaCode: record.areaCode,
                      partnerCompany: record.cooperateCompanyCode,
                      fourthDepartmentCategory: record.fourthDepartmentCategory,
                      fourthDepartmentName: record.fourthDepartmentName,
                    },
                    employmentFormCode: record.employmentFormCode,
                    employeeTypeCode: record.employmentTypeCode,
                    rankCode: record.postRankCode,
                    probationPeriod: TrialPeriodMap[record.trialPeriodCode as string],
                    timeExpectedEntry: record.timeEntry || undefined, // timeEntry为0的时候，要处理成undefined,
                    entryCityCode: record.entryCityCode,
                    recruitTypeCode: record?.recruitTypeName?.code,
                    entryPlaceName:
                      record?.entryPlaceNameType === ENTRY_PLACE_NAME_TYPE.OTHER
                        ? [`${ENTRY_PLACE_NAME_TYPE.OTHER}`, record?.entryPlaceName]
                        : [record?.entryPlaceName],
                    entryPlaceAddress: record?.entryPlaceAddress,
                    fourthDepartmentCategory: record.fourthDepartmentCategory,
                    fourthDepartmentName: record.fourthDepartmentName,
                  }}
                  entryMaterialId={record.id}
                  isCampus={record.isCampus}
                  isVirtualPosition={record.isVirtualPosition}
                  validateErrorMsg="录用信息校验不通过，修改信息失败。若您希望继续修改信息，请在ehr操作退回录用，并通知招聘负责人在录用管理中重新发起录用。"
                  ref={(node) => {
                    if (node) {
                      this.modifyRef[record.id] = node
                    }
                  }}
                />
              </>
            </Permission>
          ) : undefined,
          (getPermission(TraceRecordBtn) || getPermission(CompletedLogBtn)) && (
            <MoreDropdown
              key="more"
              menu={{
                // @ts-ignore 已经filter掉undefined场景
                items: [
                  {
                    key: 9,
                    label: '过程跟踪记录',
                    onClick: async () => {
                      this.traceRecordRef[record.id]?.open?.()
                    },
                    hasPermission: getPermission(TraceRecordBtn),
                  },
                  {
                    key: 6,
                    label: '操作记录',
                    onClick: () => {
                      this.navigate(`log?id=${record.id}&title=${encodeURIComponent(record.name)}`)
                    },
                    hasPermission: getPermission(CompletedLogBtn),
                  },
                ]
                  .filter((item) => !!item)
                  .filter((item) => item?.hasPermission)
                  .map((item) => omit(item, 'hasPermission')),
              }}
            />
          ),
          <TraceRecord
            key="traceRecord"
            action={this.action}
            id={record.id}
            ref={(node) => {
              if (node) {
                this.traceRecordRef[record.id] = node
              }
            }}
            content={record.entryMaterialProcessTrackInfo}
          />,
        ]
      },
    })
  }

  getSearchColumns = (): SchemaTableProps['searchColumns'] => {
    return [
      this.searchColumn.get('ehrCandidateId'), // EHR人才ID
      this.searchColumn.get('name'), // 姓名
      getPermission(CompletedDepartmentSearch) && this.searchColumn.get('departmentId'), // 预计报到部门
      this.searchColumn.get('positionCandidateId'), // DHR候选人ID
      this.searchColumn.get('positionName'), // 报到岗位
      this.searchColumn.get('recruitResponsibleUserNums'), // 招聘负责人
      this.searchColumn.get('areaCode'), // 所属区域
      this.searchColumn.get('entryCityCodes'), // 入职办理城市
      this.searchColumn.get('entryPlaceName'), // 入职办理地简称
      this.searchColumn.get('employmentFormCodes'), // 用工形式
      this.searchColumn.get('timeEntry'), // 预计报到日期
      this.searchColumn.get('enterEntryTime'), // 进入预入职日期
      this.searchColumn.get('recruitTypeCode'), // 招聘类型
      this.searchColumn.get('processProgresses'), // 处理进度
      this.searchColumn.get('completeSituation'), // 超时状态
      this.searchColumn.get('isPpTrainee'), // 是否朴培生
    ].filter((item) => !!item) as SchemaTableProps['searchColumns']
  }

  getColumns = (): SchemaColumnType => {
    return [
      this.column.get('positionCandidateId'), // DHR候选人ID
      this.column.get('name'), // 姓名
      this.column.get('departmentName'), // 预计报到部门
      this.column.get('ehrCandidateId'), // EHR人才ID
      this.column.get('contactPhone'), // 联系方式
      this.column.get('positionName'), // 报到岗位
      this.column.get('recruitResponsiblePerson'), // 招聘负责人
      this.column.get('areaName'), // 所属区域
      this.column.get('contractEntityName'), // 合同主体
      this.column.get('offerTemplateName'), // offer适用模板
      this.column.get('entryCityName'), // 入职办理城市
      this.column.get('recruitTypeName'), // 招聘类型
      this.column.get('employmentFormName'), // 用工形式
      this.column.get('employmentTypeName'), // 员工类型
      this.column.get('employmentStateName'), // 人员类别
      this.column.get('postLevelName'), // 职位
      this.column.get('postRankName'), // 职级
      this.column.get('trialPeriodName'), // 试用期期限
      this.column.get('timeEntry'), // 预计报到日期
      this.column.get('entryPlaceName'), // 入职办理地简称
      this.column.get('entryPlaceAddress'), // 入职办理地点
      this.column.get('processProgress'), // 处理进度
      getPermission(TraceRecordBtn) && this.column.get('entryMaterialProcessTrackInfo'), // 过程跟踪记录
      this.column.get('endRecruitReasonName'), // 结束招聘原因
      this.column.get('timeCreate'), // 进入预入职日期
      this.column.get('timeAuditComplete'), // 审核完成时间
      this.column.get('completeSituation'), // 完成情况
      this.column.get('jobDuration'), // 作业净时长
      this.column.get('spentTotalTime'), // 耗费总时长
      this.column.get('operator'), // 操作
    ].filter((item) => !!item) as SchemaColumnType
  }
}

export default CompletedConfig
