import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons'
import React, { useEffect, useState } from 'react'
import { desensitization } from '@/utils/utils'
import subEvent from './subEvent'
import style from './style.module.less'

type Iprops = {
  id: string // 相同id只允许显示一个手机号
  value: string // 明文手机号
}

const ShowPhone: React.FC<Iprops> = ({ id, value }) => {
  const [show, setShow] = useState(false)

  useEffect(() => {
    subEvent.on(id, () => {
      setShow(false)
    })
  }, [])

  if (show) {
    return (
      <>
        {value}{' '}
        <span
          className={style.eyeOutlined}
          onClick={() => {
            setShow(false)
          }}
        />
      </>
    )
  }

  return (
    <>
      {desensitization(value, 3, -4)}{' '}
      <span
        className={style.eyeInvisibleOutlined}
        onClick={() => {
          subEvent.pub(id)
          setShow(true)
        }}
      />
    </>
  )
}

export default ShowPhone
