import { Item, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { createAction } from '@amazebird/antd-schema-table'
import { TreeSelect as Dict } from '@galaxy/dict'
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react'
import entryListService from '@/services/preEntry/list'
import ReModal from '@/components/ReModal'
import dmsService from '@/services/dms'
import { CLOSE_RECRUITMENT2 } from '@/constants/dms/eventName'

type Iprops = {
  action: ReturnType<typeof createAction>
  id: number
}

export type ActionType = {
  open: () => void
}

const EndReview: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, id }, ref) => {
  const [open, setOpen] = useState<boolean>(false)

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
  }))

  const schema = useMemo<SchemaType>(() => {
    return {
      reason: {
        label: '结束预入职原因',
        component: Dict,
        required: true,
        props: {
          code: 'OMS_END_RECRUIT_REASON',
          scope: 'ENTRY',
          placeholder: '请选择结束预入职原因',
          allowClear: true,
        },
      },
      tip: {
        label: '说明',
        component: 'Input.TextArea',
        props: {
          maxLength: 100,
        },
      },
      preserve: {
        label: ' ',
        component: 'Check',
        props: {
          label: '允许重新发起录用',
        },
      },
    }
  }, [])

  const form = SchemaForm.createForm()

  return (
    <div>
      <ReModal
        title="结束审核"
        open={open}
        onCancel={() => {
          setOpen(false)
        }}
        onOk={async () => {
          const data = await form.validateFields()
          const isSuccess = await entryListService.endReview({
            id,
            ...data,
          })
          if (isSuccess) {
            dmsService.trace(CLOSE_RECRUITMENT2, {
              obmCloseRecruitmentReason: data.reason,
              obmIsAllowReinitiateProcess: data.preserve ? 1 : 0,
            })
            setOpen(false)
            action.refresh()
          }
        }}
        width={600}
      >
        <SchemaForm initialValues={{ preserve: true }} form={form} schema={schema}>
          <Item field="reason" />
          <Item field="tip" />
          <Item field="preserve" colon={false} />
        </SchemaForm>
      </ReModal>
    </div>
  )
})

export default EndReview
