import React, { useImperativeHandle, useMemo, useState, useRef, useEffect } from 'react'
import { createAction } from '@amazebird/antd-schema-table'
import { Item, Observer, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import ReModal from '@/components/ReModal'
import { TraceRecordEditBtn } from '@/constants/rbac/preEntry'
import { getPermission } from '@galaxy/rbac'
import { Button, Input } from 'antd'
import commonService from '@/services/common'
import style from './style.module.less'

type Iprops = {
  action: ReturnType<typeof createAction> | { refresh: (val: string) => void }
  content?: string
  id: number
}

export type ActionType = {
  open: () => void
}

type SchemaModeType = 'modify' | 'detail'

const TraceRecord: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, content = '', id }, ref) => {
  const [open, setOpen] = useState(false)
  const [schemaMode, setSchemaMode] = useState<SchemaModeType>('detail')
  const [showContent, setShowContent] = useState('')
  const [loading, setLoading] = useState(false)
  const form = SchemaForm.createForm()

  useEffect(() => {
    setShowContent(content)
  }, [content])

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
  }))

  const closeFun = async () => {
    // 清空表单状态再关闭
    setOpen(false)
    setTimeout(() => {
      setSchemaMode('detail')
      form.resetFields()
    })
  }

  const schema = useMemo<SchemaType>(() => {
    return {
      traceRecord: {
        label: '',
        component: Observer({
          watch: '#mode',
          action: (modeVal: SchemaModeType) => {
            if (modeVal === 'modify') {
              return (props) => <Input.TextArea {...props} />
            }
            return ({ value }) => {
              if (!value) {
                return '暂无过程跟踪记录'
              }
              return <div style={{ whiteSpace: 'pre-wrap' }}>{value}</div>
            }
          },
        }),
        props: {
          maxLength: 500,
          showCount: true,
        },
        placeholder: '请输入过程跟踪记录',
      },
    }
  }, [])

  return (
    <ReModal
      title="过程跟踪记录"
      open={open}
      width={600}
      onCancel={() => {
        closeFun()
      }}
      footer={
        <>
          {schemaMode === 'detail' && (
            <>
              {getPermission(TraceRecordEditBtn) && (
                <Button type="default" onClick={() => setSchemaMode('modify')}>
                  编辑
                </Button>
              )}
              <Button
                type="primary"
                onClick={() => {
                  closeFun()
                }}
              >
                确定
              </Button>
            </>
          )}

          {schemaMode === 'modify' && (
            <>
              <Button
                type="default"
                onClick={() => {
                  form.setFieldsValue({
                    traceRecord: showContent,
                  })
                  setSchemaMode('detail')
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={async () => {
                  const data = await form.validateFields()
                  const reqParams = {
                    bizKey: `${id}`,
                    content: data.traceRecord,
                    type: 1,
                  }
                  setLoading(true)
                  await commonService.postRecord(reqParams).finally(() => {
                    setLoading(false)
                  })
                  action?.refresh?.(data.traceRecord)
                  closeFun()
                }}
              >
                保存
              </Button>
            </>
          )}
        </>
      }
    >
      <SchemaForm
        form={form}
        mode={schemaMode}
        initialValues={{
          traceRecord: content,
        }}
        labelCol={{
          span: 0,
        }}
        wrapperCol={{
          span: 24,
        }}
        schema={schema}
      >
        <div className={style.traceRecordWrap}>
          <Item field="traceRecord" />
        </div>
      </SchemaForm>
    </ReModal>
  )
})

export default TraceRecord
