import React from 'react'
import { ArrayElement } from '@/types/common'
import { SchemaColumnType, SchemaTableProps, createAction } from '@amazebird/antd-schema-table'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'
import { TreeSelect as UserSelector } from '@galaxy/user-selector'
import { TreeSelect as Dict } from '@galaxy/dict'
import { Tooltip, Typography, Tag } from 'antd'
import moment from 'moment'
import DictText from '@/components/DictText'
import QuestionTooltip from '@/components/QuestionTooltip'
import { NavigateFunction } from 'react-router-dom'
import commonService from '@/services/common'
import ShowPhone from './ShowPhone'

const TIME_ENTRY_LIMIT = '2023-04-01'

const TIME_ENTER_ENTRY = '2023-10-01'

export const emptyText = '--'

// 试用期期限 入职是字典，录用是数字，这里做个映射字典 code -> numebr
export enum TrialPeriodMap {
  '01' = 0,
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
}

export type ConfigInterface = {
  action: ReturnType<typeof createAction>
  type: 0 | 1 // 0 - 进行中； 1 - 已完成
  navigate: NavigateFunction
  column: Map<string, ArrayElement<SchemaColumnType>>
  searchColumn: Map<string, ArrayElement<SchemaTableProps['searchColumns']>>
  getColumns: () => SchemaColumnType
  getSearchColumns: () => SchemaTableProps['searchColumns']
  records: any[]
}

class BaseConfig implements ConfigInterface {
  action: ReturnType<typeof createAction>

  type: 0 | 1 // 0 - 进行中； 1 - 已完成

  navigate: NavigateFunction

  column: Map<string, ArrayElement<SchemaColumnType>> = new Map()

  searchColumn: Map<string, ArrayElement<SchemaTableProps['searchColumns']>> = new Map()

  records: any[]

  constructor(config: {
    action: ReturnType<typeof createAction>
    navigate: NavigateFunction
    records: any[]
    type: 0 | 1 // 0 - 进行中； 1 - 已完成
  }) {
    this.action = config.action
    this.navigate = config.navigate
    this.records = config.records
    this.type = config.type
  }

  protected initSearchColumn() {
    this.searchColumn.set('isPpTrainee', {
      dataIndex: 'isPpTrainee',
      title: '朴培生',
      component: 'Select',
      placeholder: '是否仅看朴培生',
      // -1:不限 0:否 1:是
      options: [
        { label: '不限', value: -1 },
        { label: '仅看朴培生', value: 1 },
      ],
    })

    this.searchColumn.set('ehrCandidateId', {
      dataIndex: 'ehrCandidateId',
      title: 'EHR人才ID',
      component: 'InputNumber',
      props: {
        controls: false,
        min: 0,
        max: 999999999999,
        // onInput: (e) => {
        //   e.target.value = e.target.value.replace(/[^\d]/g, '')
        // },
      },
    })
    this.searchColumn.set('name', {
      dataIndex: 'name',
      title: '姓名',
      component: 'Input',
      props: {
        allowClear: true,
      },
    })
    this.searchColumn.set('departmentId', {
      dataIndex: 'departmentId',
      title: '预计报到部门',
      component: OrgSelector,
      placeholder: '请输入预计报到部门',
      props: {
        onlySearch: true,
        showSearch: true,
        filter: (value) => {
          return {
            ...value,
            label: `${value.label}（${value.fullName}）`,
          }
        },
        showArrow: false,
      },
    })
    this.searchColumn.set('positionCandidateId', {
      dataIndex: 'positionCandidateId',
      title: 'DHR候选人ID',
      component: 'InputNumber',
      props: {
        controls: false,
        min: 0,
        max: 999999999999,
        // onInput: (e) => {
        //   e.target.value = e.target.value.replace(/^[1-9][\d]*/g, '')
        // },
      },
    })
    this.searchColumn.set('positionName', {
      dataIndex: 'positionName',
      title: '报到岗位',
      component: 'Input',
      placeholder: '请输入报到岗位',
      props: {
        allowClear: true,
      },
    })
    this.searchColumn.set('recruitResponsibleUserNums', {
      dataIndex: 'recruitResponsibleUserNums',
      title: '招聘负责人',
      component: UserSelector,
      placeholder: '请输入姓名/工号',
      props: {
        onlySearch: true,
        multiple: true,
        autoClearSearchValue: false,
      },
    })
    this.searchColumn.set('areaCode', {
      dataIndex: 'areaCode',
      title: '所属区域',
      component: Dict,
      placeholder: '请选择所属区域',
      props: {
        multiple: true,
        code: 'BM_BZGSQY',
        scope: 'EHR',
        allowClear: true,
        labelInValue: true,
      },
    })
    this.searchColumn.set('entryCityCodes', {
      dataIndex: 'entryCityCodes',
      title: '入职办理地区',
      component: 'Select',
      options: async () => {
        const $value = await commonService.getEhrDictArea()
        return $value
      },
      placeholder: '请选择入职办理地区',
      props: {
        allowClear: true,
        mode: 'multiple',
        labelInValue: true,
      },
    })
    this.searchColumn.set('entryPlaceName', {
      dataIndex: 'entryPlaceName',
      title: '入职办理地简称',
      component: 'Input',
      placeholder: '请输入入职办理地简称',
      props: {
        allowClear: true,
      },
    })
    this.searchColumn.set('employmentFormCodes', {
      dataIndex: 'employmentFormCodes',
      title: '用工形式',
      component: Dict,
      placeholder: '请选择用工形式',
      props: {
        code: 'BM_35',
        scope: 'EHR',
        multiple: 10,
        allowClear: true,
        labelInValue: true,
        showArrow: true,
      },
    })
    this.searchColumn.set('timeEntry', {
      dataIndex: 'timeEntry',
      title: '预计报到日期',
      component: 'RangePicker',
      props: {
        disabledDate: (currentDate: moment.Moment) => {
          return currentDate.isBefore(moment(TIME_ENTRY_LIMIT))
        },
      },
    })
    this.searchColumn.set('enterEntryTime', {
      dataIndex: 'enterEntryTime',
      title: '进入预入职日期',
      component: 'RangePicker',
      props: {
        disabledDate: (currentDate: moment.Moment) => {
          return currentDate.isBefore(moment(TIME_ENTER_ENTRY))
        },
      },
    })
    this.searchColumn.set('timeRecentSubmit', {
      dataIndex: 'timeRecentSubmit',
      title: '最近提交时间',
      component: 'RangePicker',
      props: {
        disabledDate: (currentDate: moment.Moment) => {
          // 只允许选择 23.10.1 - 当天 23：59 区间的时间
          const todayLastTime = moment()
          todayLastTime.set({ hour: 23, minute: 59, second: 59, millisecond: 0 })

          return (
            currentDate.isBefore(moment(TIME_ENTER_ENTRY)) || currentDate.isAfter(todayLastTime)
          )
        },
        format: 'YYYY-MM-DD HH:mm',
        showTime: {
          format: 'HH:mm',
        },
      },
    })
    this.searchColumn.set('recruitTypeCode', {
      dataIndex: 'recruitTypeCode',
      title: '招聘类型',
      component: Dict,
      placeholder: '请选择招聘类型',
      props: {
        code: 'RECRUIT_TYPE',
        scope: 'GLOBAL',
        allowClear: true,
        showArrow: true,
        labelInValue: true,
      },
    })
  }

  protected initColumn() {
    this.column.set('positionCandidateId', {
      dataIndex: 'positionCandidateId',
      title: 'DHR候选人ID',
      fixed: 'left' as const,
      width: 100,
    })
    this.column.set('name', {
      dataIndex: 'name',
      title: '姓名',
      fixed: 'left' as const,
      width: 150,
      cell: ({ text, record }) => {
        return (
          <span>
            {text}{' '}
            {record.isPpTrainee === 1 ? (
              <Tag style={{ marginLeft: 4 }} color="green">
                朴培生
              </Tag>
            ) : null}
          </span>
        )
      },
    })
    this.column.set('departmentName', {
      dataIndex: 'departmentName',
      title: '预计报到部门',
      fixed: 'left' as const,
      cell({ text, record }) {
        if (!text) {
          return emptyText
        }
        return <Tooltip title={record.departmentFullName}>{text}</Tooltip>
      },
      width: 150,
    })
    this.column.set('ehrCandidateId', {
      dataIndex: 'ehrCandidateId',
      title: 'EHR人才ID',
      width: 100,
      cell: ({ text, record }) => {
        return text || '--'
      },
    })
    this.column.set('contactPhone', {
      dataIndex: 'contactPhone',
      title: '联系方式',
      width: 120,
      cell: ({ text }) => {
        return <ShowPhone id={`${this.type}`} value={text} />
      },
    })
    this.column.set('positionName', {
      dataIndex: 'positionName',
      title: '报到岗位',
      width: 100,
    })
    this.column.set('recruitResponsiblePerson', {
      dataIndex: 'recruitResponsiblePerson',
      title: '招聘负责人',
      width: 150,
    })
    this.column.set('areaName', {
      dataIndex: 'areaName',
      title: '所属区域',
      cell: DictText,
      width: 100,
    })
    this.column.set('contractEntityName', {
      dataIndex: 'contractEntityName',
      title: '合同主体',
      cell: DictText,
      width: 200,
    })
    this.column.set('offerTemplateName', {
      dataIndex: 'offerTemplateName',
      title: 'offer适用模板',
      cell: DictText,
      width: 100,
    })
    this.column.set('entryCityName', {
      dataIndex: 'entryCityName',
      title: '入职办理地区',
      options: async () => {
        const $value = await commonService.getEhrDictArea()
        return $value
      },
      cell: 'Text',
      width: 100,
    })
    this.column.set('recruitTypeName', {
      dataIndex: 'recruitTypeName',
      title: '招聘类型',
      cell: DictText,
      width: 100,
    })
    this.column.set('employmentFormName', {
      dataIndex: 'employmentFormName',
      title: '用工形式',
      cell: DictText,
      width: 150,
    })
    this.column.set('employmentTypeName', {
      dataIndex: 'employmentTypeName',
      title: '员工类型',
      cell: DictText,
      width: 100,
    })
    this.column.set('employmentStateName', {
      dataIndex: 'employmentStateName',
      title: '人员类别',
      cell: DictText,
      width: 100,
    })
    this.column.set('postLevelName', {
      dataIndex: 'postLevelName',
      title: '职位',
      cell: DictText,
      width: 100,
    })
    this.column.set('postRankName', {
      dataIndex: 'postRankName',
      title: '部门职级',
      cell: DictText,
      width: 100,
    })
    this.column.set('trialPeriodName', {
      dataIndex: 'trialPeriodName',
      title: '试用期期限（月）',
      cell: DictText,
      width: 120,
    })
    this.column.set('timeEntry', {
      dataIndex: 'timeEntry',
      title: '预计报到日期',
      sorter: true,
      cell: 'Date',
      width: 120,
    })
    this.column.set('entryPlaceName', {
      dataIndex: 'entryPlaceName',
      title: '入职办理地简称',
      width: 120, // 每行最少显示8个字符
      cell: ({ text }) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              cursor: 'pointer',
            }}
            ellipsis={{
              rows: 2,
              tooltip: true,
            }}
          >
            {text}
          </Typography.Paragraph>
        )
      },
    })
    this.column.set('entryPlaceAddress', {
      dataIndex: 'entryPlaceAddress',
      title: '入职办理地点',
      width: 120, // 每行最少显示8个字符
      cell: ({ text }) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              cursor: 'pointer',
            }}
            ellipsis={{
              rows: 2,
              tooltip: true,
            }}
          >
            {text}
          </Typography.Paragraph>
        )
      },
    })
    this.column.set('timeCreate', {
      dataIndex: 'timeCreate',
      title: '进入预入职日期',
      sorter: true,
      cell: 'Date',
      width: 120,
    })
    this.column.set('jobDuration', {
      dataIndex: 'jobDuration',
      title: (
        <QuestionTooltip
          mark="作业净时长"
          pointTips="候选人材料审核停留在初审、复核节点的总计时长"
        />
      ),
      cell({ text }) {
        if (typeof text === 'undefined') {
          return emptyText
        }
        return <span>{`${text}h`}</span>
      },
      width: 100,
    })
    this.column.set('spentTotalTime', {
      dataIndex: 'spentTotalTime',
      title: (
        <QuestionTooltip mark="耗费总时长" pointTips="候选人自进入预入职阶段至完成审核的总计时长" />
      ),
      cell({ text }) {
        if (typeof text === 'undefined') {
          return emptyText
        }
        return <span>{`${text}h`}</span>
      },
      width: 100,
    })
    this.column.set('entryMaterialProcessTrackInfo', {
      dataIndex: 'entryMaterialProcessTrackInfo',
      title: '过程跟踪记录',
      width: 170, // 每行最少显示12个字符
      cell: ({ text }) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              cursor: 'pointer',
            }}
            ellipsis={{
              rows: 2,
              tooltip: <div style={{ whiteSpace: 'pre-wrap' }}>{text}</div>,
            }}
          >
            {text}
          </Typography.Paragraph>
        )
      },
    })
  }

  getColumns = (): SchemaColumnType => {
    return []
  }

  getSearchColumns = (): SchemaTableProps['searchColumns'] => {
    return []
  }
}

export default BaseConfig
