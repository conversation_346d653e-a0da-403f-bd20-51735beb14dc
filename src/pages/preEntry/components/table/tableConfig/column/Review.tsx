import { Item, Observer, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { Cascader } from 'antd'
import React, { useImperativeHandle, useMemo, useState } from 'react'
import dmsService from '@/services/dms'
import { RECHECK } from '@/constants/dms/eventName'
import { Cascader as Dict } from '@galaxy/dict'
import AuditService from '@/services/preEntry/audit'
import { createAction } from '@amazebird/antd-schema-table'
import ReModal from '@/components/ReModal'

type Iprops = {
  action: ReturnType<typeof createAction>
  content: string
  id: number
}

export type ActionType = {
  open: () => void
}

const Review: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, content, id }, ref) => {
  const [open, setOpen] = useState(false)

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
  }))

  const schema = useMemo<SchemaType>(() => {
    return {
      reviewResult: {
        label: '复核结果',
        component: 'Radio',
        options: [
          { label: '认可初审结果', value: 1 },
          { label: '驳回初审结果', value: 2 },
        ],
        required: true,
      },
      refuse: {
        label: '驳回原因',
        component: Dict,
        props: {
          code: 'OMS_INFORMATION_FAILED_REASON',
          scope: 'ENTRY',
          multiple: 10,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          placeholder: '请选择驳回原因',
          showSearch: true,
          displayRender: (label) => {
            return label.join('/')
          },
        },
        visible: Observer({
          watch: 'reviewResult',
          action: (reviewResult) => {
            return reviewResult === 2
          },
        }),
        required: true,
      },
      note: {
        label: '备注',
        component: 'Input.TextArea',
        props: {
          maxLength: 100,
        },
        visible: Observer({
          watch: 'reviewResult',
          action: (reviewResult) => {
            return reviewResult === 2
          },
        }),
      },
    }
  }, [])

  const form = SchemaForm.createForm()

  return (
    <div>
      <ReModal
        title="复核"
        open={open}
        onCancel={() => {
          setOpen(false)
        }}
        onOk={async () => {
          const data = await form.validateFields()
          const { note, reviewResult, refuse } = data
          const isSuccess = await AuditService.review({
            id,
            note,
            reviewResult,
            refuseCodes: refuse,
          })
          if (isSuccess) {
            // 提交成功后
            dmsService.trace(RECHECK, {
              obmRecheckResult: data.reviewResult === 1 ? 'passed' : 'rejected',
            })
            setOpen(false)
            action.refresh()
          }
        }}
        width={600}
      >
        {content}
        <SchemaForm
          form={form}
          labelCol={{
            span: 6,
          }}
          wrapperCol={{
            span: 18,
          }}
          schema={schema}
        >
          <Item field="reviewResult" />
          <Item field="refuse" />
          <Item field="note" />
        </SchemaForm>
      </ReModal>
    </div>
  )
})

export default Review
