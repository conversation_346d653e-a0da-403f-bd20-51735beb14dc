import { SchemaColumnType, SchemaTableProps, createAction } from '@amazebird/antd-schema-table'
import { ArrayElement } from '@/types/common'
import { STASTUS_COLOR } from '@galaxy/dhr-style'
import {
  EhrProgress,
  IsEntry,
  IsNeedAudit,
  IsNeedReview,
  ProcessProgress,
} from '@/services/preEntry/list'
import { Badge, Button, Tooltip, Typography, message } from 'antd'
import React from 'react'
import DictText from '@/components/DictText'
import SpecialPass from '@/components/SpecialPass'
import dmsService from '@/services/dms'
import { RESEND_SMS, RESULT_PUSH, VIEW_CANDIDATE } from '@/constants/dms/eventName'
import RouterService from '@/services/router'
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons'
import materialService from '@/services/preEntry/material'
import ModifyResult from '@/components/ModifyResult'
import { NavigateFunction } from 'react-router-dom'
import { Permission, getPermission } from '@galaxy/rbac'
import {
  InProcessCheckBtn,
  InProcessDepartmentSearch,
  InProcessEndAuditBtn,
  InProcessEndBtn,
  InProcessLogBtn,
  InProcessModifyResultBtn,
  InProcessPushBtn,
  InProcessResendBtn,
  InProcessResendModifyBtn,
  InProcessReviewBtn,
  ModifyEmploymentInfoBtn,
  TraceRecordBtn,
} from '@/constants/rbac/preEntry'
import { omit } from 'lodash-es'
import ModifyInformationModal, {
  ActionType as ModifyActionType,
} from '@/components/ModifyInformation'
import { OTHER, ENTRY_PLACE_NAME_TYPE, FOURTH_DEPARTMENT_CATEGORY } from '@/constants'
import { backgroundInvestigationStatus } from '@/pages/preEntry/components/table/constant'
import BaseConfig, { TrialPeriodMap } from './BaseColumnConfig'
import Resend, { ActionType as ResendActionType } from './Resend'
import Review, { ActionType as ReviewActionType } from './Review'
import End, { ActionType as EndActionType } from './End'
import Push, { ActionType as PushActionType } from './Push'
import TraceRecord, { ActionType as TraceRecordActionType } from './TraceRecord'
import style from '../../style.module.less'
import MoreDropdown from '../../../MoreDropdown'
import EndReview, { ActionType as EndReviewActionType } from './EndReview'

const emptyText = '--'

export const processProgressOptions = [
  {
    label: '待提交',
    value: ProcessProgress.ToBeSubmitted,
    color: STASTUS_COLOR.YELLOW,
  },
  {
    label: '待再次提交',
    value: ProcessProgress.ToBeResubmitted,
    color: STASTUS_COLOR.YELLOW,
  },
  {
    label: '待审核',
    value: ProcessProgress.Audit,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '待复核',
    value: ProcessProgress.ToBeReviewed,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '待重审',
    value: ProcessProgress.PendingReview,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '审核不通过',
    value: ProcessProgress.ReviewFailed,
    color: STASTUS_COLOR.RED,
  },
  {
    label: '审核通过',
    value: ProcessProgress.Approved,
    color: STASTUS_COLOR.BLUE,
  },
  {
    label: '特批通过',
    value: ProcessProgress.SpecialApproval,
    color: STASTUS_COLOR.BLUE,
  },
  {
    label: '结束预入职',
    value: ProcessProgress.EndRecruitment,
    color: STASTUS_COLOR.RED,
  },
]

export const timeoutStatusOptions = [
  { label: '未超时', value: 0, color: STASTUS_COLOR.GREEN },
  { label: '即将超时', value: 1, color: STASTUS_COLOR.YELLOW },
  { label: '已超时', value: 2, color: STASTUS_COLOR.RED },
]

export const ehrProgressOptions = [
  {
    label: '--',
    value: EhrProgress.No,
  },
  {
    label: '待代缴流程归档',
    value: EhrProgress.ToBeArchived,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '待下发',
    value: EhrProgress.ToBeIssued,
    color: STASTUS_COLOR.GREEN,
  },
  {
    label: '自动重试中',
    value: EhrProgress.AutoTry,
    color: STASTUS_COLOR.YELLOW,
  },
  {
    label: '下发失败',
    value: EhrProgress.Failed,
    color: STASTUS_COLOR.RED,
  },
  {
    // search场景过滤掉这个选项
    label: '下发成功',
    value: EhrProgress.Success,
    color: STASTUS_COLOR.BLUE,
  },
]

const getAuditResultText = (auditResult: 0 | 1 | 2 | 3) => {
  if (auditResult === 1) {
    return '审核通过'
  }
  if (auditResult === 2) {
    return '审核不通过'
  }
  if (auditResult === 3) {
    return '驳回修改'
  }
  return emptyText
}

export const getCheckStatus = (
  processProgress: ProcessProgress,
  isEntry: IsEntry,
  isNeedAudit: IsNeedAudit,
): boolean => {
  if (isNeedAudit === IsNeedAudit.NO) {
    return false
  }
  // 待再次提交、待审核、待复核、待重审、审核不通过、审核通过（人员未入职时可用）、特批通过、结束招聘
  if (processProgress === ProcessProgress.Approved && isEntry === IsEntry.NO) {
    return true
  }
  return [
    ProcessProgress.ToBeResubmitted,
    ProcessProgress.Audit,
    ProcessProgress.ToBeReviewed,
    ProcessProgress.PendingReview,
    ProcessProgress.ReviewFailed,
    ProcessProgress.SpecialApproval,
    ProcessProgress.EndRecruitment,
  ].includes(processProgress)
}

class InProcessConfig extends BaseConfig {
  column: Map<string, ArrayElement<SchemaColumnType>> = new Map()

  searchColumn: Map<string, ArrayElement<SchemaTableProps['searchColumns']>> = new Map()

  resendRef: Record<string, ResendActionType> = {}

  reviewRef: Record<string, ReviewActionType> = {}

  endRef: Record<string, EndActionType> = {}

  endReviewRef: Record<string, EndReviewActionType> = {}

  pushRef: Record<string, PushActionType> = {}

  modifyRef: Record<string, ModifyActionType> = {}

  traceRecordRef: Record<string, TraceRecordActionType> = {}

  constructor(config: {
    action: ReturnType<typeof createAction>
    navigate: NavigateFunction
    records: any[]
  }) {
    super({ ...config, type: 0 })
    this.initSearchColumn()
    this.initColumn()
  }

  protected initSearchColumn() {
    super.initSearchColumn()
    this.searchColumn.set('entryInvestigationResult', {
      dataIndex: 'entryInvestigationResult',
      title: '背调状态',
      component: 'Select',
      placeholder: '请选择背调状态',
      options: backgroundInvestigationStatus,
      props: {
        mode: 'multiple',
        labelInValue: true,
        showArrow: true,
      },
    })
    this.searchColumn.set('processProgresses', {
      dataIndex: 'processProgresses',
      title: '处理进度',
      component: 'Select',
      placeholder: '请选择处理进度',
      options: processProgressOptions,
      props: {
        mode: 'multiple',
        labelInValue: true,
        showArrow: true,
      },
    })
    this.searchColumn.set('timeoutStatuses', {
      dataIndex: 'timeoutStatuses',
      title: '超时状态',
      component: 'Select',
      placeholder: '请选择超时状态',
      options: timeoutStatusOptions,
      props: {
        mode: 'multiple',
        labelInValue: true,
        showArrow: true,
      },
    })
    this.searchColumn.set('ehrProgress', {
      dataIndex: 'ehrProgress',
      title: '结果推送状态',
      component: 'Select',
      placeholder: '请选择结果推送状态',
      options: ehrProgressOptions.filter((item) => item.value !== EhrProgress.Success),
      props: {
        allowClear: true,
        labelInValue: true,
      },
    })
  }

  // eslint-disable-next-line max-lines-per-function
  protected initColumn() {
    super.initColumn()
    this.column.set('entryInvestigationResult', {
      dataIndex: 'entryInvestigationResult',
      title: '背调状态',
      options: backgroundInvestigationStatus,
      width: 100,
    })
    this.column.set('currentAddressName', {
      dataIndex: 'currentAddressName',
      title: '现居住地',
      width: 120, // 每行最少显示8个字符
      cell: ({ text }) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              cursor: 'pointer',
            }}
            ellipsis={{
              rows: 2,
              tooltip: true,
            }}
          >
            {text}
          </Typography.Paragraph>
        )
      },
    })
    this.column.set('residenceAddress', {
      dataIndex: 'residenceAddress',
      title: '身份证地址',
      width: 120, // 每行最少显示8个字符
      cell: ({ text }) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              cursor: 'pointer',
            }}
            ellipsis={{
              rows: 2,
              tooltip: true,
            }}
          >
            {text}
          </Typography.Paragraph>
        )
      },
    })
    this.column.set('age', {
      dataIndex: 'age',
      title: '年龄',
      cell: 'Number',
      width: 80,
    })
    this.column.set('timeIdCardStart', {
      dataIndex: 'timeIdCardStart',
      title: '身份证开始日期',
      cell: 'Date',
      width: 100,
    })
    this.column.set('timeIdCardEnd', {
      dataIndex: 'timeIdCardEnd',
      title: '身份证到期日期',
      cell: 'Date',
      width: 100,
    })
    this.column.set('bankTypeName', {
      dataIndex: 'bankTypeName',
      title: '银行卡类型',
      cell: DictText,
      width: 100,
    })
    this.column.set('bankName', {
      dataIndex: 'bankName',
      title: '开户银行',
      width: 100,
    })
    this.column.set('educationName', {
      dataIndex: 'educationName',
      title: '学历',
      cell: DictText,
      width: 100,
    })
    this.column.set('graduateSchool', {
      dataIndex: 'graduateSchool',
      title: '毕业院校',
      width: 150,
    })
    this.column.set('major', {
      dataIndex: 'major',
      title: '专业',
      width: 120,
    })
    this.column.set('timeGraduate', {
      dataIndex: 'timeGraduate',
      title: '毕业日期',
      cell: 'Date',
      width: 100,
    })
    this.column.set('studyModeName', {
      dataIndex: 'studyModeName',
      title: '学习形式',
      cell: DictText,
      width: 100,
    })
    this.column.set('degreeName', {
      dataIndex: 'degreeName',
      title: '学位',
      cell: DictText,
      width: 100,
    })
    this.column.set('isHealthCertificateName', {
      dataIndex: 'isHealthCertificateName',
      title: '是否有健康证',
      cell: DictText,
      width: 100,
    })
    this.column.set('timeHealthEnd', {
      dataIndex: 'timeHealthEnd',
      title: '健康证到期日期',
      cell: 'Date',
      width: 100,
    })
    this.column.set('emergencyContactName', {
      dataIndex: 'emergencyContactName',
      title: '紧急联系人姓名',
      width: 100,
    })
    this.column.set('emergencyContactPhone', {
      dataIndex: 'emergencyContactPhone',
      title: '紧急联系人电话',
      width: 120,
    })
    this.column.set('memberRelationName', {
      dataIndex: 'memberRelationName',
      title: '与本人关系',
      cell: (props) => {
        const { record } = props
        const { memberRelationName, memberRelationRemark } = record || {}
        let extendsText = null
        // 关系为「其他」时
        if (memberRelationName?.code === '06') {
          extendsText = `（${memberRelationRemark || ''}）`
        }
        return <DictText {...props} extendsText={extendsText} />
      },
      width: 100,
    })
    this.column.set('isDisabledName', {
      dataIndex: 'isDisabledName',
      title: '是否残障人士',
      cell: DictText,
      width: 100,
    })
    this.column.set('isVeteranName', {
      dataIndex: 'isVeteranName',
      title: '是否退伍军人',
      cell: DictText,
      width: 100,
    })
    this.column.set('timeoutStatus', {
      dataIndex: 'timeoutStatus',
      title: '超时状态',
      cell: 'Status',
      options: timeoutStatusOptions,
      width: 100,
    })
    this.column.set('timeRecentSubmit', {
      dataIndex: 'timeRecentSubmit',
      title: '最近提交时间',
      cell: {
        type: 'DateTime',
        props: {
          format: 'YYYY-MM-DD HH:mm',
        },
      },
      width: 150,
      sorter: true,
    })
    this.column.set('processProgress', {
      dataIndex: 'processProgress',
      title: '处理进度',
      cell: ({ text, record }) => {
        const config = processProgressOptions.find((item) => item.value === text)
        // 找不到配置项，text值异常，暂时返回emptyText
        if (!config) {
          return emptyText
        }
        // 特批通过，特殊处理
        if (text === 7) {
          return (
            <Badge
              color={config.color}
              text={
                <span style={{ color: config.color }}>
                  <SpecialPass id={record.id} />
                </span>
              }
            />
          )
        }
        return (
          <Badge
            color={config.color}
            text={<span style={{ color: config.color }}>{config.label}</span>}
          />
        )
      },
      width: 150,
    })
    this.column.set('auditResult', {
      dataIndex: 'auditResult',
      title: '初审结果',
      options: [
        {
          label: '--',
          value: -1,
        },
        {
          label: '审核通过',
          value: 1,
        },
        {
          label: '审核不通过',
          value: 2,
        },
        {
          label: '审核不通过',
          value: 3,
        },
      ],
      width: 100,
    })
    this.column.set('noPassReason', {
      dataIndex: 'noPassReason',
      title: '初审不通过原因',
      cell: ({ text }) => (
        <DictText
          text={text}
          render={(content) => {
            return (
              <Typography.Paragraph
                style={{
                  cursor: 'pointer',
                  margin: 0,
                }}
                ellipsis={{
                  rows: 2,
                  tooltip: true,
                }}
              >
                {content}
              </Typography.Paragraph>
            )
          }}
        />
      ),
      width: 200,
    })
    this.column.set('rejectReason', {
      dataIndex: 'rejectReason',
      title: '复核驳回原因',
      cell: ({ record, text }) => (
        <DictText
          text={text}
          render={(content) => {
            return (
              <div className={style.rejectReason}>
                <Typography.Paragraph
                  style={{
                    margin: 0,
                    cursor: 'pointer',
                  }}
                  ellipsis={{
                    rows: 2,
                    tooltip: true,
                  }}
                >
                  {content}
                </Typography.Paragraph>
                {record.rejectReasonRemark && (
                  <Tooltip title={record.rejectReasonRemark}>
                    <InfoCircleOutlined />
                  </Tooltip>
                )}
              </div>
            )
          }}
        />
      ),
      width: 200,
    })
    this.column.set('ehrProgress', {
      dataIndex: 'ehrProgress',
      title: '结果推送状态',
      cell: 'Status',
      options: ehrProgressOptions,
      width: 100,
    })
    this.column.set('recentAuditPerson', {
      dataIndex: 'recentAuditPerson',
      title: '最近初审人',
      width: 150,
    })
    this.column.set('recentReviewPerson', {
      dataIndex: 'recentReviewPerson',
      title: '最近复核人',
      width: 150,
    })
    this.column.set('operator', {
      title: '操作',
      key: 'operator',
      width: 150,
      align: 'left' as const,
      fixed: 'right' as const,
      // eslint-disable-next-line max-lines-per-function
      cell: ({ record }) => {
        return (
          <>
            <Permission code={InProcessCheckBtn}>
              <Button
                type="link"
                size="small"
                disabled={
                  !getCheckStatus(record.processProgress, record.isEntry, record.isNeedAudit)
                }
                onClick={() => {
                  dmsService.trace(VIEW_CANDIDATE)
                  this.navigate(
                    RouterService.path(`/preEntry/entryMaterialsAuditForm`, {
                      id: record.id,
                      listIds: this.records
                        .filter((item) =>
                          getCheckStatus(item.processProgress, item.isEntry, item.isNeedAudit),
                        )
                        .map((item) => item.id)
                        .join(','),
                    }),
                  )
                }}
                style={{
                  marginRight: '6px', // dropdown有bug，无法从disable元素直接到dropdown，会导致hover无效
                }}
              >
                查看
              </Button>
            </Permission>
            {getPermission(InProcessReviewBtn) && record.isNeedReview === IsNeedReview.YES ? (
              <Button
                type="link"
                size="small"
                // 处理进度=待复核
                disabled={![ProcessProgress.ToBeReviewed].includes(record.processProgress)}
                onClick={() => {
                  this.reviewRef[record.id]?.open?.()
                }}
                style={{
                  marginRight: '6px', // dropdown有bug，无法从disable元素直接到dropdown，会导致hover无效
                }}
              >
                复核
              </Button>
            ) : null}
            <MoreDropdown
              menu={{
                // @ts-ignore 已经filter掉undefined场景
                items: [
                  {
                    key: 0,
                    label: '重发补录短信',
                    // 处理进度=待提交、待审核
                    disabled: ![ProcessProgress.ToBeSubmitted, ProcessProgress.Audit].includes(
                      record.processProgress,
                    ),
                    onClick: async () => {
                      dmsService.trace(RESEND_SMS)
                      const isSuccess = await materialService
                        .resendSMS(record.id, { noMessage: true })
                        .catch(() => {
                          return false
                        })
                      if (isSuccess) {
                        // 发送成功后
                        message.success('补录短信已重发')
                      } else {
                        message.error('补录短信重发失败，请稍后再试')
                      }
                    },
                    hasPermission: getPermission(InProcessResendBtn),
                  },
                  {
                    key: 1,
                    label: '重发修改通知',
                    // 处理进度=待再次提交  且 允许发送短信
                    disabled: !(
                      (typeof record.isCanNoticeModifyEntryMaterial === 'boolean'
                        ? record.isCanNoticeModifyEntryMaterial
                        : true) &&
                      [ProcessProgress.ToBeResubmitted].includes(record.processProgress)
                    ),
                    onClick: () => {
                      this.resendRef[record.id]?.open?.()
                    },
                    hasPermission: getPermission(InProcessResendModifyBtn),
                  },
                  record.dataSource === 'employment'
                    ? {
                        key: 8,
                        label: '修改录用信息',
                        // 同时满足以下条件：1. 「候选人状态」=“预入职审核中“ || "待入职" 2. 该预入职条目是同候选人id的最新条目
                        disabled: !(
                          record.isPositionCandidateNewestEntryMaterial &&
                          ['ATS_CS_035', 'ATS_CS_036'].includes(record.positionCandidateStatus)
                        ),
                        onClick: () => {
                          this.modifyRef[record.id]?.open?.()
                        },
                        hasPermission: getPermission(ModifyEmploymentInfoBtn),
                      }
                    : undefined,
                  {
                    key: 9,
                    label: '过程跟踪记录',
                    onClick: async () => {
                      this.traceRecordRef[record.id]?.open?.()
                    },
                    hasPermission: getPermission(TraceRecordBtn),
                  },
                  {
                    key: 3,
                    label: '审核结果推送',
                    // 处理进度=审核不通过/审核通过/特批通过/结束招聘 且 结果推送状态=自动重试中、下发失败或待下发时
                    disabled: !(
                      [
                        ProcessProgress.ReviewFailed,
                        ProcessProgress.Approved,
                        ProcessProgress.SpecialApproval,
                        ProcessProgress.EndRecruitment,
                      ].includes(record.processProgress) &&
                      [EhrProgress.ToBeIssued, EhrProgress.AutoTry, EhrProgress.Failed].includes(
                        record.ehrProgress,
                      )
                    ),
                    onClick: async () => {
                      dmsService.trace(RESULT_PUSH)
                      // 仅审核不通过且EHR待下发时，才打开弹窗
                      if (
                        record.processProgress === ProcessProgress.ReviewFailed &&
                        record.ehrProgress === EhrProgress.ToBeIssued
                      ) {
                        this.pushRef[record.id]?.open?.()
                      } else {
                        await this.pushRef[record.id]?.push?.()
                      }
                    },
                    hasPermission: getPermission(InProcessPushBtn),
                  },
                  record.processProgress === ProcessProgress.EndRecruitment
                    ? undefined
                    : {
                        key: 4,
                        label: '修改审核结果',
                        // 处理进度=审核不通过/审核通过/特批通过 且 结果推送状态=下发失败、待下发或待代缴流程归档时
                        disabled: !(
                          [
                            ProcessProgress.ReviewFailed,
                            ProcessProgress.Approved,
                            ProcessProgress.SpecialApproval,
                          ].includes(record.processProgress) &&
                          [
                            EhrProgress.ToBeIssued,
                            EhrProgress.Failed,
                            EhrProgress.ToBeArchived,
                          ].includes(record.ehrProgress) &&
                          record.isNeedAudit === IsNeedAudit.YES
                        ),
                        onClick: () => {
                          ModifyResult.confirm({
                            id: record.id,
                            name: record.name,
                            onOk: () => {
                              this.action.refresh()
                            },
                          })
                        },
                        hasPermission: getPermission(InProcessModifyResultBtn),
                      },
                  record.dataSource !== 'employment' // 没有该字段，默认是试运行外数据
                    ? {
                        key: 5,
                        label: '结束预入职',
                        danger: true,
                        // 处理进度=待提交；待再次提交；待审核；待复核；待重审；审核通过且结果推送状态=下发失败、自动重试中；审核不通过且流转状态=下发失败或待下发、自动重试中；特批通过且流转状态=下发失败、自动重试中。
                        disabled: !(
                          [
                            ProcessProgress.ToBeSubmitted,
                            ProcessProgress.ToBeResubmitted,
                            ProcessProgress.Audit,
                            ProcessProgress.ToBeReviewed,
                            ProcessProgress.PendingReview,
                          ].includes(record.processProgress) ||
                          ([ProcessProgress.Approved].includes(record.processProgress) &&
                            [EhrProgress.AutoTry, EhrProgress.Failed].includes(
                              record.ehrProgress,
                            )) ||
                          ([ProcessProgress.ReviewFailed].includes(record.processProgress) &&
                            [
                              EhrProgress.Failed,
                              EhrProgress.ToBeIssued,
                              EhrProgress.AutoTry,
                            ].includes(record.ehrProgress)) ||
                          ([ProcessProgress.SpecialApproval].includes(record.processProgress) &&
                            [EhrProgress.Failed, EhrProgress.AutoTry].includes(record.ehrProgress))
                        ),
                        onClick: () => {
                          this.endRef[record.id]?.open?.()
                        },
                        hasPermission: getPermission(InProcessEndBtn),
                      }
                    : undefined,
                  record.dataSource === 'employment'
                    ? {
                        key: 7,
                        label: '结束审核',
                        danger: true,
                        // 处理进度=待提交；待再次提交；待审核；待复核；待重审；审核通过且结果推送状态=待代缴流程归档、下发失败、自动重试中；审核不通过且流转状态=待代缴流程归档、下发失败、待下发、自动重试中；特批通过且流转状态=待代缴流程归档、下发失败、自动重试中。
                        disabled: !(
                          [
                            ProcessProgress.ToBeSubmitted,
                            ProcessProgress.ToBeResubmitted,
                            ProcessProgress.Audit,
                            ProcessProgress.ToBeReviewed,
                            ProcessProgress.PendingReview,
                          ].includes(record.processProgress) ||
                          ([ProcessProgress.Approved].includes(record.processProgress) &&
                            [
                              EhrProgress.ToBeArchived,
                              EhrProgress.AutoTry,
                              EhrProgress.Failed,
                            ].includes(record.ehrProgress)) ||
                          ([ProcessProgress.ReviewFailed].includes(record.processProgress) &&
                            [
                              EhrProgress.ToBeArchived,
                              EhrProgress.Failed,
                              EhrProgress.ToBeIssued,
                              EhrProgress.AutoTry,
                            ].includes(record.ehrProgress)) ||
                          ([ProcessProgress.SpecialApproval].includes(record.processProgress) &&
                            [
                              EhrProgress.ToBeArchived,
                              EhrProgress.Failed,
                              EhrProgress.AutoTry,
                            ].includes(record.ehrProgress))
                        ),
                        onClick: () => {
                          this.endReviewRef[record.id]?.open?.()
                        },
                        hasPermission: getPermission(InProcessEndAuditBtn),
                      }
                    : undefined,
                  {
                    key: 6,
                    label: '操作记录',
                    onClick: () => {
                      this.navigate(`log?id=${record.id}&title=${encodeURIComponent(record.name)}`)
                    },
                    hasPermission: getPermission(InProcessLogBtn),
                  },
                ]
                  .filter((item) => !!item)
                  .filter((item) => item?.hasPermission)
                  .map((item) => omit(item, 'hasPermission')),
              }}
            />
            <Resend
              action={this.action}
              id={record.id}
              ref={(node) => {
                if (node) {
                  this.resendRef[record.id] = node
                }
              }}
            />
            <Review
              action={this.action}
              id={record.id}
              content={`候选人${record.name}的初审结果为：${getAuditResultText(
                record.auditResult,
              )}`}
              ref={(node) => {
                if (node) {
                  this.reviewRef[record.id] = node
                }
              }}
            />
            <End
              action={this.action}
              id={record.id}
              ref={(node) => {
                if (node) {
                  this.endRef[record.id] = node
                }
              }}
            />
            <EndReview
              action={this.action}
              id={record.id}
              ref={(node) => {
                if (node) {
                  this.endReviewRef[record.id] = node
                }
              }}
            />
            <Push
              action={this.action}
              content={`候选人${record.name}的审核结果为：审核不通过`}
              id={record.id}
              ref={(node) => {
                if (node) {
                  this.pushRef[record.id] = node
                }
              }}
            />
            <ModifyInformationModal
              action={this.action}
              dataSource={{
                entryDepartmentId: {
                  value: record.departmentId,
                  label: record.departmentName,
                },
                entryPositionId: {
                  value: record.positionId,
                  label: record.positionName,
                  positionBaseId: record.positionBaseId,
                  areaCode: record.areaCode,
                  partnerCompany: record.cooperateCompanyCode,
                  fourthDepartmentCategory: record.fourthDepartmentCategory,
                  fourthDepartmentName: record.fourthDepartmentName,
                },
                employmentFormCode: record.employmentFormCode,
                employeeTypeCode: record.employmentTypeCode,
                rankCode: record.postRankCode,
                probationPeriod: TrialPeriodMap[record.trialPeriodCode as string],
                timeExpectedEntry: record.timeEntry || undefined, // timeEntry为0的时候，要处理成undefined
                entryCityCode: record.entryCityCode,
                recruitTypeCode: record?.recruitTypeName?.code,
                entryPlaceName:
                  record?.entryPlaceNameType === ENTRY_PLACE_NAME_TYPE.OTHER
                    ? [`${ENTRY_PLACE_NAME_TYPE.OTHER}`, record?.entryPlaceName]
                    : [record?.entryPlaceName],
                entryPlaceAddress: record?.entryPlaceAddress,
                fourthDepartmentCategory: record.fourthDepartmentCategory,
                fourthDepartmentName: record.fourthDepartmentName,
              }}
              entryMaterialId={record.id}
              isCampus={record.isCampus}
              isVirtualPosition={!!record.isVirtualPosition}
              validateErrorMsg="录用信息校验不通过，修改信息失败。若您希望继续修改信息，请【结束审核】并勾选“允许重新发起录用”，并通知招聘负责人在录用管理中重新发起录用。"
              ref={(node) => {
                if (node) {
                  this.modifyRef[record.id] = node
                }
              }}
            />
            <TraceRecord
              action={this.action}
              id={record.id}
              ref={(node) => {
                if (node) {
                  this.traceRecordRef[record.id] = node
                }
              }}
              content={record.entryMaterialProcessTrackInfo}
            />
          </>
        )
      },
    })
  }

  getSearchColumns = (): SchemaTableProps['searchColumns'] => {
    return [
      this.searchColumn.get('positionCandidateId'), // DHR候选人ID
      this.searchColumn.get('name'), // 姓名
      getPermission(InProcessDepartmentSearch) && this.searchColumn.get('departmentId'), // 预计报到部门
      this.searchColumn.get('positionName'), // 报到岗位
      this.searchColumn.get('recruitResponsibleUserNums'), // 招聘负责人
      this.searchColumn.get('areaCode'), // 所属区域
      this.searchColumn.get('entryCityCodes'), // 入职办理城市
      this.searchColumn.get('entryPlaceName'), // 入职办理地简称
      this.searchColumn.get('employmentFormCodes'), // 用工形式
      this.searchColumn.get('timeEntry'), // 预计报到日期
      this.searchColumn.get('enterEntryTime'), // 进入预入职日期
      this.searchColumn.get('timeRecentSubmit'), // 进入预入职日期
      this.searchColumn.get('recruitTypeCode'), // 招聘类型
      this.searchColumn.get('entryInvestigationResult'), // 背调状态
      this.searchColumn.get('processProgresses'), // 处理进度
      this.searchColumn.get('timeoutStatuses'), // 超时状态
      this.searchColumn.get('ehrProgress'), // 结果推送状态
      this.searchColumn.get('isPpTrainee'), // 是否朴培生
      this.searchColumn.get('ehrCandidateId'), // EHR人才ID
    ].filter((item) => !!item) as SchemaTableProps['searchColumns']
  }

  getColumns = (): SchemaColumnType => {
    return [
      this.column.get('positionCandidateId'), // DHR候选人ID
      this.column.get('name'), // 姓名
      this.column.get('departmentName'), // 预计报到部门
      this.column.get('ehrCandidateId'), // EHR人才ID
      this.column.get('contactPhone'), // 联系方式
      this.column.get('positionName'), // 报到岗位
      this.column.get('recruitResponsiblePerson'), // 招聘负责人
      this.column.get('areaName'), // 所属区域
      this.column.get('contractEntityName'), // 合同主体
      this.column.get('offerTemplateName'), // offer适用模板
      this.column.get('entryCityName'), // 入职办理城市
      this.column.get('recruitTypeName'), // 招聘类型
      this.column.get('entryInvestigationResult'), // 背调状态
      this.column.get('employmentFormName'), // 用工形式
      this.column.get('employmentTypeName'), // 员工类型
      this.column.get('employmentStateName'), // 人员类别
      this.column.get('postLevelName'), // 职位
      this.column.get('postRankName'), // 职级
      this.column.get('trialPeriodName'), // 试用期期限
      this.column.get('currentAddressName'), // 现居住地
      this.column.get('residenceAddress'), // 身份证地址
      this.column.get('age'), // 年龄
      this.column.get('timeIdCardStart'), // 身份证开始日期
      this.column.get('timeIdCardEnd'), // 身份证到期日期
      this.column.get('bankTypeName'), // 银行卡类型
      this.column.get('bankName'), // 开户银行
      // this.column.get('educationName'), // 学历
      // this.column.get('graduateSchool'), // 毕业院校
      // this.column.get('major'), // 专业
      // this.column.get('timeGraduate'), // 毕业日期
      // this.column.get('studyModeName'), // 学习形式
      // this.column.get('degreeName'), // 学位
      this.column.get('isHealthCertificateName'), // 是否有健康证
      this.column.get('timeHealthEnd'), // 健康证到期日期
      this.column.get('emergencyContactName'), // 紧急联系人姓名
      this.column.get('emergencyContactPhone'), // 紧急联系人电话
      this.column.get('memberRelationName'), // 与本人关系
      this.column.get('isDisabledName'), // 是否残障人士
      this.column.get('isVeteranName'), // 是否退伍军人
      this.column.get('timeEntry'), // 预计报到日期
      this.column.get('entryPlaceName'), // 入职办理地简称
      this.column.get('entryPlaceAddress'), // 入职办理地点
      this.column.get('timeoutStatus'), // 超时状态
      this.column.get('timeCreate'), // 进入预入职日期
      this.column.get('timeRecentSubmit'), // 最近提交时间
      this.column.get('processProgress'), // 处理进度
      getPermission(TraceRecordBtn) && this.column.get('entryMaterialProcessTrackInfo'), // 过程跟踪记录
      this.column.get('auditResult'), // 初审结果
      this.column.get('noPassReason'), // 初审不通过原因
      this.column.get('rejectReason'), // 复核驳回原因
      this.column.get('ehrProgress'), // 结果推送状态
      this.column.get('recentAuditPerson'), // 最近初审人
      this.column.get('recentReviewPerson'), // 最近复核人
      this.column.get('jobDuration'), // 作业净时长
      this.column.get('spentTotalTime'), // 耗费总时长
      this.column.get('operator'), // 操作
    ].filter((item) => !!item) as SchemaColumnType
  }
}

export default InProcessConfig
