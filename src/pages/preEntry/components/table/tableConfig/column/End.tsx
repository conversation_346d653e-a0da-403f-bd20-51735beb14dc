import { Item, SchemaForm, SchemaType } from '@amazebird/antd-schema-form'
import { createAction } from '@amazebird/antd-schema-table'
import { TreeSelect as Dict } from '@galaxy/dict'
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react'
import dmsService from '@/services/dms'
import { CLOSE } from '@/constants/dms/eventName'
import entryListService from '@/services/preEntry/list'
import ReModal from '@/components/ReModal'

type Iprops = {
  action: ReturnType<typeof createAction>
  id: number
}

export type ActionType = {
  open: () => void
}

const End: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(({ action, id }, ref) => {
  const [open, setOpen] = useState<boolean>(false)

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true)
    },
  }))

  const schema = useMemo<SchemaType>(() => {
    return {
      reason: {
        label: '结束预入职原因',
        component: Dict,
        required: true,
        props: {
          code: 'OMS_END_RECRUIT_REASON',
          scope: 'ENTRY',
          placeholder: '请选择结束预入职原因',
          allowClear: true,
        },
      },
      tip: {
        label: '说明',
        component: 'Input.TextArea',
        props: {
          maxLength: 100,
        },
      },
      preserve: {
        label: ' ',
        component: 'Check',
        props: {
          label: '通知ehr遗珠',
        },
      },
    }
  }, [])

  const form = SchemaForm.createForm()

  return (
    <div>
      <ReModal
        title="结束预入职"
        open={open}
        onCancel={() => {
          setOpen(false)
        }}
        onOk={async () => {
          const data = await form.validateFields()
          const isSuccess = await entryListService.endRecruit({
            id,
            ...data,
          })
          if (isSuccess) {
            // 提交成功后
            dmsService.trace(CLOSE, {
              obmCloseRecruitmentReason: data.reason,
              obmIsNotifyEhrCloseProcess: data.preserve ? 1 : 0,
            })
            setOpen(false)
            action.refresh()
          }
        }}
        width={600}
      >
        <SchemaForm initialValues={{ preserve: false }} form={form} schema={schema}>
          <Item field="reason" />
          <Item field="tip" />
          <Item field="preserve" colon={false} />
        </SchemaForm>
      </ReModal>
    </div>
  )
})

export default End
