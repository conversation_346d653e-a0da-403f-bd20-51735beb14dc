import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from 'antd'
import ReModal from '@/components/ReModal'
import auditService from '@/services/preEntry/audit'
import dmsService from '@/services/dms'
import { BATCH_RECHECK } from '@/constants/dms/eventName'
import { getPermission } from '@galaxy/rbac'
import {
  InProcessBatchReviewBtn,
  InProcessExportBtn,
  InProcessTimeExportBtn,
} from '@/constants/rbac/preEntry'
import userService from '@/services/user'
import BaseConfig, { BatchReviewProps } from './BaseConfig'

class InProcessConfig extends BaseConfig {
  exportContent: string = '【进行中】tab数据：'

  timeDetailExportContent: string = '【进行中】tab数据：'

  persistenceKey: string = `entry::preEntry::inProcessList::${
    userService.info?.num ?? userService.info?.userName
  }`

  getColumnsState = () => {
    return {
      persistenceType: 'localStorage' as const,
      persistenceKey: this.persistenceKey,
      defaultValue: {
        // offer 适用模板
        offerTemplateName: {
          show: false,
        },
        ehrCandidateId: {
          show: false,
        },
        // 招聘负责人
        recruitResponsiblePerson: {
          show: false,
        },
        // 职级
        postRankName: {
          show: false,
        },
        // 试用期期限
        trialPeriodName: {
          show: false,
        },
        // 现居住地
        currentAddressName: {
          show: false,
        },
        // 身份证地址
        residenceAddress: {
          show: false,
        },
        // 年龄
        age: {
          show: false,
        },
        // 身份证开始日期
        timeIdCardStart: {
          show: false,
        },
        // 身份证到期日期
        timeIdCardEnd: {
          show: false,
        },
        // 银行卡类型
        bankTypeName: {
          show: false,
        },
        // 开户银行
        bankName: {
          show: false,
        },
        // 学历
        educationName: {
          show: false,
        },
        // 毕业院校
        graduateSchool: {
          show: false,
        },
        // 专业
        major: {
          show: false,
        },
        // 毕业日期
        timeGraduate: {
          show: false,
        },
        // 学习形式
        studyModeName: {
          show: false,
        },
        // 学位
        degreeName: {
          show: false,
        },
        // 是否有健康证
        isHealthCertificateName: {
          show: false,
        },
        // 健康证到期日期
        timeHealthEnd: {
          show: false,
        },
        // 紧急联系人姓名
        emergencyContactName: {
          show: false,
        },
        // 紧急联系人电话
        emergencyContactPhone: {
          show: false,
        },
        // 与本人关系
        memberRelationName: {
          show: false,
        },
        // 是否残障人士
        isDisabledName: {
          show: false,
        },
        // 是否退伍军人
        isVeteranName: {
          show: false,
        },
        // 复核驳回原因
        rejectReason: {
          show: false,
        },
        // 最近初审人
        recentAuditPerson: {
          show: false,
        },
        // 最近复核人
        recentReviewPerson: {
          show: false,
        },
        contactPhone: {
          show: false,
        },
        recruitTypeName: {
          show: false,
        },
        timeCreate: {
          show: false,
        },
        entryCityName: {
          show: false,
        },
        entryPlaceName: {
          show: false,
        },
        entryPlaceAddress: {
          show: false,
        },
      },
    }
  }

  getBatchReview = ({ tableAction, keys, setKeys }: BatchReviewProps) => {
    if (!getPermission(InProcessBatchReviewBtn)) {
      return {}
    }
    return {
      button: (
        <Button
          disabled={keys.length === 0}
          key="review"
          onClick={() => {
            ReModal.confirm({
              title: '批量复核通过',
              content: `确定认可选中的${keys.length}位候选人的初审结果吗？`,
              onOk: async () => {
                const { success, fail } = await auditService.batchReview({
                  reviewResult: 1,
                  ids: keys as number[],
                })
                setKeys([])
                // 提交成功后
                dmsService.trace(BATCH_RECHECK, {
                  obmBatchRecheckQty: keys.length,
                })
                tableAction.refresh()
                if (fail !== 0) {
                  message.warn(
                    `${success}位候选人批量复核成功，${fail}位候选人批量复核失败，请确认候选人处理进度后再试`,
                  )
                  return
                }
                message.success(`${success}位候选人批量复核成功！`)
              },
            })
          }}
        >
          批量复核通过
        </Button>
      ),
      rowSelection: {
        selectedRowKeys: keys,
        onChange(rowKeys, selectedRows) {
          let newSelectedRowKeys = [...rowKeys]
          if (rowKeys.length > 20) {
            if (rowKeys.length - selectedRows.length === 1) {
              message.warning('最多只能选择20条')
            } else {
              message.warning('最多只能选择20条，已为您选中前20条内容')
            }
            newSelectedRowKeys = rowKeys.splice(0, 20)
          }
          setKeys(newSelectedRowKeys)
        },
        getCheckboxProps: (record) => {
          return {
            // 仅可选择当前处理进度=待复核的条目
            disabled: record.processProgress !== 3,
          }
        },
        renderCell: (checked, record, index, originNode) => {
          if (record.processProgress !== 3) {
            return <Tooltip title="仅可选择状态为”待复核“的条目">{originNode}</Tooltip>
          }
          return originNode
        },
      },
    }
  }

  checkExportPermission = () => {
    return getPermission(InProcessExportBtn)
  }

  checkTimeDetailExportPermission = () => {
    return getPermission(InProcessTimeExportBtn)
  }
}

export default InProcessConfig
