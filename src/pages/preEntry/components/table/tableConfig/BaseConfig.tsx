import { createAction } from '@amazebird/antd-schema-table'
import { ColumnsStateType } from '@amazebird/antd-schema-table/es/types'
import { TableProps } from 'antd'
import React from 'react'

export type BatchReviewProps = {
  tableAction: ReturnType<typeof createAction>
  keys: React.Key[]
  setKeys: React.Dispatch<React.SetStateAction<React.Key[]>>
}

export type ConfigInterface = {
  exportContent: string
  timeDetailExportContent: string
  getColumnsState: () => ColumnsStateType
  getBatchReview: (props: BatchReviewProps) => {
    button?: React.ReactElement
    rowSelection?: TableProps<any>['rowSelection']
  }
  checkExportPermission: () => boolean
  checkTimeDetailExportPermission: () => boolean
}

class BaseConfig implements ConfigInterface {
  exportContent: string = ''

  timeDetailExportContent: string = ''

  // 控制字段显隐
  getColumnsState = (): ColumnsStateType => {
    return {}
  }

  getBatchReview = (
    props: BatchReviewProps,
  ): {
    button?: React.ReactElement
    rowSelection?: TableProps<any>['rowSelection']
  } => {
    return {}
  }

  checkExportPermission = () => {
    return false
  }

  checkTimeDetailExportPermission = () => {
    return false
  }
}

export default BaseConfig
