/* eslint-disable max-lines-per-function */
import React, { useMemo, useRef, useState } from 'react'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { useActivate } from '@/components/cacheRoute'
import preEntryService from '@/services/preEntry/list'
import { get, isEmpty } from 'lodash-es'
import dmsService from '@/services/dms'
import { handleSort } from '@/utils/utils'
import { QUERY_CONDITION } from '@/constants/dms/eventName'
import useExport from './useExport'
import useColumns from './useColumns'
import useBatchReview from './useBatchReview'
import useTimeDetailExport from './useTimeDetailExport'
import BaseConfig from './tableConfig/BaseConfig'
import InProcessConfig from './tableConfig/InProcessConfig'
import CompletedConfig from './tableConfig/CompletedConfig'
import { SortMap } from './constant'
import { TableStatus, SortParams } from './type'

type IProps = {
  status: TableStatus
}

// eslint-disable-next-line max-lines-per-function
const Table: React.FC<IProps> = ({ status }) => {
  const config = useMemo<BaseConfig>(() => {
    if (status === TableStatus.InProgress) {
      return new InProcessConfig()
    }
    if (status === TableStatus.Completed) {
      return new CompletedConfig()
    }
    return new BaseConfig()
  }, [])

  const tableAction = SchemaTable.createAction()
  // 记录每次列表所有的id，查看操作需要
  const [records, setRecords] = useState<any[]>([])
  const [total, setTotal] = useState<number>(0)

  const [searchColumns, columns] = useColumns(status, records, tableAction)

  // 保留查询条件值，导出时会用到
  const searchQuery = useRef<Record<string, any>>({})
  // 保留查询条件值，导出时会用到
  const exportQuery = useRef<Record<string, any>>({})
  // 保留查询条件值，name
  const exportQueryString = useRef<Record<string, any>>({})

  const { modal: exportModal, button: exportButton } = useExport(status, {
    getExportColumns: () => {
      const recruitType = searchColumns?.find?.((item) => item.dataIndex === 'recruitTypeCode')
      if (recruitType) {
        const restSearchColumns = searchColumns?.filter?.(
          (item) => item.dataIndex !== 'recruitTypeCode',
        )
        return [...(restSearchColumns ?? []), recruitType]
      }
      return searchColumns
    },
    getCountParams: () => {
      return searchQuery.current
    },
    getExportParams: () => {
      return exportQuery.current
    },
    getExportParamsName: () => {
      return exportQueryString.current
    },
  })

  const { modal: timeDetailExportModal, button: timeDetailExportButton } = useTimeDetailExport(
    status,
    {
      getExportColumns: () => {
        const recruitType = searchColumns?.find?.((item) => item.dataIndex === 'recruitTypeCode')
        if (recruitType) {
          const restSearchColumns = searchColumns?.filter?.(
            (item) => item.dataIndex !== 'recruitTypeCode',
          )
          return [...(restSearchColumns ?? []), recruitType]
        }
        return searchColumns
      },
      getCountParams: () => {
        return searchQuery.current
      },
      getExportParams: () => {
        return exportQuery.current
      },
      getExportParamsName: () => {
        return exportQueryString.current
      },
    },
  )

  const { action, button, rowSelection, tableAlertAction } = useBatchReview(status, tableAction)

  useActivate(() => {
    tableAction?.refresh()
  })

  return (
    <>
      <SchemaTable
        action={tableAction}
        columns={columns}
        searchColumns={searchColumns}
        onSearch={() => {
          action.clearKeys()
        }}
        request={async ({ filter: $filter, pagination, sorter }) => {
          // 需要特殊处理的字段
          const {
            departmentId,
            recruitResponsibleUserNums,
            timeEntry,
            enterEntryTime,
            recruitTypeCode,
            areaCode,
            employmentFormCodes,
            entryInvestigationResult,
            processProgresses,
            timeoutStatuses,
            ehrProgress,
            completeSituation,
            timeRecentSubmit,
            entryCityCodes,
            isPpTrainee,
            ...restFilter
          } = $filter

          const filter = {
            ...restFilter,
            areaCodes:
              areaCode && areaCode.length > 0
                ? areaCode.map((item) => item.value).join(',')
                : undefined,
            departmentId: departmentId && departmentId.value,
            employmentFormCodes:
              employmentFormCodes && employmentFormCodes.length > 0
                ? employmentFormCodes.map((item) => item.value).join(',')
                : undefined,
            recruitResponsibleUserNums:
              recruitResponsibleUserNums && recruitResponsibleUserNums.length > 0
                ? recruitResponsibleUserNums.map((item) => item.value).join(',')
                : undefined,
            entryCityCodes: entryCityCodes && entryCityCodes.map((v) => v.value).join(','),
            timeEntryBegin: timeEntry && timeEntry[0].startOf('day').valueOf(),
            timeEntryEnd: timeEntry && timeEntry[1].endOf('day').valueOf(),
            enterEntryTimeBegin: enterEntryTime && enterEntryTime[0].startOf('day').valueOf(),
            enterEntryTimeEnd: enterEntryTime && enterEntryTime[1].endOf('day').valueOf(),
            timeRecentSubmitBegin:
              timeRecentSubmit && timeRecentSubmit[0] && timeRecentSubmit[0].second(0).valueOf(),
            timeRecentSubmitEnd:
              timeRecentSubmit && timeRecentSubmit[1] && timeRecentSubmit[1].second(59).valueOf(),
            recruitTypeCode: recruitTypeCode && recruitTypeCode.value,
            processProgresses:
              processProgresses && processProgresses.length > 0
                ? processProgresses.map((item) => item.value).join(',')
                : undefined,
            entryInvestigationResult:
              entryInvestigationResult && entryInvestigationResult.length > 0
                ? entryInvestigationResult.map((item) => item.value).join(',')
                : undefined,
            timeoutStatuses:
              timeoutStatuses && timeoutStatuses.length > 0
                ? timeoutStatuses.map((item) => item.value).join(',')
                : undefined,
            ehrProgress: ehrProgress && ehrProgress.value,
            completeSituation: completeSituation && completeSituation.value,
            isPpTrainee,
          }

          const $exportQuery: Record<string, any> = {}
          if (areaCode && areaCode.length > 0) {
            $exportQuery.areaCodes = areaCode.map((item) => item.value)
          }
          if (completeSituation) {
            $exportQuery.completeSituation = completeSituation.value
          }
          if (departmentId) {
            $exportQuery.departmentId = departmentId.value
          }
          if (restFilter.ehrCandidateId) {
            $exportQuery.ehrCandidateId = Number(restFilter.ehrCandidateId)
          }
          if (ehrProgress) {
            $exportQuery.ehrProgress = ehrProgress.value
          }
          if (timeRecentSubmit) {
            $exportQuery.timeRecentSubmitBegin = timeRecentSubmit[0].valueOf()
            $exportQuery.timeRecentSubmitEnd = timeRecentSubmit[1].valueOf()
          }
          if (entryCityCodes) {
            $exportQuery.entryCityCodes = entryCityCodes.map((v) => v.value)
          }
          if (restFilter.entryPlaceName) {
            $exportQuery.entryPlaceName = restFilter.entryPlaceName
          }
          if (employmentFormCodes && employmentFormCodes.length > 0) {
            $exportQuery.employmentFormCodes = employmentFormCodes.map((item) => item.value)
          }
          if (restFilter.name) {
            $exportQuery.name = restFilter.name
          }
          if (restFilter.positionCandidateId) {
            $exportQuery.positionCandidateId = Number(restFilter.positionCandidateId)
          }
          if (restFilter.positionName) {
            $exportQuery.positionName = restFilter.positionName
          }
          if (processProgresses && processProgresses.length > 0) {
            $exportQuery.processProgresses = processProgresses.map((item) => item.value)
          }
          if (entryInvestigationResult && entryInvestigationResult.length > 0) {
            $exportQuery.entryInvestigationResult = entryInvestigationResult.map(
              (item) => item.value,
            )
          }
          if (recruitResponsibleUserNums && recruitResponsibleUserNums.length > 0) {
            $exportQuery.recruitResponsibleUserNums = recruitResponsibleUserNums.map(
              (item) => item.value,
            )
          }
          if (timeEntry) {
            $exportQuery.timeEntryBegin = timeEntry[0].startOf('day').valueOf()
            $exportQuery.timeEntryEnd = timeEntry[1].endOf('day').valueOf()
          }
          if (enterEntryTime) {
            $exportQuery.enterEntryTimeBegin = enterEntryTime[0].startOf('day').valueOf()
            $exportQuery.enterEntryTimeEnd = enterEntryTime[1].endOf('day').valueOf()
          }
          if (recruitTypeCode) {
            $exportQuery.recruitTypeCode = recruitTypeCode.value
          }
          if (timeoutStatuses) {
            $exportQuery.timeoutStatuses = timeoutStatuses.map((item) => item.value)
          }
          debugger
          if (isPpTrainee) {
            $exportQuery.isPpTrainee = isPpTrainee
          }

          // 导出展示导出字段，取字典label、时间区间处理
          const $exportQueryString = {
            ...restFilter,
            areaCode:
              areaCode && areaCode.length > 0
                ? areaCode.map((item) => item.label).join(',')
                : undefined,
            entryCityCodes: entryCityCodes && entryCityCodes.map((v) => v.label).join(','),
            departmentId: departmentId && departmentId.label.replace(/（.*?）/g, ''),
            employmentFormCodes:
              employmentFormCodes && employmentFormCodes.length > 0
                ? employmentFormCodes.map((item) => item.label).join('、')
                : undefined,
            recruitResponsibleUserNums:
              recruitResponsibleUserNums && recruitResponsibleUserNums.length > 0
                ? recruitResponsibleUserNums
                    .map((item) => `${item.label}（${item.value}）`)
                    .join('、')
                : undefined,
            timeEntry:
              timeEntry &&
              `${timeEntry[0].format('YYYY-MM-DD')} ~ ${timeEntry[1].format('YYYY-MM-DD')}`,
            enterEntryTime:
              enterEntryTime &&
              `${enterEntryTime[0].format('YYYY-MM-DD')} ~ ${enterEntryTime[1].format(
                'YYYY-MM-DD',
              )}`,
            timeRecentSubmit:
              timeRecentSubmit &&
              `${timeRecentSubmit[0].format('YYYY-MM-DD HH:mm')} ~ ${timeRecentSubmit[1].format(
                'YYYY-MM-DD HH:mm',
              )}`,
            recruitTypeCode: recruitTypeCode && recruitTypeCode.label,
            processProgresses:
              processProgresses && processProgresses.length > 0
                ? processProgresses.map((item) => item.label).join('、')
                : undefined,
            entryInvestigationResult:
              entryInvestigationResult && entryInvestigationResult.length > 0
                ? entryInvestigationResult.map((item) => item.label).join(',')
                : undefined,
            timeoutStatuses:
              timeoutStatuses && timeoutStatuses.length > 0
                ? timeoutStatuses.map((item) => item.label).join('、')
                : undefined,
            ehrProgress: ehrProgress && ehrProgress.label,
            completeSituation: completeSituation && completeSituation.label,
            isPpTrainee: isPpTrainee === 1 ? '仅看朴培生' : '不限',
          }

          exportQueryString.current = $exportQueryString
          exportQuery.current = $exportQuery
          searchQuery.current = filter
          if (!isEmpty(searchQuery.current)) {
            dmsService.trace(QUERY_CONDITION, {
              obmQueryCondition: searchQuery.current,
            })
          }

          let useSorter: SortParams[] = []

          if (sorter && Object.keys(sorter).length > 0) {
            const sorterKey = Object.keys(sorter)[0]
            const extendSortParams =
              status === TableStatus.InProgress
                ? SortMap.IN_PROCESS[sorterKey]
                : SortMap.COMPLETED[sorterKey]
            useSorter = [{ field: sorterKey, order: sorter[sorterKey] as string }].concat(
              extendSortParams,
            )
          }

          const res = await (status === TableStatus.InProgress
            ? preEntryService.getInProcessList
            : preEntryService.getCompletedList)({
            ...filter,
            sort: handleSort(useSorter),
            page: pagination.current,
            size: pagination.pageSize,
          })
          setRecords(res.data)
          setTotal(res.total || 0)
          return res
        }}
        toolbarOptions={{
          setting: true,
        }}
        columnsState={config.getColumnsState()}
        toolbar={{
          title: button,
          action: (
            <>
              {timeDetailExportButton}
              {exportButton}
            </>
          ),
        }}
        rowSelection={rowSelection}
        pagination={{
          onChange: () => {
            action.clearKeys()
          },
          total: total >= 10000 ? 10000 : total,
          showTotal: (t, range) => `总共 ${total} 个项目`,
          showQuickJumper: true,
        }}
        tableAlertAction={tableAlertAction}
        searchProps={{
          labelWidth: 110,
        }}
      />
      {exportModal}
      {timeDetailExportModal}
    </>
  )
}

export { TableStatus }

export default Table
