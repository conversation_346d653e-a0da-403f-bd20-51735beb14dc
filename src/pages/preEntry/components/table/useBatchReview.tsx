import { Alert, TableProps } from 'antd'
import React, { useMemo, useState } from 'react'
import { SchemaTableProps, createAction } from '@amazebird/antd-schema-table'
import { TableStatus } from './type'
import InProcessConfig from './tableConfig/InProcessConfig'
import CompletedConfig from './tableConfig/CompletedConfig'
import BaseConfig from './tableConfig/BaseConfig'
import style from './style.module.less'

function useBatchReview(
  status: TableStatus,
  tableAction: ReturnType<typeof createAction>,
): {
  action: {
    clearKeys: () => void
  }
  button?: React.ReactElement
  rowSelection: TableProps<any>['rowSelection']
  tableAlertAction?: SchemaTableProps['tableAlertAction']
} {
  const config = useMemo(() => {
    if (status === TableStatus.InProgress) {
      return new InProcessConfig()
    }
    if (status === TableStatus.Completed) {
      return new CompletedConfig()
    }
    return new BaseConfig()
  }, [])

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const { button, rowSelection } = useMemo(() => {
    return config.getBatchReview({
      tableAction,
      keys: selectedRowKeys,
      setKeys: setSelectedRowKeys,
    })
  }, [selectedRowKeys])

  const tableAlertAction = useMemo<SchemaTableProps['tableAlertAction']>(() => {
    if (selectedRowKeys.length === 0) {
      return undefined
    }
    return () => (
      <Alert
        className={style.alert}
        message={
          <div>
            <span className={style.alertContent}>{`已选 ${selectedRowKeys.length} 项`}</span>
            <span
              onClick={() => {
                setSelectedRowKeys([])
              }}
              className={style.clear}
            >
              清空选择
            </span>
          </div>
        }
        type="info"
      />
    )
  }, [selectedRowKeys])

  return {
    action: {
      clearKeys: () => {
        setSelectedRowKeys([])
      },
    },
    button,
    rowSelection,
    tableAlertAction,
  }
}

export default useBatchReview
