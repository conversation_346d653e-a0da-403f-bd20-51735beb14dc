import { SchemaTableProps } from '@amazebird/antd-schema-table'
import useCommonExport from '@/hooks/useExport'
import preEntryService from '@/services/preEntry/list'
import taskService from '@/services/task'
import dmsService from '@/services/dms'
import { downloadFile } from '@/utils/utils'
import { EXPORT_TIME_DETAIL } from '@/constants/dms/eventName'
import { Button, message } from 'antd'
import React, { useMemo } from 'react'
import { DownloadOutlined } from '@ant-design/icons'
import { TableStatus } from './type'
import InProcessConfig from './tableConfig/InProcessConfig'
import CompletedConfig from './tableConfig/CompletedConfig'
import BaseConfig from './tableConfig/BaseConfig'

function useTimeDetailExport(
  status: TableStatus,
  conf: {
    getExportColumns: () => SchemaTableProps['searchColumns']
    getCountParams: () => Record<string, any>
    getExportParams: () => Record<string, any>
    getExportParamsName: () => Record<string, any>
  },
) {
  const config = useMemo(() => {
    if (status === TableStatus.InProgress) {
      return new InProcessConfig()
    }
    if (status === TableStatus.Completed) {
      return new CompletedConfig()
    }
    return new BaseConfig()
  }, [])

  const { modal, action } = useCommonExport({
    columns: conf.getExportColumns(),
    getCount: async () => {
      const res = await (status === TableStatus.InProgress
        ? preEntryService.getInProcessList
        : preEntryService.getCompletedList)({
        ...conf.getCountParams(),
        page: 1,
        size: 10,
      })
      return res.total
    },
    onOk: async (taskController) => {
      const data = await taskController.createTask({
        service: 'admin/entry',
        params: {
          taskType: 2,
          taskModule: 'ENTRY_TIME_STATISTICS',
          taskArgs: {
            status,
            ...conf.getExportParams(),
          },
        },
      })
      if (data.status === 2 && data.objectKey) {
        const { url } = await taskService.upload.download(data.objectKey)
        downloadFile(url)
        dmsService.trace(EXPORT_TIME_DETAIL)
        return
      }
      message.error(data.errorMessage || '导出错误')
      throw new Error(data.errorMessage || '导出错误')
    },
    title: '导出时间明细',
    content: config.timeDetailExportContent,
  })

  const button = (
    <Button
      type="link"
      key="timeDetail"
      onClick={() => {
        action.open(conf.getExportParamsName())
      }}
    >
      <DownloadOutlined />
      时间明细
    </Button>
  )

  if (!config.checkTimeDetailExportPermission()) {
    return {}
  }

  return {
    button,
    modal,
  }
}

export default useTimeDetailExport
