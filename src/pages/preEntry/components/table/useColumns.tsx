import { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { SchemaColumnType, createAction, SchemaTableProps } from '@amazebird/antd-schema-table'
import { TableStatus } from './type'
import InProcessConfig from './tableConfig/column/InProcessColumnConfig'
import CompletedConfig from './tableConfig/column/CompletedColumnConfig'
import { ConfigInterface } from './tableConfig/column/BaseColumnConfig'

function useColumns(
  status: TableStatus,
  records: any[],
  action: ReturnType<typeof createAction>,
): [SchemaTableProps['searchColumns'], SchemaColumnType] {
  const navigate = useNavigate()

  const config = useMemo<ConfigInterface | undefined>(() => {
    if (status === TableStatus.InProgress) {
      return new InProcessConfig({ action, navigate, records })
    }
    if (status === TableStatus.Completed) {
      return new CompletedConfig({ action, navigate, records })
    }
    return undefined
  }, [records])

  const search = useMemo(() => {
    if (config) {
      return config.getSearchColumns()
    }
    return []
  }, [config])

  const table = useMemo(() => {
    if (config) {
      return config.getColumns()
    }
    return []
  }, [config])

  return [search, table]
}

export default useColumns
