/**
 * 时间字段排序规则
   上述需求路径下，列表中的时间字段支持切换顺序/倒序排列
   1. 进行中tab：默认按进入预入职日期倒序排列
      预计报到日期：时间相同时按进入预入职日期顺序，再按最近提交时间顺序，最后按dhr候选人id顺序
      进入预入职日期：时间相同时按最近提交时间顺序，再按预计报到日期顺序，最后按dhr候选人id顺序
      最近提交时间：时间相同时按进入预入职日期顺序，再按预计报到日期顺序，最后按dhr候选人id顺序
   2. 已完成tab：默认按进入已完成tab时间倒序排列
      预计报到日期：时间相同时按进入预入职日期顺序，再按审核完成时间顺序，最后按dhr候选人id顺序
      进入预入职日期：时间相同时按审核完成时间顺序，再按预计报到日期顺序，最后按dhr候选人id顺序
      审核完成时间：时间相同时按进入预入职日期顺序，再按预计报到日期顺序，最后按dhr候选人id顺序
 */
export const SortMap = {
  IN_PROCESS: {
    timeEntry: [
      { field: 'timeCreate', order: 'ascend' },
      { field: 'timeRecentSubmit', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
    timeCreate: [
      { field: 'timeRecentSubmit', order: 'ascend' },
      { field: 'timeEntry', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
    timeRecentSubmit: [
      { field: 'timeCreate', order: 'ascend' },
      { field: 'timeEntry', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
  },
  COMPLETED: {
    timeEntry: [
      { field: 'timeCreate', order: 'ascend' },
      { field: 'timeAuditComplete', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
    timeCreate: [
      { field: 'timeAuditComplete', order: 'ascend' },
      { field: 'timeEntry', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
    timeAuditComplete: [
      { field: 'timeCreate', order: 'ascend' },
      { field: 'timeEntry', order: 'ascend' },
      { field: 'positionCandidateId', order: 'ascend' },
    ],
  },
}

// 背调状态
export const backgroundInvestigationStatus = [
  {
    label: '--',
    value: 0,
  },
  {
    label: '绿灯',
    value: 1,
  },
  {
    label: '黄灯',
    value: 2,
  },
  {
    label: '红灯',
    value: 3,
  },
  {
    label: '待定',
    value: 4,
  },
  {
    label: '未完成',
    value: 5,
  },
  {
    label: '无需背调',
    value: 6,
  },
]
