import useCommonExport from '@/hooks/useExport'
import preEntryService from '@/services/preEntry/list'
import taskService from '@/services/task'
import { Button, message } from 'antd'
import { downloadFile } from '@/utils/utils'
import dmsService from '@/services/dms'
import { EXPORT_CANDIDATE } from '@/constants/dms/eventName'
import React, { useMemo } from 'react'
import { DownloadOutlined } from '@ant-design/icons'
import { SchemaTableProps } from '@amazebird/antd-schema-table'
import InProcessConfig from './tableConfig/InProcessConfig'
import CompletedConfig from './tableConfig/CompletedConfig'
import BaseConfig from './tableConfig/BaseConfig'
import { TableStatus } from './type'

function useExport(
  status: TableStatus,
  conf: {
    getExportColumns: () => SchemaTableProps['searchColumns']
    getCountParams: () => Record<string, any>
    getExportParams: () => Record<string, any>
    getExportParamsName: () => Record<string, any>
  },
) {
  const config = useMemo(() => {
    if (status === TableStatus.InProgress) {
      return new InProcessConfig()
    }
    if (status === TableStatus.Completed) {
      return new CompletedConfig()
    }
    return new BaseConfig()
  }, [])

  const { modal, action } = useCommonExport({
    columns: conf.getExportColumns(),
    getCount: async () => {
      const res = await (status === TableStatus.InProgress
        ? preEntryService.getInProcessList
        : preEntryService.getCompletedList)({
        ...conf.getCountParams(),
        page: 1,
        size: 10,
      })
      return res.total
    },
    onOk: async (taskController) => {
      const data = await taskController.createTask({
        service: 'admin/entry',
        params: {
          taskType: 1,
          taskModule: 'ENTRY_MATERIAL',
          taskArgs: {
            status,
            ...conf.getExportParams(),
          },
        },
      })
      if (data.status === 2 && data.objectKey) {
        const { url } = await taskService.upload.download(data.objectKey)
        downloadFile(url)
        dmsService.trace(EXPORT_CANDIDATE)
        return
      }
      message.error(data.errorMessage || '导出错误')
      throw new Error(data.errorMessage || '导出错误')
    },
    title: '新建导出任务',
    content: config.exportContent,
  })

  const button = (
    <Button
      type="link"
      key="export"
      onClick={() => {
        action.open(conf.getExportParamsName())
      }}
    >
      <DownloadOutlined />
      导出
    </Button>
  )

  if (!config.checkExportPermission()) {
    return {}
  }

  return {
    modal,
    button,
  }
}

export default useExport
