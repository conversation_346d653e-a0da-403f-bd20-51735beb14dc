import { DownOutlined, UpOutlined } from '@ant-design/icons'
import { Button, Dropdown, DropdownProps } from 'antd'
import React, { useEffect, useMemo, useRef, useState } from 'react'

// 仅用在预入职列表，该组件有副作用
type Iprops = DropdownProps

const MoreDropdown: React.FC<Iprops> = (props) => {
  const { menu } = props

  const [open, setOpen] = useState<boolean>(false)

  const handleOpenChange: DropdownProps['onOpenChange'] = ($open: boolean) => {
    setOpen($open)
  }

  useMemo(() => {
    if (menu) {
      // 副作用
      menu.onClick = (info) => {
        setOpen(false)
      }
    }
    return menu
  }, [])

  return menu?.items?.length && menu.items.length > 0 ? (
    <Dropdown open={open} onOpenChange={handleOpenChange} {...props} menu={menu}>
      <Button type="link" size="small" style={{ display: 'inline-block', alignItems: 'center' }}>
        <span>更多</span>
        {open ? (
          <UpOutlined key="open" />
        ) : (
          <UpOutlined key="close" style={{ transform: 'rotate(180deg)' }} />
        )}
      </Button>
    </Dropdown>
  ) : null
}

export default MoreDropdown
