.container {
  position: relative;

  // &Loader {
  //   height: calc(100vh - 220px);
  //   position: absolute;
  //   top: 16px;
  //   bottom: 0;
  //   left: 0;
  //   right: 0;
  //   z-index: 999;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   background: #fff;
  //   margin: 0 16px;
  // }

  :global {
    .@{ant-prefix}-tabs-nav {
      background: #fff;
      padding: 0 24px;
      margin-bottom: 24px;
    }

    .schema-table-search {
      background: #fff;
    }

    .schema-table {
      margin: 0 24px;
    }

    .schema-table-wrapper {
      background-color: #fff;
    }

    .schema-table-toolbar-title, .schema-table-toolbar-action {
      padding-top: 0;
    }
  }
}
