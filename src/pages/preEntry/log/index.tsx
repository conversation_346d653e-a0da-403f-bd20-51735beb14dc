import { SchemaTable } from '@amazebird/antd-schema-table'
import React, { useCallback, useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import dmsService from '@/services/dms'
import materialService from '@/services/preEntry/material'
import { OPERATION_RECORD } from '@/constants/dms/pageName'
import useTimer, { CallbackType } from '@/hooks/useTimer'
import { PAGE_VIEW } from '@/constants/dms/eventName'
import { useStore } from '@/stores'
import useColumns from './useColumns'
import style from './style.module.less'

const Log: React.FC = () => {
  const { setTitle } = useStore()
  const [searchParams] = useSearchParams()

  const id = useMemo(() => {
    const idStr = searchParams.get('id')
    if (!idStr) {
      return undefined
    }
    const idNum = Number(idStr)
    if (Number.isNaN(idNum)) {
      return undefined
    }
    return idNum
  }, [searchParams])

  const title = useMemo(() => {
    const titleStr = searchParams.get('title')
    if (!titleStr) {
      return undefined
    }
    return titleStr
  }, [])

  useEffect(() => {
    setTitle(title)
  }, [title])

  useEffect(() => {
    dmsService.injectPageContext({
      pageName: OPERATION_RECORD,
    })
  }, [])

  const pageView = useCallback<CallbackType>(({ duration, start, end }) => {
    dmsService.trace(PAGE_VIEW, {
      timeBehavior: end,
      pageStayTime: duration,
      timeBehaviour__when__: start,
    })
  }, [])
  useTimer(pageView)

  const columns = useColumns()

  return (
    <div className={style.log}>
      <SchemaTable
        request={async ({ pagination }) => {
          if (typeof id === 'undefined') {
            return {
              total: 0,
              data: [],
            }
          }
          const res = await materialService.getOperationLogList({
            id,
            page: pagination.current!,
            size: pagination.pageSize!,
          })
          return {
            total: res.count,
            data: res.data,
          }
        }}
        columns={columns}
        rowKey={(record, index) => JSON.stringify(record) + index}
      />
    </div>
  )
}

export default Log
