import { SchemaColumnType } from '@amazebird/antd-schema-table'
import { useMemo } from 'react'

function useColumns(): SchemaColumnType {
  return useMemo<SchemaColumnType>(() => {
    return [
      {
        dataIndex: 'time',
        title: '操作时间',
        width: 150,
        cell: 'DateTime',
      },
      {
        dataIndex: 'content',
        title: '操作内容',
        width: 550,
      },
      {
        dataIndex: 'user',
        title: '操作人',
        width: 150,
      },
    ]
  }, [])
}

export default useColumns
