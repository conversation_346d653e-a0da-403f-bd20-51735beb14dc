import { Tabs } from 'antd'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import dmsService from '@/services/dms'
import { PREENTRY_LIST } from '@/constants/dms/pageName'
import { PAGE_VIEW } from '@/constants/dms/eventName'
import useTimer, { CallbackType } from '@/hooks/useTimer'
import { getPermission } from '@galaxy/rbac'
import { CompletedTab, InProcessTab } from '@/constants/rbac/preEntry'
import { useStore } from '@/stores'
import style from './style.module.less'
import Table, { TableStatus } from './components/table'

const PreEntry: React.FC = () => {
  const { title, setTitle, setExtra } = useStore()
  const items = useMemo(() => {
    return [
      {
        label: '进行中',
        key: 'underway',
        children: <Table status={TableStatus.InProgress} />,
        hasPermission: getPermission(InProcessTab),
      },
      {
        label: '已完成',
        key: 'finished',
        children: <Table status={TableStatus.Completed} />,
        hasPermission: getPermission(CompletedTab),
      },
    ].filter((item) => {
      return item.hasPermission
    })
  }, [])
  const [key, setKey] = useState<string>(items[0]?.key)

  useEffect(() => {
    dmsService.injectPageContext({
      pageName: PREENTRY_LIST,
      obmOnboardingListTab: key,
    })
  }, [key])

  const pageView = useCallback<CallbackType>(({ duration, start, end }) => {
    dmsService.trace(PAGE_VIEW, {
      timeBehavior: end,
      pageStayTime: duration,
      timeBehaviour__when__: start,
    })
  }, [])

  useTimer(pageView)

  return (
    <div className={style.container}>
      <Tabs
        activeKey={key}
        onChange={(activeKey) => {
          setKey(activeKey)
        }}
        items={items}
        tabBarStyle={{
          paddingLeft: '24px',
          paddingRight: '24px',
        }}
      />
    </div>
  )
}

export default PreEntry
