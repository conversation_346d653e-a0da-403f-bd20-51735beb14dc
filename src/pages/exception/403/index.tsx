import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Result, Button } from 'antd'
import Router from '@/services/router'

export default function () {
  return (
    <Result
      status="403"
      title="403"
      style={{
        background: 'none',
      }}
      subTitle="对不起，你不能访问这个页面"
      extra={
        <Link to={Router.path('/')}>
          <Button type="primary">首页</Button>
        </Link>
      }
    />
  )
}
