/**
 * @description 顶部入职信息
 */
import React, { useEffect, useRef, useState, useMemo } from 'react'
import { Button, Descriptions, Badge, Tooltip, Alert } from 'antd'
import { useStore } from '@/stores'
import { useSize, useDebounceEffect } from 'ahooks'
import moment from 'moment'
import classNames from 'classnames'
import { getUrlParam } from '@/utils/utils'
import {
  PROCESS_PROGRESS_STATUS_TEXT,
  PROCESS_COLOR_STYLE,
  PROCESS_COLOR_CLASS_NAME,
  PROCESS_PROGRESS_STATUS,
} from '@/constants/review'
import ModifyResult from '@/components/ModifyResult'
import { useInitService } from '@/pages/entryMaterialsAudit/initContext'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import SpecialPass from '@/components/SpecialPass'
import TraceRecord from '@/pages/preEntry/components/table/tableConfig/column/TraceRecord'
import { TraceRecordBtn } from '@/constants/rbac/preEntry'
import { getPermission } from '@galaxy/rbac'
import informationBlockService from './AuditContent/InformationBlock/service'
import dictService from './dictService'
import styles from './style.module.less'

// 自定义label样式
const labelStyle = {
  display: 'flex',
  width: '100px',
  justifyContent: 'flex-end',
  color: '#999',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
}

const alterMessageId = 'alter_message'

const TopEntryInfo: React.FC = () => {
  const entryMaterialId = getUrlParam('id', window.location.href)
  const { getDictMap } = dictService
  const { isCanClickEditAuditResult, isHideEditAuditResult } = businessAuthor
  const service = useInitService()
  const { formData } = informationBlockService
  const { setTitle, setExtra, setClosely, auditStatus, setBack } = useStore()
  const processColor = PROCESS_COLOR_STYLE[formData?.processInfo?.processProgress]
  const processTextClassName = PROCESS_COLOR_CLASS_NAME[formData?.processInfo?.processProgress]
  const processText = PROCESS_PROGRESS_STATUS_TEXT[formData?.processInfo?.processProgress]
  const ref = useRef(null)
  const traceRecordRef = useRef(null)
  const size = useSize(ref)
  const [isShowDetailOption, setIsShowDetailOption] = useState<boolean>(false)
  const [alertRecordText, setAlertRecordText] = useState('')

  const customAction = {
    refresh: (content) => {
      setAlertRecordText(content)
    },
  }

  useEffect(() => {
    setAlertRecordText(formData.otherInfo?.entryMaterialProcessTrackInfo || '')
  }, [formData.otherInfo?.entryMaterialProcessTrackInfo])

  useEffect(() => {
    setTitle(
      <div className={styles.head}>
        <span className={styles.name}>{formData?.identityInfo?.name}</span>
        <Badge
          color={processColor}
          text={
            formData?.processInfo?.processProgress ===
            PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED ? (
              <span className={classNames(styles[processTextClassName], styles.process_text)}>
                <SpecialPass dataSource={auditStatus?.specialApprovalInfo} />
              </span>
            ) : (
              <span className={classNames(styles[processTextClassName], styles.process_text)}>
                {processText}
              </span>
            )
          }
        />
      </div>,
    )
    setExtra(
      <>
        {!isHideEditAuditResult() && (
          <Button
            type="primary"
            onClick={() => {
              ModifyResult.confirm({
                id: entryMaterialId,
                name: formData.identityInfo?.name || '',
                onOk: () => {
                  service.upLoad()
                },
              })
            }}
            disabled={!isCanClickEditAuditResult()}
          >
            修改审核结果
          </Button>
        )}
        {getPermission(TraceRecordBtn) && (
          <Tooltip
            placement="bottomRight"
            title={
              <div style={{ whiteSpace: 'pre-wrap' }}>{alertRecordText || '暂无过程跟踪记录'}</div>
            }
          >
            <Button
              type="primary"
              onClick={() => {
                traceRecordRef.current?.open?.()
              }}
            >
              过程跟踪记录
            </Button>
          </Tooltip>
        )}
      </>,
    )
    setClosely(true)
    setBack(true)

    return () => {
      setExtra(null)
      setTitle(null)
      setBack(false)
      setClosely(false)
    }
  }, [alertRecordText])

  useDebounceEffect(
    () => {
      const container = document.getElementById(alterMessageId) as HTMLElement
      if (container && container?.scrollWidth > container?.clientWidth) {
        setIsShowDetailOption(true)
      } else {
        setIsShowDetailOption(false)
      }
    },
    [size],
    {
      wait: 500,
    },
  )

  const alterMessageText = useMemo(() => {
    return (
      <>
        复核驳回原因：
        {formData.otherInfo?.rejectReasonCodes
          ?.map((item: string) => {
            return getDictMap('OMS_INFORMATION_FAILED_REASON', { isRenderParent: true })[item]
          })
          .join('、')}
        {formData.otherInfo?.rejectReason && (
          <>
            <span style={{ paddingLeft: 16 }}>说明：</span>
            <span>{formData.otherInfo.rejectReason}</span>
          </>
        )}
      </>
    )
  }, [formData])

  return (
    <div className={styles.top_base_info}>
      <Descriptions labelStyle={labelStyle as React.CSSProperties} column={4}>
        <Descriptions.Item label="证件号码">{formData.identityInfo?.idCardNum}</Descriptions.Item>
        <Descriptions.Item label="年龄">{formData.identityInfo?.age}</Descriptions.Item>
        <Descriptions.Item label="联系方式">
          {formData.identityInfo?.contactPhone}
        </Descriptions.Item>
        <Descriptions.Item label="预计报到日期">
          {formData.entryInfo?.timeEntry
            ? moment(formData.entryInfo?.timeEntry).format('YYYY-MM-DD')
            : null}
        </Descriptions.Item>
        <Descriptions.Item label="用工形式">
          {getDictMap('BM_35')[formData.entryInfo?.employmentFormCode]}
        </Descriptions.Item>
        <Descriptions.Item label="员工类型">
          {getDictMap('BM_YGLX')[formData.entryInfo?.employmentTypeCode]}
        </Descriptions.Item>
        <Descriptions.Item label="预计报到部门">
          {formData.entryInfo?.departmentName}
        </Descriptions.Item>
        <Descriptions.Item label="报到岗位">{formData.entryInfo?.positionName}</Descriptions.Item>
        <Descriptions.Item label="合同主体">
          {getDictMap('BM_HTZT')[formData.entryInfo?.contractEntityCode]}
        </Descriptions.Item>
        {/* <Descriptions.Item label="合同期限">
          {formData.entryInfo?.contractPeriod ? `${formData.entryInfo?.contractPeriod}年` : ''}
        </Descriptions.Item>
        <Descriptions.Item label="合同签订日期">
          {formData.entryInfo?.timeContractSigned
            ? moment(formData.entryInfo?.timeContractSigned).format('YYYY-MM-DD')
            : null}
        </Descriptions.Item>
        <Descriptions.Item label="合同到期日期">
          {formData.entryInfo?.timeContractEnd
            ? moment(formData.entryInfo?.timeContractEnd).format('YYYY-MM-DD')
            : null}
        </Descriptions.Item> */}
      </Descriptions>
      {formData?.processInfo?.processProgress === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED && (
        <Alert
          message={
            <div className={styles.alter_message}>
              <div className={styles.alter_context} id={alterMessageId} ref={ref}>
                {alterMessageText}
              </div>
              {isShowDetailOption ? (
                <Tooltip placement="topRight" title={alterMessageText}>
                  <div className={styles.alter_option}>详情</div>
                </Tooltip>
              ) : null}
            </div>
          }
          type="warning"
          showIcon
        />
      )}
      <TraceRecord
        key={`${formData?.entryInfo?.id} ${alertRecordText}`}
        action={customAction as any}
        id={formData?.entryInfo?.id}
        ref={(node) => {
          if (node) {
            traceRecordRef.current = node
          }
        }}
        content={alertRecordText}
      />
    </div>
  )
}

export default TopEntryInfo
