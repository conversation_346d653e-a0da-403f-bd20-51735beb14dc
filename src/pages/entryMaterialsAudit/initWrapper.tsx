/**
 * @desc 入职材料审核初始化包装器
 */
import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Skeleton, Result, message } from 'antd'
import { cloneDeep } from 'lodash-es'
import { reportBizError } from '@galaxy/utils'
import { getUrlParam } from '@/utils/utils'
import reviewService from '@/services/preEntry/review'
import RouterService from '@/services/router'
import { useStore } from '@/stores'
import { ProcessProgress } from '@/services/preEntry/list'
import businessAuthor from './businessAuthor'
import informationBlockService from './AuditContent/InformationBlock/service'
import dictService from './dictService'
import { InitContext } from './initContext'

export default (WrappedComponent) => {
  const initWrapper = (props) => {
    const { setProcess, setEhrProgress, setIsNeedBadgeCode, hasCanEditInfoAuth } = businessAuthor
    const { initAudit } = useStore()
    const { initFormData, initOriginFormData } = informationBlockService
    const { fetchDict } = dictService
    const [loaded, setLoaded] = useState<boolean>(false)
    const [hasAuditDataPermission, setHasAuditDataPermission] = useState<boolean>(true)
    const navigate = useNavigate()

    const upLoad = async (): Promise<void> => {
      try {
        const entryMaterialId = getUrlParam('id', window.location.href)
        if (!entryMaterialId || entryMaterialId === 'null' || entryMaterialId === 'undefined') {
          reportBizError('获取审核材料详情：url丢失id')
          message.warn('获取审核材料详情：入职材料id不存在')
          navigate(RouterService.path('/preEntry'))
          return
        }
        setLoaded(false)
        let formData = await reviewService.getEntryMaterialDetail(entryMaterialId)
        // formData.processInfo.processProgress = 2
        let statusData = await reviewService.getEntryMaterialAuditInfo(entryMaterialId)
        await fetchDict()
        setProcess(formData.processInfo?.processProgress)
        setEhrProgress(formData.processInfo?.ehrProgress)
        setIsNeedBadgeCode(formData.entryInfo?.isNeedBadgeCode)
        initOriginFormData(cloneDeep(formData))
        // 草稿的相关处理要在setProcess之后（涉及权限判断） - 2024.3.6
        if (
          hasCanEditInfoAuth() &&
          [ProcessProgress.Audit, ProcessProgress.PendingReview].includes(
            formData?.processInfo?.processProgress,
          )
        ) {
          await reviewService.loadDraft(formData)
          formData = reviewService.handleFormAndDraftData(formData)
          statusData = reviewService.handleStatusAndDraftData(statusData)
        }
        initAudit(statusData)
        initFormData(formData)
        setLoaded(true)
      } catch (error) {
        setHasAuditDataPermission(false)
      }
    }

    useEffect(() => {
      ;(async () => {
        upLoad()
      })()
    }, [])

    if (!hasAuditDataPermission) {
      return <Result status="403" title="403" subTitle="当前账号数据权限不足，无法查看" />
    }

    if (!loaded) {
      return (
        <div
          style={{
            padding: '20px',
          }}
        >
          <Skeleton active paragraph={{ rows: 8 }} />
        </div>
      )
    }

    return (
      <InitContext.Provider
        value={{
          upLoad,
        }}
      >
        <WrappedComponent {...props} />
      </InitContext.Provider>
    )
  }
  return initWrapper
}
