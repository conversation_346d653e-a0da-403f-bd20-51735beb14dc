/**
 * @name 提交审核结果弹窗
 */
import React, { useState } from 'react'
import { Modal, Button, Tooltip, Space, Divider, Radio, message, Row, Col, Checkbox } from 'antd'
import { useMount, useDebounceFn, useUpdate } from 'ahooks'
import dayjs from 'dayjs'
import { cloneDeep, isNil } from 'lodash-es'
import { useNavigate } from 'react-router-dom'
import type { SchemaType } from '@amazebird/schema-form'
import { Item, SchemaForm, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import { QuestionCircleOutlined, WarningOutlined } from '@ant-design/icons'
import { getLocalStorage, setLocalStorage, delLocalStorage, reportBizError } from '@galaxy/utils'
import ReModal from '@/components/ReModal'
import ExtraDateFooter from '@/components/ExtraDateFooter'
import {
  personPhotoAuditSchema,
  workCardPhotoAuditSchema,
  personBaseInfoAuditSchema,
  identityInfoAuditSchema,
  bankCardInfoAuditSchema,
  educationExperienceAuditSchema,
  resignationCertificateAuditSchema,
  healthInfoAuditSchema,
  relationshipInfoAuditSchema,
  otherInfoAuditSchema,
} from '@/pages/entryMaterialsAudit/config/auditConfig'
import userService from '@/services/user'
import reviewService from '@/services/preEntry/review'
import configService from '@/services/config'
import RouterService from '@/services/router'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { useInitService } from '@/pages/entryMaterialsAudit/initContext'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import { getUrlParam } from '@/utils/utils'
import {
  getEducationExperienceValidateFields,
  renderTitle,
} from '@/pages/entryMaterialsAudit/config/config'
import templateService, { TemplateData } from '@/services/template'
import dmsService from '@/services/dms'
import { MATERIAL_VERSION_UPDATE, PASS, SUBMIT_RESULT_SUCCESS } from '@/constants/dms/eventName'
import eduInfoService from '@/services/preEntry/eudcationExperience'
import useCacheAsync from './useCacheAsync'
import {
  handleAuditResultActionStatus,
  handleAuditResultSubmitData,
  isExistNotInConfig,
} from './submitUtils'
import ModalAudit from '../AuditContent/AuditBlock/ModalAudit'
import styles from './style.module.less'

const layoutCol = {
  wrapperCol: { span: 24 },
  labelCol: { span: 0 },
}
interface SubmitAuditResultModalProps {
  visible: boolean
  onCancel: () => void
  onOk: () => void
  fieldsValue: Record<string, any>
  hasNext: boolean
  handleNextClick: () => void
  /* 外部form，用来校验教育经历模块 */
  form: any
}

const msgKey = 'updatable'
const JUMP_NEXT_KEY = `entryMaterialsAudit_autoJump${
  userService.info?.num ?? userService.info?.userName
}`
const expires = 60 * 60 * 24 * 30 // 30天

// eslint-disable-next-line max-lines-per-function
const SubmitAuditResultModal: React.FC<SubmitAuditResultModalProps> = (props) => {
  const { visible, onCancel, onOk, fieldsValue, hasNext, handleNextClick, form } = props
  const modalForm: any = SchemaForm.createForm()
  const navigate = useNavigate()
  const service = useInitService()
  const upDate = useUpdate()
  const { formData } = informationBlockService
  const { hasNeedBadge } = businessAuthor
  const [lockOneKeyCompliance, setLockOneKeyCompliance] = useState<boolean>(false)
  const [filterRenderTitle, setFilterRenderTitle] = useState<Record<string, string>>({})
  const [schemaFormValuesChange, setSchemaFormValuesChange] = useState<boolean>(false)
  const [submitDisabed, setSubmitDisabled] = useState(false)
  const [originEducationExperienceAudit, setOriginEducationExperienceAudit] = useState<number>() // 外层表单教育经历审核字段缓存值

  useMount(() => {
    const title = Object.fromEntries(
      Object.entries(renderTitle).filter(([key]) => {
        if (!hasNeedBadge() && key === 'workCardPhoto') {
          return false
        }
        if (
          !informationBlockService.formData.otherInfo?.isHasWorkExperienceModule &&
          key === 'resignationCertificate'
        ) {
          return false
        }
        return true
      }),
    )
    setFilterRenderTitle(title)
    // 过滤出fieldsValues中审核符合与不符合结果的值的字段跟值
    const filterAuditResultFields: any = Object.keys(fieldsValue).reduce((acc, cur) => {
      if (cur.includes('_itemAuditResult')) {
        acc[cur] = fieldsValue[cur]
      }
      return acc
    }, {})
    const tmpActionStatus = handleAuditResultActionStatus(filterAuditResultFields)
    // @ts-ignore
    modalForm.setState({ $watchActionStatus: tmpActionStatus })
    // 保存外层表单教育经历审核字段的值
    setOriginEducationExperienceAudit(form.getFieldValue('educationExperience_itemAuditResult'))
  })

  const { cacheAsync } = useCacheAsync({ cacheKey: 'getRuleData' })
  /**
   * 驳回修改通知模板、要求完成时间是否可见
   */
  const observerRejectOrTime = (): any => {
    return Observer({
      watch: `auditResult`,
      action: async (auditResult): Promise<boolean> => {
        try {
          if (auditResult === 3) {
            setSubmitDisabled(true)
            const data = await cacheAsync(configService.getRuleData, { type: 1 })
            setSubmitDisabled(false)
            return isExistNotInConfig(modalForm.getFieldsValue(), data?.withoutNotice || [])
          }
          return false
        } catch (err) {
          setSubmitDisabled(false)
          return false
        }
      },
    })
  }

  const schema: SchemaType = {
    ...personPhotoAuditSchema(true),
    ...workCardPhotoAuditSchema(true),
    ...personBaseInfoAuditSchema(true),
    ...identityInfoAuditSchema(true),
    ...bankCardInfoAuditSchema(true),
    ...educationExperienceAuditSchema(true),
    ...resignationCertificateAuditSchema(true),
    ...healthInfoAuditSchema(true),
    ...relationshipInfoAuditSchema(true),
    ...otherInfoAuditSchema(true),
    auditResult: {
      label: '',
      component: (props1) => <Radio.Group {...props1} />,
      options: Observer({
        watch: `#$watchActionStatus`,
        action: (actionStatus) => {
          return [
            { label: '审核通过', value: 1, disabled: !actionStatus?.pass },
            { label: '审核不通过', value: 2, disabled: !actionStatus?.notPass },
            { label: '驳回修改', value: 3, disabled: !actionStatus?.reject },
          ]
        },
      }),
      required: [true, '请选择'],
    },
    rejectModifyTemplateId: {
      label: '',
      component: 'SelectTooltip',
      options: async () => {
        const { data } = await templateService.getList({
          typeCode: 'OMS_TT_002',
          noPage: true,
        })
        // 默认选中第一个
        modalForm.setFieldsValue({
          rejectModifyTemplateId: data?.[0]?.id,
        })
        return data.map((item) => ({
          value: item.id,
          label: item.name,
        }))
      },
      placeholder: '请选择驳回修改通知模板',
      required: [true, '请选择'],
      visible: observerRejectOrTime(),
      props: {
        noOptionsTip: '没有可用的模板，请联系管理员进行配置',
      },
    },
    timeRequireComplete: {
      label: '',
      component: 'DatePicker',
      props: {
        showTime: true,
        showToday: false,
        showNow: false,
        format: 'YYYY-MM-DD HH:mm',
        disabledDate: (currentDate) => {
          const currentDay = dayjs().startOf('day')
          const lastDay = dayjs().add(365, 'day').endOf('day')
          return dayjs(currentDate).isBefore(currentDay) || dayjs(currentDate).isAfter(lastDay)
        },
        renderExtraFooter: () => (
          <ExtraDateFooter
            onClick={(targetDate) => {
              modalForm.setFieldsValue({
                timeRequireComplete: targetDate,
              })
            }}
          />
        ),
      },
      placeholder: '请选择要求完成时间',
      required: [true, '请选择'],
      visible: observerRejectOrTime(),
    },
  }

  /**
   * 判断一键符合按钮是否禁用
   */
  const oneKeyComplianceDisabled = (): boolean => {
    if (lockOneKeyCompliance) {
      return true
    }
    const getFieldsValue = schemaFormValuesChange ? modalForm.getFieldsValue() : fieldsValue
    const arr = Object.keys(filterRenderTitle).filter((key) => {
      if (isNil(getFieldsValue[`${key}_itemAuditResult`])) {
        return true
      }
      return false
    })
    if (arr.length === 0) {
      return true
    }
    return false
  }

  /**
   * 一键“符合” 将未选择的模块全部设置为符合
   */
  const oneKeyCompliance = (): void => {
    const getFieldsValue = modalForm.getFieldsValue()
    Object.keys(filterRenderTitle).forEach((key) => {
      if (isNil(getFieldsValue[`${key}_itemAuditResult`])) {
        modalForm.setFieldsValue({
          [`${key}_itemAuditResult`]: 1,
        })
      }
    })
    const tmpActionStatus = handleAuditResultActionStatus(
      Object.keys(getFieldsValue).reduce((acc, cur) => {
        if (cur.includes('_itemAuditResult')) {
          if (isNil(getFieldsValue[cur])) {
            acc[cur] = 1
          } else {
            acc[cur] = getFieldsValue[cur]
          }
        }
        return acc
      }, {}),
    )
    // @ts-ignore
    modalForm.setState({ $watchActionStatus: tmpActionStatus })
    setLockOneKeyCompliance(true)
  }

  /**
   * 表单值改变
   * @param changedValues  当前改变的值
   * @param allValues  当前表单所有值
   */
  const schemaValuesChange = (changedValues: any, allValues: any): void => {
    // 特殊处理教育经历错误提示
    if (
      'educationExperience_itemAuditResult' in changedValues &&
      modalForm.getFieldValue('educationExperience_itemAuditResult') !== undefined
    ) {
      modalForm.setFields([
        {
          name: 'educationExperience_itemAuditResult',
          errors: [],
        },
      ])
    }
    const tmpActionStatus = handleAuditResultActionStatus(allValues)
    // @ts-ignore
    modalForm.setState({ $watchActionStatus: tmpActionStatus })
    // 当审核结果、不通过原因改变时将审核意见置空
    const key = Object.keys(changedValues)[0]
    if (key && (key.includes('_itemAuditResult') || key.includes('_noPassReasonCodes'))) {
      setSchemaFormValuesChange(true)
      upDate()
      modalForm.setFieldsValue({
        auditResult: undefined,
      })
    }
  }

  /**
   * 是否自动跳转下一份
   */
  const onCheckChange = (e): void => {
    if (e.target.checked) {
      setLocalStorage(`${JUMP_NEXT_KEY}`, JSON.stringify(true), expires)
    } else {
      delLocalStorage(JUMP_NEXT_KEY)
    }
  }

  /**
   * @description: 学历是否缺失校验
   * @return {*}
   */
  const handleEducationRequired = () => {
    const educationExperiences = form.getFieldValue('educationExperiences')
    const { check, missWord } = eduInfoService.linkCheck(educationExperiences)
    if (!check) {
      message.error(missWord)
      return false
    }
    return true
  }

  /**
   * 提交审核结果
   */
  const { run: onSubmit } = useDebounceFn(
    () => {
      modalForm
        .validateFields()
        .then(async (values: any) => {
          message.loading({ content: '提交中...', key: msgKey, duration: 0 })
          const cloneFieldsValue = cloneDeep(fieldsValue)
          Object.keys(cloneFieldsValue).forEach((key) => {
            if (
              key.includes('_itemAuditResult') ||
              key.includes('_noPassReason') ||
              key.includes('_noPassReasonCodes')
            ) {
              delete cloneFieldsValue[key]
            }
          })
          const finalFieldsValues = {
            ...cloneFieldsValue,
            ...values,
          }
          // submitData as EntryMaterialSubmit
          const submitData = await handleAuditResultSubmitData(finalFieldsValues)
          const entryMaterialId = getUrlParam('id', window.location.href)
          try {
            await reviewService.submitEntryMaterialAuditInfo(
              entryMaterialId,
              formData.processInfo?.version,
              submitData,
            )
            onOk()
            const isNeedJumpNext = getLocalStorage(JUMP_NEXT_KEY) && hasNext
            let content
            if (values.rejectModifyTemplateId) {
              content = `审核成功，已通知候选人修改补录信息${
                isNeedJumpNext ? '，自动跳转下一份...' : ''
              }`
            } else {
              content = `审核成功${isNeedJumpNext ? '，自动跳转下一份...' : ''}`
            }
            dmsService.trace(SUBMIT_RESULT_SUCCESS)
            message.success({
              content,
              key: msgKey,
              duration: 2,
            })
            if (isNeedJumpNext) {
              handleNextClick()
            } else {
              navigate(RouterService.path(`/preEntry`))
            }
            // 删除草稿缓存
            reviewService.deleteDraft(entryMaterialId)
          } catch (error: any) {
            reportBizError('材料审核提交失败', {
              errorMsg: error?.msg || error?.message,
            })
            if (error?.code === 'ENTRY/ENTRY_MATERIAL_VERSION_EXPIRE') {
              message.destroy()
              ReModal.confirm({
                title: '候选人有更新提交内容',
                content: `提交异常，检测到候选人有新提交的数据，请点击“更新”查看最新数据后重新审核`,
                onOk: () => {
                  dmsService.trace(MATERIAL_VERSION_UPDATE)
                  service.upLoad()
                },
                okText: '更新',
              })
            } else {
              const msg = error?.msg || '审核失败，请稍后再试'
              message.error({ content: msg, key: msgKey, duration: 2 })
            }
          }
        })
        .catch((err) => {
          if (err && 'errorFields' in err) {
            reportBizError('材料审核：弹窗提交时字段校验错误', {
              errorMsg: JSON.stringify(err.errorFields),
            })
            return null
          }
          return Promise.reject(err)
        })
    },
    {
      wait: 500,
    },
  )

  const onSubmitWithRequireCheck = () => {
    // 教育经历选择符合，需要额外做必填校验的逻辑以及链路校验
    if (modalForm.getFieldValue('educationExperience_itemAuditResult') === 1) {
      form.setFieldsValue({ educationExperience_itemAuditResult: 1 }) // 外层校验字段修改为'符合',强制触发必填校验
      setTimeout(() => {
        form
          .validateFields(
            getEducationExperienceValidateFields(formData.educationExperiences.length),
          )
          .then(async (values) => {
            if (!handleEducationRequired()) {
              return
            }
            onSubmit()
          })
          .catch((err) => {
            modalForm.setFields([
              {
                name: 'educationExperience_itemAuditResult',
                errors: ['教育经历不完整，仅可选择“不符合”'],
              },
            ])
          })
      }, 100)
    }
    // 选择不符合直接执行提交逻辑
    else {
      onSubmit()
    }
  }

  const handleCancel = (): void => {
    ReModal.confirm({
      title: '确定取消吗？',
      content: `取消后，已修改的数据将不会保存`,
      onOk: () => {
        // 还原教育经历外层表单审核字段的选项值
        form.setFieldsValue({ educationExperience_itemAuditResult: originEducationExperienceAudit })
        onCancel()
      },
    })
  }

  return (
    <SchemaForm
      schema={schema}
      onValuesChange={schemaValuesChange}
      form={modalForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
    >
      <Modal
        title="提交审核结果"
        open={visible}
        onCancel={handleCancel}
        footer={
          <Space>
            <Checkbox defaultChecked={!!getLocalStorage(JUMP_NEXT_KEY)} onChange={onCheckChange}>
              提交成功后自动跳转下一份
            </Checkbox>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={onSubmitWithRequireCheck} disabled={submitDisabed}>
              确定
            </Button>
          </Space>
        }
        width={900}
        className={styles.modal}
      >
        <Space className={styles.modal_item_title}>
          <span className={styles.title}>各模块审核情况 : </span>
          <Button
            onClick={() => {
              dmsService.trace(PASS)
              oneKeyCompliance()
            }}
            disabled={oneKeyComplianceDisabled()}
          >
            一键“符合”
          </Button>
          <Tooltip title="点击按钮自动将未选择结果的模块全部填充为“符合”">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
        <FormItemGrid colCount={2}>
          {Object.keys(filterRenderTitle).map((key) => {
            return <ModalAudit key={key} indexes={key} form={modalForm} fieldsValue={fieldsValue} />
          })}
        </FormItemGrid>
        <Divider dashed />
        <Space className={styles.modal_item_title}>
          <div className={styles.audit_result}>
            <div className={styles.audit_result_label}>审核结果：</div>
            <div className={styles.audit_result_wrapper}>
              <Row className={styles.row} gutter={16}>
                <Col flex="325px">
                  <Item field="auditResult" {...layoutCol} />
                </Col>
                <Col flex="200px">
                  <Item field="rejectModifyTemplateId" {...layoutCol} />
                </Col>
                <Col flex="200px">
                  <Item field="timeRequireComplete" {...layoutCol} />
                </Col>
              </Row>
            </div>
          </div>
        </Space>
        <div className={styles.remark}>
          <WarningOutlined />
          备注：当有工作经历及证明外的模块不符合要求时，仅允许选择驳回修改
        </div>
      </Modal>
    </SchemaForm>
  )
}

export default SubmitAuditResultModal
