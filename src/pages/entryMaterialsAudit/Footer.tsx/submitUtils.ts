import { omit, pick } from 'lodash-es'
import {
  EntryMaterialSubmit,
  EntryMaterialAuditGroups,
  EntryMaterialSubmits,
  EducationExperienceItem,
} from '@/types/entryMaterialsAudit'
import { validateFields } from '@/pages/entryMaterialsAudit/config/config'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import dictService from '@/pages/entryMaterialsAudit/dictService'

type AuditResultFields = Record<string, any>
type AuditStatus = {
  pass: boolean
  notPass: boolean
  reject: boolean
}

const EliminateFields: string[] = [
  'resignationCertificate_itemAuditResult', // 工作经历模块
]

const AuditResultStatus = {
  NotPass: 0,
  Compliance: 1,
  NotSelected: undefined,
  isNull: null,
}

/**
 * 处理审核结果对应的操作状态
 * @filterAuditResultFields 过滤出fieldsValues中审核符合与不符合结果的值的字段跟值
 * @return pass: 审核通过, notPass: 审核不通过, reject: 驳回修改
 */
export const handleAuditResultActionStatus = (
  filterAuditResultFields: AuditResultFields,
): AuditStatus => {
  // 过滤出filterAuditResultFields中含'_itemAuditResult'的字段跟值
  const filterAuditResultFieldsKeys: AuditResultFields = Object.keys(
    filterAuditResultFields,
  ).reduce((acc: any, cur: string) => {
    if (cur.includes('_itemAuditResult')) {
      acc[cur] = filterAuditResultFields[cur]
    }
    return acc
  }, {})

  // 如果选项目中还有未选中的则全部禁用
  if (
    Object.values(filterAuditResultFieldsKeys).includes(AuditResultStatus.NotSelected) ||
    Object.values(filterAuditResultFieldsKeys).includes(AuditResultStatus.isNull)
  ) {
    return {
      pass: false,
      notPass: false,
      reject: false,
    }
  }
  // 当所有模块的审核情况均为”符合“时，可选项为“审核通过”、“审核不通过”，置灰禁用“驳回修改”；
  const isAllCompliance: boolean = Object.values(filterAuditResultFieldsKeys).every(
    (item) => item === AuditResultStatus.Compliance,
  )
  if (isAllCompliance) {
    return {
      pass: true,
      notPass: true,
      reject: false,
    }
  }
  // 当只有教育经历信息、健康相关信息、离职证明模块存在审核情况为“不符合”，其他模块均为“符合”时，所有选项均点亮可选；
  // v2.1.0修改为只有工作经历模块
  const isOnlyEducationExperienceAuditNotPass: boolean =
    EliminateFields.some(
      (field) => filterAuditResultFieldsKeys[field] === AuditResultStatus.NotPass,
    ) &&
    Object.values(omit(filterAuditResultFieldsKeys, EliminateFields)).every(
      (item) => item === AuditResultStatus.Compliance,
    )
  if (isOnlyEducationExperienceAuditNotPass) {
    return {
      pass: true,
      notPass: true,
      reject: true,
    }
  }
  // 当有教育经历信息、健康相关信息、离职证明以外的模块存在审核情况为“不符合”时，可选项为“驳回修改”，置灰禁用“审核通过”、“审核不通过”。
  // v2.1.0修改为只有工作经历模块
  const isOtherAuditNotPass: boolean = Object.values(
    omit(filterAuditResultFieldsKeys, EliminateFields),
  ).some((item) => item === AuditResultStatus.NotPass)
  if (isOtherAuditNotPass) {
    return {
      pass: false,
      notPass: false,
      reject: true,
    }
  }
  return {
    pass: false,
    notPass: false,
    reject: false,
  }
}

/**
 * 提交审核表单数据处理组装处理
 * 将fieldsValues中的值转换成EntryMaterialSubmit类型格式
 */
export const handleAuditResultSubmitData = async (finalFieldsValues: AuditResultFields) => {
  const res = Object.entries(finalFieldsValues).reduce((acc, [key, value]) => {
    const [prefix, suffix] = key.split('_')
    if (!suffix) {
      return acc
    }
    if (!acc[prefix]) {
      acc[prefix] = {}
    }
    if (suffix === 'itemAuditResult') {
      acc[prefix].itemAuditResult = value
    } else if (suffix === 'noPassReason') {
      acc[prefix].noPassReason = value
    } else if (suffix === 'noPassReasonCodes') {
      acc[prefix].noPassReasonCodes = value
    }
    return acc
  }, {})

  const entryMaterialAudit = {
    ...res,
    auditResult: finalFieldsValues.auditResult,
    rejectModifyTemplateId: finalFieldsValues.rejectModifyTemplateId,
    timeRequireComplete: finalFieldsValues.timeRequireComplete,
  }
  let bankCardInfo = pick(finalFieldsValues, validateFields.bankCardInfo)
  const $bankPlaceCode = bankCardInfo.bankPlaceCode
  bankCardInfo.bankPlaceProvinceCode = $bankPlaceCode?.[0] || ''
  bankCardInfo.bankPlaceCityCode = $bankPlaceCode?.[1] || ''
  bankCardInfo = omit(bankCardInfo, ['bankPlaceCode'])

  const educationExperiences = pick(finalFieldsValues, [
    ...validateFields.educationExperience,
  ]).educationExperiences?.map((item: EducationExperienceItem) => {
    return {
      ...item,
      timeEnrollment: item.timeEnrollment?.valueOf?.() || null,
      timeExpectedGraduate: item.timeExpectedGraduate?.valueOf?.() || null,
      timeGraduate: item.timeGraduate?.valueOf?.() || null,
    }
  })

  // 工作经历只有一段，但是接口接收数组格式
  const workExperiencesObject = pick(finalFieldsValues, [...validateFields.workExperiences])
  const hasWorkExperiences = Object.keys(workExperiencesObject).length !== 0
  if (hasWorkExperiences) {
    workExperiencesObject.timeBegin = workExperiencesObject?.timeBegin?.valueOf?.() || null
    workExperiencesObject.timeEnd = workExperiencesObject?.timeEnd?.valueOf?.() || null
    workExperiencesObject.timeEndWork = workExperiencesObject?.timeEndWork?.valueOf?.() || null
  }
  const workExperiences = hasWorkExperiences ? [workExperiencesObject] : []

  const entryInfo = pick(finalFieldsValues, [
    ...validateFields.healthInfo,
    ...validateFields.otherInfo,
  ])
  entryInfo.timeHealthEnd = entryInfo.timeHealthEnd?.valueOf() || null
  entryInfo.timeDisabilityCertificate = entryInfo.timeDisabilityCertificate?.valueOf?.() || null
  entryInfo.timeCertificateProvide = entryInfo.timeCertificateProvide?.valueOf?.() || null
  entryInfo.timeValidPeriodEnd = entryInfo.timeValidPeriodEnd?.valueOf?.() || null

  let identityInfo: any = pick(finalFieldsValues, [
    ...validateFields.personBaseInfo,
    ...validateFields.identityInfo,
  ])
  identityInfo.idCardIssuingAuthority = identityInfo.idCardIssuingAuthority || ''

  /**
   * 特殊处理 currentAddressObj
   * identityInfo.address如果是个字符串取为最初始的赋值
   * identityInfo.address如果是数组就取正常值
   */
  let currentAddressObj = {} as any
  const primevalIdentityInfo = informationBlockService.formData.identityInfo
  if (
    typeof identityInfo?.address?.address === 'string' &&
    identityInfo.address.address === primevalIdentityInfo.currentAddressName
  ) {
    currentAddressObj = {
      currentAddressName: primevalIdentityInfo.currentAddressName,
      currentProvinceCode: primevalIdentityInfo.currentProvinceCode,
      currentCityCode: primevalIdentityInfo.currentCityCode,
      currentAreaCode: primevalIdentityInfo.currentAreaCode,
      currentStreetCode: primevalIdentityInfo.currentStreetCode,
    }
  } else {
    currentAddressObj = {
      currentProvinceCode: identityInfo.address?.address?.[0],
      currentCityCode: identityInfo.address?.address?.[1],
      currentAreaCode: identityInfo.address?.address?.[2],
      currentStreetCode: identityInfo.address?.address?.[3],
    }
    const { getAsyncDict } = dictService
    const datas = await getAsyncDict('REGION_CODE', 'GLOBAL', {
      codes: [
        Number(currentAddressObj.currentProvinceCode),
        Number(currentAddressObj.currentCityCode),
        Number(currentAddressObj.currentAreaCode),
        Number(currentAddressObj.currentStreetCode),
      ],
    })
    const currentAddressName = datas?.map((item) => item.name).join('/')
    currentAddressObj.currentAddressName = currentAddressName
  }

  identityInfo = {
    ...identityInfo,
    currentAddress: identityInfo.address?.detail,
    ...currentAddressObj,
    timeIdCardStart: identityInfo.timeIdCard?.[0]?.valueOf() || null,
    timeIdCardEnd: identityInfo.timeIdCard?.[1]?.valueOf() || null,
  }
  identityInfo = omit(identityInfo, ['address', 'timeIdCard'])

  const entryMaterialSubmit = {
    educationExperiences,
    workExperiences,
    entryInfo,
    identityInfo,
    bankCardInfo,
  }

  return {
    entryMaterialAudit,
    entryMaterialSubmit,
  }
}

/**
 * 判断提交审核表单不符合原因的code集合是否存在不在配置项中的code集合里
 */
export const isExistNotInConfig = (
  fieldsValue: Record<string, any>,
  configNoPassReasonCodes: string[],
): boolean => {
  const fieldsValueNoPassReasonItems = Object.keys(fieldsValue).reduce((acc: any, cur: string) => {
    if (cur.includes('_noPassReasonCodes')) {
      acc[cur] = fieldsValue[cur]
    }
    return acc
  }, {})
  const fieldsValueNoPassReasonCodes = Object.values(fieldsValueNoPassReasonItems)
    .map((item: any) => {
      return item?.map((item2: any[]) => item2[item2.length - 1])
    })
    .reduce((acc: any, cur: any) => {
      return acc?.concat(cur)
    }, [])

  // 判断 fieldsValueNoPassReasonCodes中的code是否有不在configNoPassReasonCodes中的
  const isHasNoExist = fieldsValueNoPassReasonCodes.some(
    (item) => !configNoPassReasonCodes.includes(item),
  )
  return isHasNoExist
}
