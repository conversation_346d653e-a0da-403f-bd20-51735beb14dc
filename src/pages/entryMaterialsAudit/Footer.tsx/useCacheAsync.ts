import { useRef } from 'react'

export default function useCacheAsync({ cacheKey = '' }) {
  const promiseListRef = useRef<{ key: string; value: Promise<any> }[]>([])

  // 检查是否有缓存
  function checkPromise(params: any): false | Promise<any> {
    const passParams = params
    passParams._cacheKey = cacheKey
    const key = JSON.stringify(passParams)
    const res = promiseListRef.current.filter((item) => item.key === key)
    if (res.length > 0) {
      return res[0].value
    }
    return false
  }

  // 缓存请求
  function cacheAsync(fetch: (params: any) => Promise<any>, params: any): Promise<any> {
    const cachePromise = checkPromise(params)
    if (cachePromise) {
      return cachePromise
    }
    const promise = fetch(params)
    promiseListRef.current.push({
      key: JSON.stringify(params),
      value: promise,
    })
    return promise
  }

  return {
    cacheAsync,
  }
}
