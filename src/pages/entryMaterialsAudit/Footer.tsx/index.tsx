/**
 * @description 底部操作栏
 */
import React, { useEffect, useState, useRef, useContext } from 'react'
import { Button, Space, Tooltip, message } from 'antd'
import { useToggle } from 'ahooks'
import classNames from 'classnames'
import { useNavigate, useLocation } from 'react-router-dom'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { Permission, getPermission } from '@galaxy/rbac'
import { createAction } from '@amazebird/antd-schema-table'
import { cloneDeep } from 'lodash-es'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import ReModal from '@/components/ReModal'
import { useInitService } from '@/pages/entryMaterialsAudit/initContext'
import reviewService from '@/services/preEntry/review'
import dmsService from '@/services/dms'
import { getUrlParam } from '@/utils/utils'
import { NEXT_MATERIALS, PREVIOUS_MATERIALS, SUBMIT_RESULT } from '@/constants/dms/eventName'
import { reportBizError } from '@galaxy/utils'
import { useStore } from '@/stores'
import { InProcessReviewBtn } from '@/constants/rbac/preEntry'
import { IsNeedReview, ProcessProgress } from '@/services/preEntry/list'
import Review, {
  ActionType as ReviewActionType,
} from '@/pages/preEntry/components/table/tableConfig/column/Review'
import { validateFields } from '../config/config'
import SubmitAuditResultModal from './SubmitAuditResultModal'
import styles from './style.module.less'
import subEvent from '../utils/subEvent'

interface FooterProps {
  form: any
  formData: any
  isFieldsTouched: boolean
}

interface UrlParams {
  id: string
  listIds: string[]
}

const emptyText = '--'

function parseUrlParams(search: string): UrlParams {
  const params = new URLSearchParams(search)
  return {
    id: params.get('id') || '',
    listIds: (params.get('listIds') || '').split(',').filter(Boolean),
  }
}

function buildNewUrl(params: UrlParams, newId: string): string {
  return `?id=${newId}&listIds=${params.listIds.join(',')}`
}

const Footer: React.FC<FooterProps> = (props) => {
  const { form, isFieldsTouched, formData } = props
  const reviewRef: any = useRef()
  const { setErrorFields } = useStore()
  const [state, { setLeft, setRight }] = useToggle<boolean>(false)
  const { hasCanEditInfoAuth } = businessAuthor
  const service = useInitService()
  const entryMaterialId = getUrlParam('id', window.location.href)
  const [auditors, setAuditors] = useState<{ userName: string; userNum: string }[]>([])

  const navigate = useNavigate()
  const location = useLocation()
  const params = parseUrlParams(location.search)
  const currentIndex = params.listIds.indexOf(params.id)

  const prevId = currentIndex > 0 ? params.listIds[currentIndex - 1] : null
  const nextId = currentIndex < params.listIds.length - 1 ? params.listIds[currentIndex + 1] : null

  const customAction = {
    refresh: () => service.upLoad(),
  }

  const handlePrevClick = (): void => {
    if (prevId) {
      const newUrl = buildNewUrl(params, prevId)
      navigate(newUrl, { replace: true })
      service.upLoad()
    }
  }

  const handleNextClick = (): void => {
    if (nextId) {
      const newUrl = buildNewUrl(params, nextId)
      navigate(newUrl, { replace: true })
      service.upLoad()
    }
  }

  /**
   * @description 获取正在审核的人员列表
   */
  const getAuditingPersonList = async (): Promise<void> => {
    const res = await reviewService.getAuditingPersonList(Number(entryMaterialId))
    setAuditors(res)
  }

  useEffect(() => {
    if (hasCanEditInfoAuth()) {
      getAuditingPersonList()
    }
  }, [])

  // 轮询定时器
  useEffect(() => {
    let timer
    const startPolling = () => {
      if (hasCanEditInfoAuth()) {
        timer = setInterval(() => {
          getAuditingPersonList()
        }, 5000)
      }
    }
    const stopPolling = () => {
      clearInterval(timer)
    }
    if (state) {
      stopPolling()
    } else {
      startPolling()
    }
    return () => {
      stopPolling()
    }
  }, [state])

  // 底部审核人信息处理
  const handleAuditors = (): React.ReactNode => {
    if (!auditors.length) {
      return null
    }
    return (
      <div
        className={classNames(
          styles.auditors_item,
          auditors.length > 1 ? styles.flashing_text : null,
        )}
      >
        <span>正在审核：</span>
        {auditors.map((item, index) => {
          const comma = index !== auditors.length - 1 ? '、' : ''
          return (
            <span key={item.userName} className={styles.name_item}>
              {item.userName}
              {item.userNum ? `(${item.userNum})` : ''}
              {comma}
            </span>
          )
        })}
      </div>
    )
  }

  // 根据field找到key
  const findKeyByField = (field: string): string | undefined => {
    // eslint-disable-next-line no-restricted-syntax
    for (const [key, fields] of Object.entries(validateFields)) {
      if (fields.includes(field)) {
        return key
      }
    }
    return undefined
  }

  /**
   * 处理错误字段
   * @param errorFields 错误字段
   */
  const handleErrorFields = (errorFields: any[]): void => {
    const newErrorFields = errorFields.map((item) => {
      const key = findKeyByField(item.name[0])
      return key || item.name[0].split('_')[0]
    })
    const newErrorFieldsSet = new Set(newErrorFields)
    const newErrorFieldsArr = Array.from(newErrorFieldsSet)

    const $filterErrorFields = errorFields?.filter((item) => {
      const $errorKey = item?.name?.[0] || ''
      if (
        $errorKey.indexOf('_itemAuditResult') > -1 ||
        $errorKey.indexOf('_noPassReasonCodes') > -1 ||
        $errorKey.indexOf('_noPassReason') > -1
      ) {
        return false
      }
      return true
    })
    const $filterNewErrorFields = $filterErrorFields.map((item) => {
      const key = findKeyByField(item.name[0])
      return key || item.name[0].split('_')[0]
    })
    // 通知错误模块打开编辑态
    newErrorFieldsArr.forEach((key) => {
      subEvent.pub(key, (callback) => {
        if (Array.from?.(new Set($filterNewErrorFields)).includes(key)) {
          callback?.()
        }
        // 等待表单改成编辑态后再处理
        setTimeout(() => {
          form.scrollToField(errorFields[0]?.name, { behavior: 'smooth', block: 'center' })
        }, 500)
      })
    })
    setErrorFields(newErrorFieldsArr)
  }

  const getAuditResultText = (auditResult: 0 | 1 | 2 | 3) => {
    if (auditResult === 1) {
      return '审核通过'
    }
    if (auditResult === 2) {
      return '审核不通过'
    }
    if (auditResult === 3) {
      return '驳回修改'
    }
    return emptyText
  }
  return (
    <>
      <div className={styles.footer}>
        <div className={styles.auditors}>{hasCanEditInfoAuth() ? handleAuditors() : ''}</div>
        <Space>
          <Tooltip title={!prevId ? '已经是第一份了' : null}>
            <Button
              type="link"
              onClick={() => {
                dmsService.trace(PREVIOUS_MATERIALS)
                handlePrevClick()
              }}
              disabled={!prevId}
            >
              <LeftOutlined />
              上一份
            </Button>
          </Tooltip>
          <Tooltip title={!nextId ? '已经是最后一份了' : null}>
            <Button
              type="link"
              onClick={() => {
                dmsService.trace(NEXT_MATERIALS)
                handleNextClick()
              }}
              disabled={!nextId}
            >
              下一份
              <RightOutlined />
            </Button>
          </Tooltip>
        </Space>
        <Space>
          <Button onClick={() => navigate(-1)}>取消</Button>
          {hasCanEditInfoAuth() && (
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                dmsService.trace(SUBMIT_RESULT)
                form
                  .validateFields()
                  .then((values: any) => {
                    setRight()
                    setErrorFields([])
                  })
                  .catch((err: any) => {
                    handleErrorFields(err.errorFields)
                    reportBizError('材料审核：提交时字段校验错误', {
                      errorMsg: JSON.stringify(err.errorFields),
                    })
                  })
              }}
            >
              提交
            </Button>
          )}
          {getPermission(InProcessReviewBtn) &&
          formData?.otherInfo?.isNeedReview === IsNeedReview.YES &&
          [ProcessProgress.ToBeReviewed].includes(formData?.processInfo?.processProgress) ? (
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                reviewRef?.current?.open?.()
              }}
              style={{
                marginRight: '6px', // dropdown有bug，无法从disable元素直接到dropdown，会导致hover无效
              }}
            >
              复核
            </Button>
          ) : null}
        </Space>
      </div>
      {state && (
        <SubmitAuditResultModal
          visible={state}
          onOk={setLeft}
          onCancel={setLeft}
          fieldsValue={form.getFieldsValue()}
          form={form}
          hasNext={!!nextId}
          handleNextClick={handleNextClick}
        />
      )}
      <Review
        action={customAction as ReturnType<typeof createAction>}
        id={formData?.entryInfo?.id}
        content={`候选人${formData?.identityInfo.name}的初审结果为：${getAuditResultText(
          formData?.otherInfo?.auditResult,
        )}`}
        ref={(node) => {
          if (node) {
            reviewRef.current = node
          }
        }}
      />
    </>
  )
}

export default Footer
