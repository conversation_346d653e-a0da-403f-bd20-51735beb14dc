.footer{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  position: fixed;
  right: 0px;
  bottom: 0px;
  width: 100%;
  background: #fff;
  box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 2px 2px;
  padding: 0px 24px 0px  232px;


  .auditors{
    min-width: 0;
    .auditors_item{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .name_item{
    padding-right: 8px;
  }

  @keyframes blink {
    0%, 100% { color: red; } /* 红色 */
    50% { color: initial; } /* 还原默认颜色（不变） */
  }

  .flashing_text {
    animation: blink 3s 3; /* 闪烁动画，3次，每次3秒 */
    color: red;
  }

}


.modal{
  :global{
    .ant-modal-body{
      max-height: 800px;
      overflow-y: auto;
      overflow-x: auto;
    }
  }
  .modal_item_title{
    margin-bottom: 24px;
    .title{
      font-weight: bold;
    }

    .audit_result{
      display: flex;
      align-items: center;
    }
    .audit_result_label{
      min-width: 100px;
      text-align: right;
      height: 56px;
      line-height: 32px;
    }
    .audit_result_wrapper{
      flex: 1 1 0;
      .row{
        flex-flow: nowrap;
        // row下的第二个div标签
        div:nth-child(2){
          max-width: 200px;
        }
      }
    }
  }
  .remark{
    color: #FAAD14;
  }
}

