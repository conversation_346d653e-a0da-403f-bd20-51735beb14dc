.top_base_info {
  display: flex;
  background: #fff;
  padding: 0px 24px 8px 24px;
  flex-direction: column;
  .alter_message{
    display: flex;
    flex-direction: nowrap;
    .alter_context{
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .alter_option{
      min-width:30px;
      text-align: right;
      color: #1abb9c;
      cursor: pointer;
    }
  }
}

.head{
  display: flex;
  align-items: center;
  & >.name{
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
    padding-right: 16px;
  }
  .process_text{
    font-size: 12px;
    font-weight: 400;
  }
  .yellow{
    color:#FAAD14,
  }
  .green{
    color: #52C41A;
  }
  .blue{
    color:#1890FF,
  }
  .red{
    color:#FF4D4F
  }
}


