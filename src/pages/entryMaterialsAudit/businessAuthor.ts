import { VIEW_MODE, PROCESS_PROGRESS_STATUS, EHR_PROGRESS_STATUS } from '@/constants/review'
import { ProcessProgressType, EhrProgressType } from '@/types/entryMaterialsAudit'
import { getPermission } from '@galaxy/rbac'
import { MaterialsAuditSubmitBtn, MaterialsAuditModifyResultBtn } from '@/constants/rbac/preEntry'

class BusinessAuthor {
  /**
   * 当前审核的流程
   */
  private process: ProcessProgressType = 0

  // 当前ehr流转状态
  private ehrProgress: EhrProgressType = 0

  // 是否需要工牌号
  private isNeedBadgeCode: string = '0'

  /**
   * 设置当前审核的流程
   * @param process
   */
  setProcess = (process): void => {
    this.process = process as ProcessProgressType
  }

  getProcess = (): ProcessProgressType => {
    return this.process
  }

  setEhrProgress = (ehrProgress): void => {
    this.ehrProgress = ehrProgress as EhrProgressType
  }

  setIsNeedBadgeCode = (isNeedBadgeCode): void => {
    this.isNeedBadgeCode = isNeedBadgeCode
  }

  /**
   * 是否有审核提交按钮的UC权限
   * 这个方法是审核模块判断在有数据权限下是否有初审权限的唯一路口
   * @returns boolean
   */
  hasSubmitUcAuth = (): boolean => getPermission(MaterialsAuditSubmitBtn)

  /**
   * 是否可以点击修改审核结果按钮
   * 若当前处理进度=审核不通过/审核通过/特批通过 且 ehr流转状态=下发失败、待下发或待代缴流程归档 时
   * @returns boolean
   */
  isCanClickEditAuditResult = (): boolean => {
    return (
      [
        PROCESS_PROGRESS_STATUS.AUDIT_PASSED,
        PROCESS_PROGRESS_STATUS.AUDIT_FAILED,
        PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED,
      ].includes(this.process) &&
      [
        EHR_PROGRESS_STATUS.TO_BE_ISSUED,
        // EHR_PROGRESS_STATUS.AUTO_RETRY,
        EHR_PROGRESS_STATUS.ISSUE_FAILED,
        EHR_PROGRESS_STATUS.ToBeArchived,
      ].includes(this.ehrProgress)
    )
  }

  /**
   * 是否隐藏修改审核结果按钮
   * 若当前处理进度=审核不通过/审核通过/特批通过 且 ehr流转状态=空时，按钮隐藏；
   * 若当前处理进度=结束招聘，按钮隐藏；【2023.05.24 新增】
   * @returns boolean
   */
  isHideEditAuditResult = (): boolean => {
    if (getPermission(MaterialsAuditModifyResultBtn)) {
      return (
        ([
          PROCESS_PROGRESS_STATUS.AUDIT_PASSED,
          PROCESS_PROGRESS_STATUS.AUDIT_FAILED,
          PROCESS_PROGRESS_STATUS.SPECIAL_APPROVAL_PASSED,
        ].includes(this.process) &&
          [EHR_PROGRESS_STATUS.NONE].includes(this.ehrProgress)) ||
        this.process === PROCESS_PROGRESS_STATUS.RECRUITMENT_ENDED
      )
    }
    return true
  }

  /**
   * 是否有编辑信息块权限
   * @returns boolean
   */
  hasCanEditInfoAuth = (): boolean => {
    if (this.hasSubmitUcAuth()) {
      if (
        this.process === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
        this.process === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
      ) {
        return true
      }
    }
    return false
  }

  /**
   * 当前审核模块的展示态组
   * 1. 是否有初审权限
   * 2. 对应的处理状态判断
   * @returns object {nav: VIEW_MODE, form: VIEW_MODE, modal: VIEW_MODE}
   */
  getAuditInitialViewStatusGroup = (): { nav: VIEW_MODE; form: VIEW_MODE; modal: VIEW_MODE } => {
    // 有初审权限
    if (this.hasSubmitUcAuth()) {
      if (
        this.process === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
        this.process === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
      ) {
        return {
          nav: VIEW_MODE.DETAIL,
          form: VIEW_MODE.EDIT,
          modal: VIEW_MODE.EDIT,
        }
      }
      return {
        nav: VIEW_MODE.DETAIL,
        form: VIEW_MODE.DETAIL,
        modal: VIEW_MODE.NONE,
      }
    }
    // 无初审权限
    if (this.process === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED) {
      return {
        nav: VIEW_MODE.NONE,
        form: VIEW_MODE.NONE,
        modal: VIEW_MODE.NONE,
      }
    }
    return {
      nav: VIEW_MODE.DETAIL,
      form: VIEW_MODE.DETAIL,
      modal: VIEW_MODE.NONE,
    }
  }

  // 是否展示工牌照片
  hasNeedBadge = (): boolean => {
    return !!Number(this.isNeedBadgeCode)
  }
}

export default new BusinessAuthor()
