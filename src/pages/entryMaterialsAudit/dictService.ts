import { service, getDict } from '@galaxy/dict'
import { listToTree, handleTreeOptions } from '@/utils/utils'
import { getAsyncDict } from '@/apis/review'

type OptionType = {
  label: string
  value: string
  children?: OptionType[]
  isLeaf?: boolean
  loadId?: number
}

class DictService {
  /**
   * 字典项
   */
  private dicts = {} as Record<string, any>

  /**
   * 请求字典项
   * @returns void
   */
  fetchDict = async (): Promise<void> => {
    if (Object.keys(this.dicts).length) {
      return
    }
    // EHR部分字典
    const ehrDicts = await service.getDictionaryItem(
      [
        'BM_35', // 用工形式
        'BM_YGLX', // 人员类别
        'BM_AT', // 政治面貌
        'BM_BG', // 婚育状况
        'BM_66', // 国籍
        // 'BM_AB', // 籍贯-省市区
        'BM_HP', // 户籍类型
        'BM_ZJLX', // 身份证类型
        'BM_AX', // 性别
        'BM_AE', // 民族
        'BM_Banktype', // 银行卡类型
        // 'BM_KHD', // 开户地省市
        'BM_XL', // 学历
        'BM_XW', // 学位
        'BM_studymode', // 学习形式
        'BM_YN', // 是否
        'BM_GX', // 与本人关系
        'BM_CZZM', // 残疾证件类型
        'BM_CJRZDJ', // 残疾人证等级
        'BM_CZDJ', // 伤残等级
        'BM_CJLB', // 残疾类别
        'BM_CJZYXQ', // 证件有效期
        'BM_CJZBB', // 残疾证版本
        'BM_CJXZ', // 残疾性质
        'BM_HTZT', // 合同主体
        'BM_Rank', // 主序列
      ],
      'EHR',
    )

    // ENTRY入职字典
    const entryDicts = await service.getDictionaryItem(
      [
        'OMS_INFORMATION_FAILED_REASON', // 复合驳回原因列表
        'OMS_OPEN_ADDRESS', // 开户地
      ],
      'ENTRY',
    )

    // GLOBAL字典
    let addressDicts = [] as any[]
    let nativePlaceDicts = [] as any[]
    try {
      const queryAddressDicts = await this.getAsyncDict('REGION_CODE', 'GLOBAL', {
        parentId: 0,
      })
      addressDicts = queryAddressDicts

      const queryNativePlaceDicts = await this.getAsyncDict('REGION_CODE', 'GLOBAL', {
        itemLevelLt: 4,
      })
      nativePlaceDicts = queryNativePlaceDicts
    } catch (e) {
      // console.log(e)
    }

    this.dicts = {
      ...entryDicts,
      ...ehrDicts,
      ...{ REGION_CODE: addressDicts },
      ...{ OMS_NATIVE_PLACE: nativePlaceDicts },
    }
  }

  /**
   *  获取字典项
   * @param key 字典项的key
   * @returns OptionType[]
   */
  getDictOptions = (key: string): OptionType[] => {
    const treeDict = ['OMS_NATIVE_PLACE', 'OMS_OPEN_ADDRESS', 'OMS_INFORMATION_FAILED_REASON']
    // 需要处理成树形结构的字典项
    if (treeDict.includes(key)) {
      const res = listToTree(this.dicts[key]?.filter((item) => !!item.isEnabled))
      return handleTreeOptions(res)
    }
    return this.dicts[key]
      ?.filter((item) => !!item.isEnabled)
      ?.map((item) => ({
        label: item.name,
        value: item.code,
        isLeaf: item.isLeaf || false,
        loadId: item.id,
      }))
  }

  /**
   *  获取字典项
   * @param key 字典项的key
   * @param code 字典 code
   * @returns string
   */
  getDictName = (key: string, code: string): string => {
    const treeDict = ['OMS_NATIVE_PLACE', 'OMS_OPEN_ADDRESS', 'OMS_INFORMATION_FAILED_REASON']
    // 需要处理成树形结构的字典项
    if (treeDict.includes(key)) {
      const res = listToTree(this.dicts[key]?.filter((item) => !!item.isEnabled))
      return handleTreeOptions(res)
    }

    const currentDict = this.dicts[key]
    const currentName = (currentDict as any[]).find((v) => v.code === code)?.name || ''

    return currentName
  }

  /**
   * 获取字典项返回Map格式
   * @param key 字典项的key
   * @param isRenderParent 是否渲染父级
   * @returns Record<string, string>
   */
  getDictMap = (key: string, { isRenderParent = false } = {}): Record<string, string> => {
    const map = {} as Record<string, string>
    this.dicts[key]?.forEach((item) => {
      if (isRenderParent) {
        const idToDictMap: Record<number, any> = {}
        this.dicts[key]?.forEach((item1) => {
          idToDictMap[item1?.id] = item1
        })
        let parentName = item.name
        let parentId = item.parentId
        while (parentId !== 0) {
          const parent = idToDictMap[parentId]
          parentName = `${parent?.name}/${parentName}`
          parentId = parent?.parentId || 0
        }
        map[item.code] = `${parentName}`
      } else {
        map[item.code] = item.name
      }
    })
    return map
  }

  /**
   * 异步加载字典项
   */
  getAsyncDict = async (
    typeCodes,
    scope,
    {
      parentId,
      codes,
      itemLevelLt,
    }: { parentId?: number; codes?: string[] | number[]; itemLevelLt?: number },
  ): Promise<Record<string, any>[]> => {
    const { data } = await getAsyncDict(typeCodes, scope, {
      parentId,
      codes,
      itemLevelLt,
    })
    return data.filter((item) => !!item.isEnabled)
  }
}

export default new DictService()
