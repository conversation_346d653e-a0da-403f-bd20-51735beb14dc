/**
 * @name auditConfig 审核表单的配置表
 */
import React, { useRef } from 'react'
import { Radio, Cascader, Input } from 'antd'
import { useMount, useDeepCompareEffect, useUpdate } from 'ahooks'
import type { SchemaType } from '@amazebird/schema-form'
import { Observer } from '@amazebird/antd-schema-form'
import { treeToList } from '@/utils/utils'
import { FORM_MODE, WATCH_MODES } from '@/constants/review'
import { useStore } from '@/stores'
import reviewService from '@/services/preEntry/review'
import {
  ObserverOptions,
  getEducationExperienceValidateFields,
  renderDictFirstCode,
} from './config'

enum MODE {
  EDIT = 'edit',
  DETAIL = 'detail',
}

/**
 * MyRadioGroup组件
 * 将Radio.Group组件二次封装下，做成受控组件方便做数据更改
 */
const MyRadioGroup = (props) => {
  const { setAuditStatus, auditStatus } = useStore()
  const { value, onChange, isSetChange, ...rest } = props
  return (
    <Radio.Group
      {...rest}
      value={value}
      onChange={(e) => {
        // TODO 待优化点 这里处理的逻辑只会改变itemAuditResult
        // 只是做个数据的更新，不做其余的操作
        if (isSetChange) {
          setAuditStatus(props.item, {
            itemAuditResult: e.target.value,
            noPassReason: auditStatus[props.item].noPassReason,
            noPassReasonCodes: auditStatus[props.item].noPassReasonCodes,
          })
        }
        onChange(e.target.value)
      }}
    />
  )
}

interface Option {
  value: string
  label: string
  children?: Option[]
}
/**
 * NoPassReasonCascader 组件
 * 针对不符合原因的级联选择器做二次封装包含编辑和详情两种模式
 */
const NoPassReasonCascader = (props) => {
  const { options, max, matchCode, value, onChange, modeType, ...rest } = props
  const [cOptions, setOptions] = React.useState([])
  const labelRef = useRef<string[]>([])
  const update = useUpdate()
  useMount(() => {
    const filterOptions = options?.filter((item) => {
      return item.value === matchCode
    })
    setOptions(filterOptions?.[0]?.children || [])
  })
  const handleChange = (val, selectedOptions): void => {
    if (val.length <= max) {
      onChange(val)
    }
  }

  /**
   * 映射详情态的label
   */
  const getLabel = (arr: Option[], val: any[]): void => {
    const arrList = treeToList(arr)
    // value是个二维数组遍历value取每个数组的最后一项做映射关系
    val.forEach((item) => {
      const findItem = arrList.find((i) => i.value === item[item.length - 1])
      if (findItem) {
        labelRef.current.push(findItem.label)
      }
    })
  }

  useDeepCompareEffect(() => {
    if (modeType === MODE.DETAIL) {
      getLabel(cOptions, value || [])
      update()
    }
  }, [cOptions, value])
  if (modeType === MODE.EDIT) {
    return (
      <Cascader
        {...rest}
        options={cOptions}
        value={props.multiple ? value || [] : value}
        onChange={handleChange}
      />
    )
  }
  if (modeType === MODE.DETAIL) {
    return <div>{labelRef.current?.join('、') || '--'}</div>
  }
  return <div>--</div>
}

// 是否符合要求
export const itemAuditResultConfig = (item, labelNone) => {
  // 教育经历字段特殊处理重新触发校验
  const commonConfig = {
    label: labelNone ? '' : '是否符合要求',
    component: Observer({
      watch: `#${WATCH_MODES.AUDIT_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <MyRadioGroup item={item} isSetChange={!labelNone} {...props} />
          default:
            return 'Select.Text'
        }
      },
    }),
    options: [
      { label: '符合', value: 1 },
      { label: '不符合', value: 0 },
    ],
  }
  if (item === 'educationExperience') {
    return {
      ...commonConfig,
      props: Observer({
        watch: 'educationExperience_itemAuditResult',
        action: (AuditResult, formParams) => {
          const $draftState: any = reviewService.handleStateAndDraftData()
          const educationExperiencesMode = $draftState?.education_experience_mode
          if (
            (educationExperiencesMode && educationExperiencesMode !== FORM_MODE.MODIFY) ||
            AuditResult === 0
          ) {
            setTimeout(() => {
              formParams.form.validateFields(
                getEducationExperienceValidateFields(
                  formParams.form.getFieldValue('educationExperiences')?.length,
                ),
              )
            }, 500)
          }
        },
      }),
    }
  }
  return commonConfig
}

// 不符合原因
export const noPassReasonCodesConfig = (watchItem, labelNone, key) => {
  return {
    label: labelNone ? '' : '不符合原因',
    component: Observer({
      watch: `#${WATCH_MODES.AUDIT_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => (
              <NoPassReasonCascader
                matchCode={renderDictFirstCode[key]}
                {...props}
                max={10}
                showCheckedStrategy={Cascader.SHOW_CHILD}
                modeType={MODE.EDIT}
              />
            )
          default:
            return (props) => (
              <NoPassReasonCascader
                matchCode={renderDictFirstCode[key]}
                {...props}
                modeType={MODE.DETAIL}
              />
            )
        }
      },
    }),
    options: ObserverOptions('OMS_INFORMATION_FAILED_REASON'),
    placeholder: '请选择不符合原因',
    visible: Observer({
      watch: [watchItem],
      action: (bool): boolean => bool[0] === 0,
    }),
    required: Observer({
      watch: [watchItem],
      action: (isRequire): boolean => isRequire[0] === 0,
    }),
    props: {
      multiple: true,
    },
  }
}

// 补充说明
export const noPassReasonConfig = (watchItem, labelNone) => {
  return {
    label: labelNone ? '' : '补充说明',
    component: Observer({
      watch: `#${WATCH_MODES.AUDIT_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <Input.TextArea {...props} maxLength={100} />
          default:
            return 'Input.Text'
        }
      },
    }),
    props: {
      autoSize: {
        minRows: 3,
      },
    },
    placeholder: '可填写补充说明，控制在100字以内（可选）',
    visible: Observer({
      watch: [watchItem],
      action: (bool): boolean => bool[0] === 0,
    }),
  }
}

const createAuditSchema = (item, labelNone) => {
  return {
    [`${item}_itemAuditResult`]: itemAuditResultConfig(item, labelNone),
    [`${item}_noPassReasonCodes`]: noPassReasonCodesConfig(
      `${item}_itemAuditResult`,
      labelNone,
      item,
    ) as any,
    [`${item}_noPassReason`]: noPassReasonConfig(`${item}_itemAuditResult`, labelNone),
  }
}

export const personPhotoAuditSchema = (labelNone: boolean = false): SchemaType => {
  return createAuditSchema('personPhoto', labelNone)
}

export const workCardPhotoAuditSchema = (labelNone = false) => {
  return createAuditSchema('workCardPhoto', labelNone)
}

export const personBaseInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('personBaseInfo', labelNone)
}

export const identityInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('identityInfo', labelNone)
}

export const bankCardInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('bankCardInfo', labelNone)
}

export const educationExperienceAuditSchema = (labelNone = false) => {
  return createAuditSchema('educationExperience', labelNone)
}

export const resignationCertificateAuditSchema = (labelNone = false) => {
  return createAuditSchema('resignationCertificate', labelNone)
}

export const healthInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('healthInfo', labelNone)
}

export const relationshipInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('relationshipInfo', labelNone)
}

export const otherInfoAuditSchema = (labelNone = false) => {
  return createAuditSchema('otherInfo', labelNone)
}
