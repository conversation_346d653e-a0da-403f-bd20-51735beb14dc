/**
 * @name 公共配置表
 */

import { Observer } from '@amazebird/antd-schema-form'
import dictService from '../dictService'

const { getDictOptions } = dictService

/**
 * informationBlock 信息块的可编辑字段
 */
export const validateFields = {
  // 个人基本信息
  personBaseInfo: ['politicalStatusCode', 'address', 'usedName'],
  // 身份信息
  identityInfo: [
    'houseRegisterTypeCode',
    'idCardIssuingAuthority',
    'timeIdCard',
    'residenceAddress',
    'idCardTypeCode',
    'ethnicCode',
  ],
  // 教育经历
  educationExperience: ['educationExperiences'],
  // 工作经历及证明
  workExperiences: [
    'companyName',
    'positionName',
    'timeBegin',
    'timeEnd',
    'timeEndIsNow',
    'timeEndWork',
    'leaveCertificates',
    'recruitWorkExperienceId',
  ],
  // 健康信息
  healthInfo: [
    'isHealthCertificateCode',
    'timeHealthEnd',
    'timeCertificateProvide',
    'timeValidPeriodEnd',
    'healthMaterialsType',
  ],
  bankCardInfo: ['branchBankInfo', 'bankAccount', 'bankPlaceCode'],
  // 其他信息
  otherInfo: [
    'isDisabledCode',
    'disabilityCertificateTypeCode',
    'disabilityCertificateRankCode',
    'isVeteranCode',
    'disabilityCertPeriodCode',
    'timeDisabilityCertificate',
    'otherAttachments',
  ],
}

/**
 * 标题渲染
 */
export const renderTitle = {
  personPhoto: '个人照片',
  workCardPhoto: '工牌照片',
  personBaseInfo: '个人基本信息',
  identityInfo: '身份证信息',
  bankCardInfo: '银行账户信息',
  educationExperience: '教育经历',
  resignationCertificate: '工作经历及证明',
  healthInfo: '健康相关信息',
  relationshipInfo: '家庭成员信息',
  otherInfo: '其他信息',
}

/**
 * @description: 获取教育经历的namepath，得到一个二维数组结构
 * @param {*} length 教育经历段数
 * @return {*}
 */
export const getEducationExperienceValidateFields = (length) => {
  const fields = []
  const fieldNames = [
    'id',
    'graduationCertificates',
    'diplomas',
    'educationCode',
    'degreeCode',
    'studyModeCode',
    'graduateSchool',
    'major',
    'isDualDegree',
    'secondMajor',
    'timeEnrollment',
    'timeExpectedGraduate',
    'timeGraduate',
    'timeGraduateIsNow',
    'educationReports',
  ]

  for (let i = 0; i < length; i++) {
    fieldNames.forEach((fieldName) => {
      fields.push(['educationExperiences', i, fieldName])
    })
  }

  return fields
}

/**
 *  Observer 获取字典项
 * @param code
 * @returns
 */

export const WATCH_LOADED_DICT = 'WATCH_LOADED_DICT'
export const WATCH_LOADED_ASYNC_DICT = 'WATCH_LOADED_ASYNC_DICT' // 处理异步字典项

export const ObserverOptions = (code) => {
  return Observer({
    watch: `#${WATCH_LOADED_DICT}`,
    action: (mode) => {
      return getDictOptions(code)
    },
  })
}

/**
 * 审核模块不符合原因字典code匹配
 * 此处与产品确认，匹配字典code前端硬编码处理
 */

export const renderDictFirstCode = {
  personPhoto: 'OMS_IFR_010',
  workCardPhoto: 'OMS_IFR_001',
  personBaseInfo: 'OMS_IFR_002',
  identityInfo: 'OMS_IFR_003',
  bankCardInfo: 'OMS_IFR_004',
  educationExperience: 'OMS_IFR_005',
  resignationCertificate: 'OMS_IFR_007',
  healthInfo: 'OMS_IFR_006',
  relationshipInfo: 'OMS_IFR_008',
  otherInfo: 'OMS_IFR_009',
}

/**
 * 字典硬编码区域
 */
export const XLDICT_DIVIDE = {
  LOW: ['01', '02', '03', '04', '05'], // 学历=小学、初中、中专、高中、大专
  NORMAL: ['06', '07', '14'], // 学历=本科-全日制、本科-成考/自考、本科
  HIGH: ['08', '09', '15'], // 学历=硕士-全日制、硕士-非全日制、硕士研究生
  HIGHEST: ['10', '11', '12', '13', '16'], // 学历=MBA、EMBA、MPA、博士、博士研究生
}

export const XWDICT_DIVIDE = {
  BACHELOR: ['03', '09'], // 学士
  MASTER: ['02', '03', '09'], // 硕士
  DOCTOR: ['01', '02', '03', '09'], // 博士
  NONE: ['09'], // 无
}

export const XLDICT_QRZ = ['01', '02', '04'] // 全日制的学历

export const XLDICT_LOW = [...XLDICT_DIVIDE.LOW.filter((item) => item !== '05')] // 小学、初中、中专、高中 （默认全日制不可更改的学历）

export const FULL_TIME = '01' // 全日制

export const DISABILITY_CERTIFICATE = '1'

export const FILE_ERR_CODE = ['-1', '-2', '-3'] // -1 无法转码 -2 转码失败 -3 转码失败，等待重试

export const XLDICT_DOUBLE_DEGREE = [...XLDICT_DIVIDE.NORMAL, '15', '16'] // 显示双学位字段的学历 本科、硕士研究生、博士研究生

export const TIME_GRADUATE = {
  UP_TO_NOW: 1,
  NOT_UP_TO_NOW: 0,
}

export const RANKDICT_OS = {
  // 主序列O、S
  O: 'O',
  S: 'S',
}

// 大专、本科、硕士研究生、博士研究生
export const XLDICT_LAST = ['05', '14', '15', '16']
