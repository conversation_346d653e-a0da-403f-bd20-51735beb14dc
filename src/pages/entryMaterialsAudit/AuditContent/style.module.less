.auditContainer {
  margin: 24px;
  display: flex;
  height: calc(100vh - 384px);
  margin-bottom: 0px;

  &.auditContainerCollapse {
    height: calc(100vh - 260px);
  }

  &.auditContainerDcs {
    height: calc(100vh - 424px);
  }
}

.anchor_box{
  // margin: 24px;
  // display: flex;
  // height: calc(100vh - 300px);
  // margin-bottom: 0px;
  display: flex;
  flex: 1;

  .anchor_line_box {
    width: 240px;
    height: 100%;
    background-color: #fff;
    padding: 16px;
    // position: sticky;
    // top: 64px;
  }
  .anchor_context_box{
    background-color: #fff;
    width: calc(100% - 220px);
    margin-left: 12px;
    overflow-y: auto;
    padding: 24px;
    min-width: 861px;
    // margin-bottom: 72px;
    .block{
      margin-bottom: 24px;
    }
  }
}

.add_height{
  height: calc(100vh - 340px);
}

.collapse_height {
  height: calc(100vh - 228px);
}
.collapse_width {
  width: calc(100% - 7px) !important;
}

.is_pay_insurance{
  :global{
    .onboarding-form-item-label{
      min-width: 182px
    }
  }
}

.is_exist_interest{
  :global{
    .onboarding-form-item-label{
      min-width: 152px
    }
  }
}
