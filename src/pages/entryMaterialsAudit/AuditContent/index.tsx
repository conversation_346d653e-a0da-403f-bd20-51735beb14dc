/**
 * @description 材料审核模块
 */
import React, { useMemo, useCallback, useState } from 'react'
import classNames from 'classnames'
import { SchemaForm } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { useDebounceFn } from 'ahooks'
import { EntryMaterialAuditInfo } from '@/types/entryMaterialsAudit'
import '@amazebird/antd-field'
import { Anchors, AnchorLine, AnchorContext, AnchorItem } from '@/components/AnchorLine'
import BaseCollapse from '@/components/BaseCollapse'
import { PROCESS_PROGRESS_STATUS } from '@/constants/review'
import { useStore } from '@/stores'
import reviewService from '@/services/preEntry/review'
import { getUrlParam } from '@/utils/utils'
import { useInitService } from '@/pages/entryMaterialsAudit/initContext'
import { IsNeedReview, ProcessProgress } from '@/services/preEntry/list'
import EduInfoService from '@/services/preEntry/eudcationExperience'
import {
  WATCH_LOADED_DICT,
  FULL_TIME,
  WATCH_LOADED_ASYNC_DICT,
  XLDICT_QRZ,
  TIME_GRADUATE,
} from '../config/config'
import Footer from '../Footer.tsx'
import NavAudit from './AuditBlock/NavAudit'
import FormAudit from './AuditBlock/FormAudit'
import businessAuthor from '../businessAuthor'
import Context from './Context'
import styles from './style.module.less'
import informationBlockService from './InformationBlock/service'
import {
  PersonPhoto,
  WorkCardPhoto,
  PersonBaseInfo,
  IdentityInfo,
  BankCardInfo,
  EducationExperience,
  ResignationCertificate,
  HealthInfo,
  RelationshipInfo,
  OtherInfo,
  personPhotoSchema,
  workCardPhotoSchema,
  personBaseInfoSchema,
  identityInfoSchema,
  bankCardInfoSchema,
  educationExperienceSchema,
  resignationCertificateSchema,
  healthInfoSchema,
  relationshipInfoSchema,
  otherInfoSchema,
} from './InformationBlock'
import './InformationBlock/registerComponent'
import {
  personPhotoAuditSchema,
  workCardPhotoAuditSchema,
  personBaseInfoAuditSchema,
  identityInfoAuditSchema,
  bankCardInfoAuditSchema,
  educationExperienceAuditSchema,
  resignationCertificateAuditSchema,
  healthInfoAuditSchema,
  relationshipInfoAuditSchema,
  otherInfoAuditSchema,
} from '../config/auditConfig'

interface Iprops {
  topBaseInfoCollapse: boolean
}

const MaterialsAuditForm: React.FC<Iprops> = ({ topBaseInfoCollapse }: Iprops) => {
  const form: any = SchemaForm.createForm()
  const [isFieldsTouched, setIsFieldsTouched] = useState<boolean>(false)
  const [anchorLineCollapse, setAnchorLineCollapse] = useState(false)
  const { hasNeedBadge, hasCanEditInfoAuth } = businessAuthor
  const { auditStatus, getAuditStatus, errorFields } = useStore()
  const { formData } = informationBlockService
  const service = useInitService()

  const schema: SchemaType = {
    ...personPhotoSchema,
    ...workCardPhotoSchema,
    ...personBaseInfoSchema,
    ...identityInfoSchema,
    ...bankCardInfoSchema,
    ...educationExperienceSchema,
    ...resignationCertificateSchema,
    ...healthInfoSchema,
    ...relationshipInfoSchema,
    ...otherInfoSchema,

    ...personPhotoAuditSchema(),
    ...workCardPhotoAuditSchema(),
    ...personBaseInfoAuditSchema(),
    ...identityInfoAuditSchema(),
    ...bankCardInfoAuditSchema(),
    ...educationExperienceAuditSchema(),
    ...resignationCertificateAuditSchema(),
    ...healthInfoAuditSchema(),
    ...relationshipInfoAuditSchema(),
    ...otherInfoAuditSchema(),
  }

  /**
   * 渲染标题与组件
   * @param key 标题或组件
   * @returns 返回标题组或组件组object
   */
  const renderTitleAndComponent = (key: 'title' | 'component'): Record<string, any> => {
    const title = key === 'title'
    const mapList: Record<string, any> = {
      personPhoto: title ? '个人照片' : <PersonPhoto />,
      workCardPhoto: title ? '工牌照片' : <WorkCardPhoto />,
      personBaseInfo: title ? '个人基本信息' : <PersonBaseInfo />,
      identityInfo: title ? '身份证信息' : <IdentityInfo />,
      bankCardInfo: title ? '银行账户信息' : <BankCardInfo />,
      educationExperience: title ? '教育经历' : <EducationExperience />,
      ...(formData.otherInfo?.isHasWorkExperienceModule
        ? { resignationCertificate: title ? '工作经历及证明' : <ResignationCertificate /> }
        : {}),
      healthInfo: title ? '健康相关信息' : <HealthInfo />,
      relationshipInfo: title ? '家庭成员信息' : <RelationshipInfo />,
      otherInfo: title ? '其他信息' : <OtherInfo />,
    }
    if (!hasNeedBadge()) {
      delete mapList.workCardPhoto
    }
    return mapList
  }

  const memoForm = useMemo(
    () => ({
      form,
    }),
    [form],
  )

  useCallback(() => {
    // @ts-ignore
    form.setState({ [WATCH_LOADED_DICT]: true })
    // @ts-ignore
    form.setState({ [WATCH_LOADED_ASYNC_DICT]: true })
  }, [form])

  const { run } = useDebounceFn(
    async () => {
      const entryMaterialId = getUrlParam('id', window.location.href)
      const $formData = await reviewService.getEntryMaterialDetail(entryMaterialId, {
        noMessage: true,
      })
      reviewService.saveDraft({
        formInstance: form,
        formData,
        updateCallback: service.upLoad,
        updateFormData: $formData,
      })
    },
    {
      wait: 3000,
    },
  )

  /**
   * 监听表单值变化
   * 处理需要联动清空表单的逻辑
   */
  const onValuesChange = useCallback((changedValues: any) => {
    // 处理草稿
    if (
      hasCanEditInfoAuth() &&
      [ProcessProgress.Audit, ProcessProgress.PendingReview].includes(
        formData?.processInfo?.processProgress,
      )
    ) {
      run()
    }
    // 变更了教育经历某段的学历
    if (
      changedValues.educationExperiences &&
      changedValues.educationExperiences.some((item) => item && item.educationCode !== undefined)
    ) {
      // 变更学历的那段教育经历中学位要清空
      const changeIndex = changedValues.educationExperiences.findIndex(
        (item) => item && item.educationCode !== undefined,
      )
      form.setFieldValue(['educationExperiences', changeIndex, 'degreeCode'], null)

      // 处理最高学历实时变化的逻辑
      const maxEducationCode = EduInfoService.getMaxHighEducationCode(
        form.getFieldValue('educationExperiences'),
      ).maxEducationCode
      const index = form
        .getFieldValue('educationExperiences')
        .findIndex((item) => item && item.educationCode === maxEducationCode)
      form.getFieldValue('educationExperiences').forEach((_, i) => {
        // 变更最高学历，并且不是当前项,选择了至今，需要清空毕业日期以及预计毕业日期
        if (
          form.getFieldValue(['educationExperiences', i, 'isHighestEducation']) &&
          index !== i &&
          form.getFieldValue(['educationExperiences', i, 'timeGraduateIsNow']) ===
            TIME_GRADUATE.UP_TO_NOW
        ) {
          form.setFieldValue(['educationExperiences', i, 'timeGraduate'], null)
          form.setFieldValue(['educationExperiences', i, 'timeExpectedGraduate'], null)
          form.setFieldValue(
            ['educationExperiences', i, 'timeGraduateIsNow'],
            TIME_GRADUATE.NOT_UP_TO_NOW,
          )
        }
        form.setFieldValue(['educationExperiences', i, 'isHighestEducation'], false)
      })
      form.setFieldValue(['educationExperiences', index, 'isHighestEducation'], true)
    }
    // 某段教育经历开始日期变化、工作经历结束日期变化，需要手动触发自定义validator的校验
    if (
      (changedValues.educationExperiences &&
        changedValues.educationExperiences.some(
          (item) => item && item.timeEnrollment !== undefined,
        )) ||
      changedValues.timeEnd
    ) {
      form.validateFields()
    }
    if (hasCanEditInfoAuth()) {
      setIsFieldsTouched(true)
    }
  }, [])

  return (
    <div
      className={classNames(
        styles.auditContainer,
        topBaseInfoCollapse ? styles.auditContainerCollapse : '',
        !topBaseInfoCollapse &&
          formData?.processInfo?.processProgress === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
          ? styles.auditContainerDcs
          : '',
      )}
    >
      <Anchors
        className={classNames(
          styles.anchor_box,
          // formData?.processInfo?.processProgress === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
          //   ? styles.add_height
          //   : '',
          // topBaseInfoCollapse ? styles.collapse_height : '',
        )}
      >
        <BaseCollapse
          direction="vertical"
          defaultStyle={{ background: 'transparent' }}
          callback={(collapse) => setAnchorLineCollapse(collapse)}
        >
          <AnchorLine className={styles.anchor_line_box} />
        </BaseCollapse>
        <AnchorContext
          className={classNames(
            styles.anchor_context_box,
            anchorLineCollapse ? styles.collapse_width : '',
          )}
        >
          <SchemaForm
            scrollToFirstError={{ block: 'center' }}
            schema={schema}
            form={form}
            // size="small"
            labelCol={{ span: 10 }}
            wrapperCol={{ span: 14 }}
            onValuesChange={onValuesChange}
          >
            <Context.Provider value={memoForm}>
              {Object.keys(renderTitleAndComponent('title')).map((key) => {
                return auditStatus[key] ? (
                  <AnchorItem
                    key={key}
                    title={renderTitleAndComponent('title')[key]}
                    message={
                      <NavAudit
                        indexes={key}
                        auditStatus={getAuditStatus() as EntryMaterialAuditInfo}
                      />
                    }
                    status={errorFields[key]}
                  >
                    <div className={styles.block}>
                      {renderTitleAndComponent('component')[key]}
                      <FormAudit indexes={key} />
                    </div>
                  </AnchorItem>
                ) : null
              })}
            </Context.Provider>
          </SchemaForm>
        </AnchorContext>
      </Anchors>
      <Footer form={form} formData={formData} isFieldsTouched={isFieldsTouched} />
    </div>
  )
}

export default MaterialsAuditForm
