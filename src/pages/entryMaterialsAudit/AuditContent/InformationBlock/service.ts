import { EntryMaterialDetail } from '@/types/entryMaterialsAudit'

type OptionType = {
  label: string
  value: string
  children?: OptionType[]
}

class InformationBlockService {
  /**
   * 信息模块的数据
   */
  formData = {} as EntryMaterialDetail

  /**
   * 信息模块的初始数据，与服务端数据一致，不含缓存和表单值变更
   */
  originFormData = {} as EntryMaterialDetail

  /**
   * 初始模块信息模块的数据
   */

  initOriginFormData = (params: EntryMaterialDetail): void => {
    this.originFormData = params
  }

  /**
   * 表单初始化信息模块的数据（可能有缓存数据）
   * @param params
   */
  initFormData = (params: EntryMaterialDetail): void => {
    this.formData = params
  }

  /**
   * 取消编辑
   * @param key  信息模块的key
   * @param targetValue 信息模块的值
   */
  cancelItem = (key: string, targetValue): void => {
    this.formData[key] = targetValue
  }

  /**
   * 完成编辑
   * @param key 信息模块的key
   * @param targetValue 信息模块的值
   */
  completeItem = (key: string, targetValue): void => {
    if (Array.isArray(this.formData[key])) {
      // 多段教育经历，工作经历服务端接收数组，但是只有一段
      // FIXME: 临时处理方案，此处key对应接口的educationExperiences
      if (key === 'educationExperiences') {
        this.formData[key] = targetValue
      } else {
        // 工作经历包装成接口接收的数组格式，临时处理
        this.formData[key] = [targetValue]
      }
    } else {
      this.formData[key] = {
        ...this.formData[key],
        ...targetValue,
      }
    }
  }

  /**
   * 获取信息模块的数据
   * @param key
   * @returns
   */
  getFormData = (key: string) => this.formData[key]
}

export default new InformationBlockService()
