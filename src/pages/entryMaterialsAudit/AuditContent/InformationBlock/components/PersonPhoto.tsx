import React, { useContext } from 'react'
import { useMount } from 'ahooks'
import { Item, ItemGroup } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Card, Image } from 'antd'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import ossService from '@/pages/entryMaterialsAudit/ossService'

export const schema: SchemaType = {
  personalPhotoUrl: {
    label: '个人照片',
    component: (props) => {
      return props.value ? <Image width={200} src={props.value} /> : <div>无个人照片</div>
    },
  },
}

export default function PersonPhotoForm() {
  const { form } = useContext(Context)
  const { formData } = informationBlockService
  const { getOssSourceUrl } = ossService

  useMount(() => {
    const personalPhotoUrl = formData.identityInfo?.personalPhoto?.url
    ;(async () => {
      const url = await getOssSourceUrl(personalPhotoUrl)
      form.setFieldsValue({
        personalPhotoUrl: url || '',
      })
    })()
  })

  return (
    <ItemGroup schema={schema}>
      <Card title="个人照片">
        <Item field="personalPhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
      </Card>
    </ItemGroup>
  )
}
