/**
 * @description 附件图片组件
 */
import React, { useState } from 'react'
import { useDeepCompareEffect } from 'ahooks'
import { Image } from 'antd'
import PdfPreview from '@/components/PdfPreview'
import DocPreview from '@/components/DocPreview'
import ossService from '@/pages/entryMaterialsAudit/ossService'
import { ATTACHMENT_TYPE } from '@/constants/review'
import { FILE_ERR_CODE } from '@/pages/entryMaterialsAudit/config/config'

interface ImageGroupProps {
  width: number
  values: Record<string, any>[] | undefined
}

// 常量定义
const IMAGE_EXTENSIONS: string[] = ['jpg', 'png', 'jpeg']
const PDF_EXTENSIONS: string[] = ['pdf']
const DOC_EXTENSIONS: string[] = ['doc', 'docx']
// const PREVIEW_EXTENSIONS: string[] = ['preview']

const ImageGroup: React.FC<ImageGroupProps> = ({ width = 634, values }) => {
  const { getOssSourceUrl } = ossService
  const [imgUrls, setImgUrls] = useState<string[]>([])
  const [pdfUrls, setPdfUrls] = useState<string[]>([])
  const [docUrls, setDocUrls] = useState<string[]>([])

  const getSuffix = (name: string): string | undefined => {
    return name.split('?')[0].split('.').pop()?.toLowerCase()
  }

  useDeepCompareEffect(() => {
    const imgValues: any = []
    const docValues: any = []
    const pdfValues: any = []
    values?.forEach((item) => {
      const suffix = getSuffix(item.fileName || '')
      if (item.attachmentType === ATTACHMENT_TYPE.PHOTO || !item.attachmentType) {
        // 图片类型
        imgValues.push(item.url)
      } else if (item.attachmentType === ATTACHMENT_TYPE.FILE) {
        // 文件类型
        if (item.previewId && !FILE_ERR_CODE.includes(item.previewId)) {
          // 离职证明：文件名称以【.doc】或者【.docx】为后缀才进行转码 转码为pdf
          // 健康材料附件：文件名称以【.doc】或者【.docx】为后缀才进行转码 转码为pdf
          pdfValues.push(item.previewId)
        } else {
          // 文件类型有文件名称 根据文件名称判断
          if (DOC_EXTENSIONS.includes(suffix as string)) {
            docValues.push(item.url)
          }
          if (PDF_EXTENSIONS.includes(suffix as string)) {
            pdfValues.push(item.url)
          }
          if (IMAGE_EXTENSIONS.includes(suffix as string)) {
            imgValues.push(item.url)
          }
        }
      }
    })
    ;(async () => {
      const realImgValues = await getOssSourceUrl(imgValues || [])
      const realPdfValues = await getOssSourceUrl(pdfValues || [])
      const realDocValues = await getOssSourceUrl(docValues || [])
      setImgUrls(realImgValues as string[])
      setPdfUrls(realPdfValues as string[])
      setDocUrls(realDocValues as string[])
    })()
  }, [values])

  if (!values || values?.length === 0) {
    return <div>无此类附件材料</div>
  }

  // renderImageGroup 用于渲染图片组件
  const renderImageGroup = (): React.ReactNode => {
    if (imgUrls?.length) {
      return (
        <Image.PreviewGroup>
          {imgUrls?.map((item) => (
            <Image key={item} width={width} src={item} />
          ))}
        </Image.PreviewGroup>
      )
    }
    return null
  }

  // renderPdfGroup 用于渲染pdf组件
  const renderPdfGroup = (): React.ReactNode => {
    if (pdfUrls?.length) {
      return pdfUrls?.map((item) => <PdfPreview key={item} pdfUrl={item} />)
    }
    return null
  }

  // renderDocGroup 用于渲染doc组件
  const renderDocGroup = (): React.ReactNode => {
    if (docUrls?.length) {
      return docUrls?.map((item) => (
        <DocPreview key={item} docUrl={item} style={{ marginTop: 8 }} />
      ))
    }
    return null
  }

  return (
    <>
      {renderImageGroup()}
      {renderPdfGroup()}
      {renderDocGroup()}
    </>
  )
}

export default ImageGroup
