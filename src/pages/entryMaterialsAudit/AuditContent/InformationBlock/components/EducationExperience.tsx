import React, { useContext, useMemo } from 'react'
import { Item, ItemGroup, FormItemGrid, Observer, NOT_ACTION } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Radio, DatePicker, Select } from 'antd'
import dayjs from 'dayjs'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { useMount } from 'ahooks'
import moment from 'moment'
import { FORM_MODE, WATCH_MODES, EMPLOYEE_TYPE, PROCESS_PROGRESS_STATUS } from '@/constants/review'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import AttachFile from '@/components/AttachFile'
import { Upload } from '@galaxy/upload-component'
import EduInfoService from '@/services/preEntry/eudcationExperience'
import UploadSdk from '@galaxy/upload'
import { isNil, map } from 'lodash-es'
import EditCard from './EditCard'
import ImageGroup from './ImageGroup'
import {
  ObserverOptions,
  XLDICT_DIVIDE,
  XWDICT_DIVIDE,
  XLDICT_QRZ,
  XLDICT_DOUBLE_DEGREE,
  XLDICT_LOW,
  FULL_TIME,
  TIME_GRADUATE,
  RANKDICT_OS,
  XLDICT_LAST,
} from '../../../config/config'
import { DatePickerWithUpToNow } from './DatePickerWithUpToNow'
import dictService from '../../../dictService'

type OptionsType = {
  label: string
  value: string
}

const uploadSdk = new UploadSdk({ oss: { service: '/admin/entry' } })

/**
 * @description: 自定义组件，用于兼容大易简历中学历、学位字段为中文的情况（详情态）
 * @param {*} param1
 * @return {*}
 */
const CustomDetailSelect = ({ value, options }) => {
  const getLabel = () => {
    if (isNil(value)) {
      return '--'
    }
    const option = options.find((item) => item.value === value)
    if (options.find((item) => item.value === value)) {
      return option?.label || '--'
    }
    return value
  }
  return <div>{getLabel()}</div>
}

/**
 * @description: 自定义upload组件，用于处理接口接收参数url实际为uuid的转换问题（接口变量url 对应 uuid）
 * @param {*} param1
 * @return {*}
 */
const CustomUpload = ({ value, onChange, ...restProps }) => {
  const uploadValue = value.map((item) => {
    return {
      ...item,
      uuid: item.url,
      name: item.fileName,
      url: item.fileUrl,
    }
  })
  const handleChange = (changeValue) => {
    const newValue = changeValue.map((item) => {
      return {
        url: item.uuid,
        fileName: item.name,
        attachmentType: 0, // 照片
      }
    })
    onChange?.(newValue)
  }
  const memoValue = useMemo(() => uploadValue, [value])
  const memoOnChange = useMemo(() => handleChange, [])
  return <Upload {...restProps} value={memoValue} onChange={memoOnChange} />
}

const DegreeSelect = ({ value, options, action, ...restProps }) => {
  // 210增补需求，兼容招聘数据处理
  const valueLabel = options.find((item) => item.value === value)?.label
  const isOptionValue = options.map((item) => item.value).includes(value)

  const getOptions = (educationCode): OptionsType[] => {
    if (XLDICT_DIVIDE.LOW.includes(educationCode)) {
      return options.filter((item) => XWDICT_DIVIDE.NONE.includes(item.value))
    }
    if (XLDICT_DIVIDE.NORMAL.includes(educationCode)) {
      return options.filter((item) => XWDICT_DIVIDE.BACHELOR.includes(item.value))
    }
    if (XLDICT_DIVIDE.HIGH.includes(educationCode)) {
      return options.filter((item) => XWDICT_DIVIDE.MASTER.includes(item.value))
    }
    if (XLDICT_DIVIDE.HIGHEST.includes(educationCode)) {
      return options.filter((item) => XWDICT_DIVIDE.DOCTOR.includes(item.value))
    }
    return options
  }

  return (
    <Select
      options={getOptions(action)}
      value={isOptionValue ? valueLabel : value}
      {...restProps}
    />
  )
}

export const itemSchema: SchemaType = {
  graduationCertificates: {
    label: '学历证书',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return CustomUpload
          default:
            return (props) => {
              return <ImageGroup width={634} values={props.value} />
            }
        }
      },
    }),
    props: {
      accept: 'image/png,image/jpeg',
      listType: 'picture-card',
      fileSizeLimit: 10,
      multiple: true,
      maxNum: 10,
      oss: {
        service: '/admin/entry',
      },
      remark: '仅支持.png .jpg .jpeg格式，单张10M以内',
    },
    rules: Observer({
      watch: ['educationExperiences[].educationCode', 'educationExperience_itemAuditResult'],
      action: ([educationCode, itemAuditResult]) => {
        // 非OS序列并且学历=大专、本科、硕士研究生、博士研究生 => 必填
        if (
          informationBlockService.formData.entryInfo?.isOsCode === '0' &&
          XLDICT_LAST.includes(educationCode) &&
          itemAuditResult === 1
        ) {
          return [
            {
              required: true,
              message: '请添加学历证书',
            },
          ]
        }
        return [{ required: false }]
      },
    }),
    visible: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        // 非OS序列、主序列为S或O，并且学历=大专、本科、硕士研究生、博士研究生 => 显示字段
        return (
          informationBlockService.formData.entryInfo?.isOsCode === '0' ||
          ((informationBlockService.formData.entryInfo.mainSequenceCode === RANKDICT_OS.S ||
            informationBlockService.formData.entryInfo.mainSequenceCode === RANKDICT_OS.O) &&
            XLDICT_LAST.includes(educationCode))
        )
      },
    }),
  },
  diplomas: {
    label: '学位证书',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => {
              return <CustomUpload {...props} />
            }
          default:
            return (props) => {
              return <ImageGroup width={634} values={props.value} />
            }
        }
      },
    }),
    props: {
      accept: 'image/png,image/jpeg',
      listType: 'picture-card',
      fileSizeLimit: 10,
      multiple: true,
      maxNum: 10,
      oss: {
        service: '/admin/entry',
      },
      isPublic: false,
      remark: '仅支持.png .jpg .jpeg格式，单张10M以内',
    },
    visible: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        // 主序列不等于O，并且学历=本科、硕士研究生、博士研究生 => 显示字段
        return (
          informationBlockService.formData.entryInfo.mainSequenceCode !== RANKDICT_OS.O &&
          XLDICT_LAST.filter((code) => code !== '05').includes(educationCode)
        )
      },
    }),
  },
  educationCode: {
    label: '学历',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return CustomDetailSelect
        }
      },
    }),
    placeholder: '请选择学历',
    options: ObserverOptions('BM_XL'),
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        const processTypeCode = informationBlockService.formData.processInfo?.processProgress // 处理进度
        const rules: any = []
        if (
          // 允许编辑的处理进度，添加校验
          processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
          processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
        ) {
          rules.push({
            validator: (_, value) => {
              const DictValues = map(dictService.getDictOptions('BM_XL'), 'value')
              if (value && !DictValues.includes(value)) {
                return Promise.reject(new Error(`请选择正确的学历`))
              }
              return Promise.resolve()
            },
          })
        }
        auditResult === 1
          ? rules.push({ required: true, message: '请选择学历' })
          : rules.push({ required: false })
        return rules
      },
    }),
  },
  degreeCode: {
    label: '学位',
    component: Observer({
      watch: [`#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`, 'educationExperiences[].educationCode'],
      action: (mode) => {
        switch (mode[0]) {
          case FORM_MODE.MODIFY:
            return (props) => <DegreeSelect action={mode[1]} {...props} />
          default:
            return CustomDetailSelect
        }
      },
    }),
    placeholder: '请选择学位',
    options: ObserverOptions('BM_XW'),
    visible: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => !!educationCode,
    }),
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult, formParams) => {
        const processTypeCode = informationBlockService.formData.processInfo?.processProgress // 处理进度
        const rules: any = []
        if (
          // 允许编辑的处理进度，添加校验
          processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
          processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
        ) {
          rules.push({
            validator: (_, value) => {
              const educationCode = formParams.form.getFieldValue([
                'educationExperiences',
                formParams.name[1],
                'educationCode',
              ])
              const dictOption = dictService.getDictOptions('BM_XW').map((item) => {
                return {
                  label: item.label,
                  value: item.value,
                }
              })
              const getOptions = () => {
                if (XLDICT_DIVIDE.LOW.includes(educationCode)) {
                  return dictOption.filter((item) => XWDICT_DIVIDE.NONE.includes(item.value))
                }
                if (XLDICT_DIVIDE.NORMAL.includes(educationCode)) {
                  return dictOption.filter((item) => XWDICT_DIVIDE.BACHELOR.includes(item.value))
                }
                if (XLDICT_DIVIDE.HIGH.includes(educationCode)) {
                  return dictOption.filter((item) => XWDICT_DIVIDE.MASTER.includes(item.value))
                }
                if (XLDICT_DIVIDE.HIGHEST.includes(educationCode)) {
                  return dictOption.filter((item) => XWDICT_DIVIDE.DOCTOR.includes(item.value))
                }
                return dictOption
              }
              const optionValue = getOptions().map((item) => item.value)
              if (value && !optionValue.includes(value)) {
                return Promise.reject(new Error(`请选择正确的学位`))
              }
              return Promise.resolve()
            },
          })
        }
        auditResult === 1
          ? rules.push({ required: true, message: '请选择学位' })
          : rules.push({ required: false })
        return rules
      },
    }),
  },
  studyModeCode: {
    label: '学习形式',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <Radio.Group {...props} />
          default:
            return 'Select.Text'
        }
      },
    }),
    options: ObserverOptions('BM_studymode'),
    disabled: Observer({
      watch: ['educationExperiences[].educationCode'],
      action: ([educationCode]) => {
        // 学历为XLDICT_LOW，固定为全日制且不可修改
        return XLDICT_LOW.includes(educationCode)
      },
    }),
    value: Observer({
      watch: ['educationExperiences[].educationCode'],
      action: ([educationCode]) => {
        if (XLDICT_LOW.includes(educationCode)) {
          return FULL_TIME
        }
        return NOT_ACTION
      },
    }),
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        return auditResult === 1
          ? [{ required: true, message: '请选择学习形式' }]
          : [{ required: false }]
      },
    }),
  },
  graduateSchool: {
    label: '毕业院校',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode, formParams) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            if (
              formParams.form.getFieldValue([
                'educationExperiences',
                formParams.name[1],
                'graduateSchool',
              ]) === ''
            ) {
              return () => <div>--</div>
            }
            return 'Input.Text'
        }
      },
    }),
    placeholder: '请输入毕业院校',
    max: 50,
    type: 'string',
    min: 1,
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        const rule: any = [
          {
            pattern: /^((?![0-9]).)*$/,
            message: '不可输入数字',
          },
        ]
        if (auditResult === 1) {
          rule.push({ required: true, message: '请输入毕业院校' })
        }
        return rule
      },
    }),
  },
  major: {
    label: '专业',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode, formParams) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            if (
              formParams.form.getFieldValue([
                'educationExperiences',
                formParams.name[1],
                'major',
              ]) === ''
            ) {
              return () => <div>--</div>
            }
            return 'Input.Text'
        }
      },
    }),
    placeholder: '请输入专业',
    max: 50,
    type: 'string',
    min: 1,
    disabled: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        return XLDICT_QRZ.includes(educationCode)
      },
    }),
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        const rules: any = [
          {
            pattern: /^((?![0-9]).)*$/,
            message: '不可输入数字',
          },
          {
            max: 50,
          },
        ]

        if (auditResult === 1) {
          rules.push({ required: true, message: '请输入专业' })
        }
        return rules
      },
    }),
    value: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        if (XLDICT_QRZ.includes(educationCode)) {
          return '无'
        }
        return NOT_ACTION
      },
    }),
  },
  isDualDegree: {
    label: '是否双学位',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    visible: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        return XLDICT_DOUBLE_DEGREE.includes(educationCode)
      },
    }),
    value: Observer({
      watch: [`#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`, 'educationExperiences[].educationCode'],
      action: ([_, educationCode], formParams) => {
        if (
          !XLDICT_DOUBLE_DEGREE.includes(educationCode) ||
          formParams.form.getFieldValue([
            'educationExperiences',
            formParams.name[1],
            'isDualDegree',
          ]) === -1
        ) {
          return 0
        }
        return NOT_ACTION
      },
    }),
    options: [
      // 【v2.1.0】是否双学位 1-是 0-否 默认-1
      { label: '是', value: 1 },
      { label: '否', value: 0 },
    ],
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        return auditResult === 1
          ? [{ required: true, message: '请选择是否双学位' }]
          : [{ required: false }]
      },
    }),
  },
  secondMajor: {
    label: '第二专业',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    visible: Observer({
      watch: 'educationExperiences[].isDualDegree',
      action: (isDualDegree) => {
        return isDualDegree === 1 // 双学位选择是
      },
    }),
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        const rules: any = [
          {
            pattern: /^((?![0-9]).)*$/,
            message: '不可输入数字',
          },
          {
            max: 50,
          },
        ]

        if (auditResult === 1) {
          rules.push({ required: true, message: '请输入第二专业' })
        }
        return rules
      },
    }),
  },
  timeEnrollment: {
    label: '开始日期',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    props: {
      disabledDate: (current) => {
        return current && current >= dayjs().startOf('day')
      },
    },
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        return auditResult === 1
          ? [{ required: true, message: '请选择开始日期' }]
          : [
              {
                validator: (_, value) => {
                  if (value && value >= dayjs().startOf('day')) {
                    return Promise.reject(
                      new Error(`仅允许选择${dayjs().format('YYYY-MM-DD')}之前的日期`),
                    )
                  }
                  return Promise.resolve()
                },
              },
            ]
      },
    }),
  },
  timeExpectedGraduate: {
    label: '预计毕业日期',
    component: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    visible: Observer({
      watch: ['educationExperiences[].timeGraduateIsNow', 'educationExperiences[].timeGraduate'],
      action: ([timeGraduateIsNow, timeGraduate]) => {
        const employmentTypeCode = informationBlockService.formData.entryInfo?.employmentTypeCode // 员工类型为实习、学生兼职
        return (
          (employmentTypeCode === EMPLOYEE_TYPE.INTERNSHIP ||
            employmentTypeCode === EMPLOYEE_TYPE.STUDENT_PART_TIME) &&
          timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW &&
          timeGraduate
        )
      },
    }),
    props: {
      disabledDate: (current) => {
        const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 预计报到日期
        return current.isSameOrBefore(timeEntry, 'day')
      },
    },
    rules: Observer({
      watch: 'educationExperience_itemAuditResult',
      action: (auditResult) => {
        const rules: any = [
          {
            validator: (_, value) => {
              const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 预计报到日期
              if (value && value.isSameOrBefore(timeEntry, 'day')) {
                return Promise.reject(
                  new Error(
                    timeEntry
                      ? `仅允许选择${dayjs(timeEntry).format('YYYY-MM-DD')}之后的日期`
                      : `
                  仅允许选择预计报到日期之后的日期
                  `,
                  ),
                )
              }
              return Promise.resolve()
            },
          },
        ]
        if (auditResult === 1) {
          rules.push({ required: true, message: '请选择预计毕业日期' })
        }
        return rules
      },
    }),
  },
  timeGraduate: {
    label: '毕业日期',
    component: Observer({
      watch: [
        `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
        'educationExperiences[].timeGraduateIsNow',
        'educationExperiences[].isHighestEducation',
      ],
      action: ([mode, timeGraduateIsNow, isHighestEducation], formParams) => {
        const employmentTypeCode = informationBlockService.formData.entryInfo?.employmentTypeCode // 员工类型
        const educationCode = formParams.form.getFieldValue([
          'educationExperiences',
          formParams.name[1],
          'educationCode',
        ])
        // 没有缓存自定义字段isHighestEducation，所以在这个字段为undefined的情况下，需要实时计算
        const isHighest =
          isHighestEducation ||
          educationCode ===
            EduInfoService.getMaxHighEducationCode(
              formParams.form.getFieldValue('educationExperiences'),
            ).maxEducationCode
        switch (mode) {
          case FORM_MODE.MODIFY:
            if (
              isHighest &&
              (employmentTypeCode === EMPLOYEE_TYPE.INTERNSHIP ||
                employmentTypeCode === EMPLOYEE_TYPE.STUDENT_PART_TIME)
            ) {
              return (props) => (
                <DatePickerWithUpToNow
                  {...props}
                  onChange={({ value, isUpToNow }) => {
                    const $isUpToNow = isUpToNow
                      ? TIME_GRADUATE.UP_TO_NOW
                      : TIME_GRADUATE.NOT_UP_TO_NOW
                    formParams.form.setFieldValue(
                      ['educationExperiences', formParams.name[1], 'timeGraduateIsNow'],
                      $isUpToNow,
                    )
                    props.onChange(value)
                  }}
                  upToNow={timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW}
                />
              )
            }
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              if (timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW) {
                return '至今'
              }
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    props: Observer({
      watch: 'educationExperiences[].timeEnrollment',
      action: (timeEnrollment) => {
        const disabledDate = (currentDate) => {
          const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 入职日期
          dayjs.extend(isSameOrBefore)
          return !(
            dayjs(currentDate).isAfter(timeEnrollment, 'day') &&
            dayjs(currentDate).isSameOrBefore(timeEntry, 'day')
          )
        }
        return {
          disabledDate,
        }
      },
    }),
    rules: Observer({
      watch: [
        'educationExperience_itemAuditResult',
        'educationExperiences[].timeGraduateIsNow',
        'educationExperiences[].educationCode',
        'educationExperiences[].timeEnrollment',
      ],
      action: ([auditResult, timeGraduateIsNow, educationCode, timeEnrollment], formParams) => {
        const rules: any = [
          {
            validator: (_, value) => {
              const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 入职日期
              dayjs.extend(isSameOrBefore)
              if (
                !(
                  dayjs(value).isAfter(
                    formParams.form.getFieldValue([
                      'educationExperiences',
                      formParams.name[1],
                      'timeEnrollment',
                    ]),
                    'day',
                  ) && dayjs(value).isSameOrBefore(timeEntry, 'day')
                ) &&
                value &&
                formParams.form.getFieldValue([
                  'educationExperiences',
                  formParams.name[1],
                  'timeEnrollment',
                ]) &&
                formParams.form.getFieldValue([
                  'educationExperiences',
                  formParams.name[1],
                  'timeGraduateIsNow',
                ]) !== TIME_GRADUATE.UP_TO_NOW
              ) {
                return Promise.reject(
                  new Error(
                    `仅允许选择${dayjs(
                      formParams.form.getFieldValue([
                        'educationExperiences',
                        formParams.name[1],
                        'timeEnrollment',
                      ]),
                    ).format('YYYY-MM-DD')}之后~${dayjs(timeEntry).format('YYYY-MM-DD')}当天`,
                  ),
                )
              }
              return Promise.resolve()
            },
          },
        ]
        if (
          timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW &&
          educationCode !==
            EduInfoService.getMaxHighEducationCode(
              formParams.form.getFieldValue('educationExperiences'),
            ).maxEducationCode
        ) {
          rules.push({ message: '仅最高学历允许选择"至今"' })
        }
        if (auditResult === 1) {
          rules.push({ required: true, message: '请选择毕业日期' })
        }
        return rules
      },
    }),
    value: Observer({
      watch: ['educationExperiences[].educationCode', 'educationExperiences[].timeGraduateIsNow'],
      action: ([educationCode, timeGraduateIsNow], formParams) => {
        if (
          educationCode !==
            EduInfoService.getMaxHighEducationCode(
              formParams.form.getFieldValue('educationExperiences'),
            ).maxEducationCode &&
          timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW
        ) {
          formParams.form.setFieldValue(
            ['educationExperiences', formParams.name[1], 'timeGraduateIsNow'],
            TIME_GRADUATE.NOT_UP_TO_NOW,
          )
          return undefined
        }
        return NOT_ACTION
      },
    }),
  },
  // 毕业日期是否至今
  timeGraduateIsNow: {},
  isHighestEducation: {
    visible: false,
    value: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode, formParams) => {
        return (
          educationCode ===
          EduInfoService.getMaxHighEducationCode(
            formParams.form.getFieldValue('educationExperiences'),
          ).maxEducationCode
        )
      },
    }),
  },
  educationReports: {
    label: '学历验证报告',
    props: Observer({
      watch: `#${WATCH_MODES.EDUCATION_EXPERIENCE_MODE}`,
      action: (mode) => {
        return {
          readOnly: mode !== FORM_MODE.MODIFY,
          dragSort: true,
          fileSizeLimit: 10,
          fileLengthLimit: 10,
          formatList: ['jpg', 'png', 'pdf', 'doc', 'docx'],
        }
      },
    }),
    visible: Observer({
      watch: 'educationExperiences[].educationCode',
      action: (educationCode) => {
        return XLDICT_LAST.includes(educationCode)
      },
    }),
    rules: Observer({
      watch: ['educationExperience_itemAuditResult', 'educationExperiences[].educationCode'],
      action: ([auditResult, educationCode]) => {
        return XLDICT_LAST.includes(educationCode) && auditResult === 1
          ? [
              {
                required: true,
                message: '请上传学历验证报告',
              },
            ]
          : [{ required: false }]
      },
    }),
    component: AttachFile,
  },
}

export const schema: SchemaType = {
  educationExperiences: {
    type: 'array',
    required: true,
    item: {
      type: 'object',
      fields: itemSchema,
    },
  },
}

export default function EducationExperience() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService

  /**
   * @description: 使用sdk批量下载数据中的图片，接口返回的uuid
   * @return {*}
   */
  const handleImageValues = async () => {
    const diplomasUrls = await Promise.all(
      formData?.educationExperiences?.map((item) =>
        item.diplomas && item.diplomas.length > 0
          ? Promise.all(
              item.diplomas.map(async (value) => {
                const uuid = value.url
                const { url } = await uploadSdk.download(uuid)
                return url
              }),
            )
          : [],
      ),
    )

    const graduationCertificateUrls = await Promise.all(
      formData?.educationExperiences.map((item) =>
        item.graduationCertificates && item.graduationCertificates.length > 0
          ? Promise.all(
              item.graduationCertificates.map(async (value) => {
                const uuid = value.url
                const { url } = await uploadSdk.download(uuid)
                return url
              }),
            )
          : [],
      ),
    )

    return {
      diplomasUrls,
      graduationCertificateUrls,
    }
  }

  /**
   * 抽离出来的设置教育经历表单字段的方法
   */
  const setEducationFormFields = async () => {
    const { diplomasUrls, graduationCertificateUrls } = await handleImageValues()
    const handleFormData = formData?.educationExperiences.map((item, i) => {
      const diplomas = item.diplomas?.map((diploma, j) => {
        return {
          ...diploma,
          uuid: diploma.url,
          name: diploma.fileName,
          fileUrl: diplomasUrls[i][j],
        }
      })
      const graduationCertificates = item.graduationCertificates?.map(
        (graduationCertificate, j) => {
          return {
            ...graduationCertificate,
            uuid: graduationCertificate.url,
            name: graduationCertificate.fileName,
            fileUrl: graduationCertificateUrls[i][j],
          }
        },
      )

      // 由于服务端选择了至今的情况下， timeGraduateIsNow 传1 ，但是timeGraduate 不返回。
      // 但是 组件需要处理 服务端返回至今的回显，所以在这里对数据做一层处理
      // TODO 采用拆分的方式处理 至今组件
      if (item.timeGraduateIsNow === TIME_GRADUATE.UP_TO_NOW) {
        // 员工类型不允许选择至今的，在初始化阶段直接将非法的毕业日期清空
        const employmentTypeCode = informationBlockService.formData.entryInfo?.employmentTypeCode // 员工类型
        const processTypeCode = informationBlockService.formData.processInfo?.processProgress // 处理进度
        if (
          employmentTypeCode !== EMPLOYEE_TYPE.INTERNSHIP &&
          employmentTypeCode !== EMPLOYEE_TYPE.STUDENT_PART_TIME &&
          // 待审核和待重审状态下，不合法的至今数据清空，其余状态保留
          (processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
            processTypeCode === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED)
        ) {
          item.timeGraduate = null
          item.timeGraduateIsNow = TIME_GRADUATE.NOT_UP_TO_NOW
        } else {
          item.timeGraduate = moment().valueOf()
        }
      }

      return {
        graduationCertificates: graduationCertificates || [],
        diplomas: diplomas || [],
        id: item.id,
        educationReports: item.educationReports || [],
        educationCode: item.educationCode,
        degreeCode: item.degreeCode,
        studyModeCode: item.studyModeCode,
        graduateSchool: item.graduateSchool,
        major: item.major,
        isDualDegree: item.isDualDegree,
        secondMajor: item.secondMajor,
        timeGraduateIsNow: item.timeGraduateIsNow,
        timeEnrollment: item.timeEnrollment ? moment(item.timeEnrollment) : null,
        timeExpectedGraduate: item.timeExpectedGraduate ? moment(item.timeExpectedGraduate) : null,
        timeGraduate: item.timeGraduate ? moment(item.timeGraduate) : null,
      }
    })
    form.setFieldsValue({
      educationExperiences: handleFormData,
    })
  }

  useMount(() => {
    setEducationFormFields()
  })

  // 完成
  const handleComplete = (values): void => {
    // 点击完成需要根据学历进行排序
    const sortedEducationExperiences = EduInfoService.getSortEducationExperience(
      values?.educationExperiences,
    )
    form.setFieldsValue({
      educationExperiences: sortedEducationExperiences,
    })
    completeItem('educationExperiences', sortedEducationExperiences)
  }

  // 取消
  const handleCancel = (): void => {
    setEducationFormFields()
  }

  //  给Item批量设置labelCol，wrapperCol
  const renderItems = (fields: string[]) => {
    return fields.map((field) => (
      <Item key={field} field={field} labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} />
    ))
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="教育经历"
        indexs="educationExperience"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <Item field="educationExperiences">
          {(fields: any) => {
            return (
              <>
                {fields.map((field: any, index: number) => {
                  return (
                    <div key={field.key}>
                      <Item {...field} wrapperCol={{ span: 24 }}>
                        <h3>{`教育经历${index + 1}`}</h3>
                        <FormItemGrid colCount={2} fillEmpty>
                          {renderItems([
                            'educationCode',
                            'degreeCode',
                            'studyModeCode',
                            'graduateSchool',
                            'major',
                            'isDualDegree',
                            'secondMajor',
                            'timeEnrollment',
                            'timeGraduate',
                            'timeExpectedGraduate',
                            'timeGraduateIsNow',
                            'isHighestEducation',
                          ])}
                        </FormItemGrid>
                        <Item
                          field="graduationCertificates"
                          wrapperCol={{ span: 19 }}
                          labelCol={{ span: 5 }}
                        />
                        <Item field="diplomas" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
                        <Item
                          field="educationReports"
                          wrapperCol={{ span: 19 }}
                          labelCol={{ span: 5 }}
                        />
                      </Item>
                    </div>
                  )
                })}
              </>
            )
          }}
        </Item>
      </EditCard>
    </ItemGroup>
  )
}
