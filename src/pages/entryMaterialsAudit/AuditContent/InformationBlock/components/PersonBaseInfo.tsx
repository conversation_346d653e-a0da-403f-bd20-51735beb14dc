import React, { useContext, useState } from 'react'
import { Item, ItemGroup, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import { useMount } from 'ahooks'
import type { SchemaType } from '@amazebird/schema-form'
import { FORM_MODE, WATCH_MODES } from '@/constants/review'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import dictService from '@/pages/entryMaterialsAudit/dictService'
import { MEMBER_DICT_VALUE } from '@/constants'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import { ObserverOptions, WATCH_LOADED_ASYNC_DICT } from '../../../config/config'
import EditCard from './EditCard'

export const schema: SchemaType = {
  name: {
    label: '姓名',
    component: 'Input.Text',
  },
  usedName: {
    label: '曾用名',
    component: Observer({
      watch: `#${WATCH_MODES.PERSON_BASE_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    placeholder: '请输入曾用名',
    min: 2,
    max: 20,
  },
  nationalityCode: {
    label: '国籍',
    component: 'Select.Text',
    options: ObserverOptions('BM_66'),
  },
  politicalStatusCode: {
    label: '政治面貌',
    component: Observer({
      watch: `#${WATCH_MODES.PERSON_BASE_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    placeholder: '请选择政治面貌',
    required: [true, '请选择政治面貌'],
    options: Observer({
      watch: `#${WATCH_MODES.PERSON_BASE_INFO_MODE}`,
      action: (mode) => {
        if (mode === FORM_MODE.MODIFY) {
          // 年龄大于28时，可选项中不包含共青团员
          const age = informationBlockService.formData.identityInfo?.age
          if (age >= 28) {
            return dictService
              .getDictOptions('BM_AT')
              .filter((item) => item.value !== MEMBER_DICT_VALUE)
          }
          return dictService.getDictOptions('BM_AT')
        }
        return dictService.getDictOptions('BM_AT')
      },
    }),
  },
  maritalStatusCode: {
    label: '婚育状况',
    component: 'Select.Text',
    options: ObserverOptions('BM_BG'),
  },
  contactPhone: {
    label: '联系电话',
    component: 'Input.Text',
  },
  address: {
    label: '现居住地',
    component: Observer({
      watch: `#${WATCH_MODES.PERSON_BASE_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'MyAddressEditComponent'
          default:
            return 'MyAddressDetailComponent'
        }
      },
    }),
    rules: [
      {
        validator: (_, value) => {
          if (value?.address && value?.detail) {
            return Promise.resolve()
          }
          return Promise.reject(new Error(value?.address ? '请填写详细地址' : '请选择居住省市'))
        },
      },
    ],
    options: Observer({
      watch: `#${WATCH_LOADED_ASYNC_DICT}`,
      action: (mode) => {
        if (mode instanceof Object) {
          return Object.values(mode)[0]
        }
        return dictService.getDictOptions('REGION_CODE')
      },
    }),
  },
  interestsOrSpecialties: {
    label: '兴趣专长',
    component: 'Input.Text',
  },
}

export default function PersonBaseInfo() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService
  // 是否点击完成，主要用于处理现居住地异步字典的问题
  const [isCompleteChangeAddress, setIsCompleteChangeAddress] = useState<boolean>(false)

  // 特殊处理年龄>28并且籍贯为共青团员的数据（清空）
  // 若处理进度=待审核、待重审，则进入材料审核页时若政治面貌不合法，自动清空，需要用户重新选择后才能提交成功；
  const validateMember = () => {
    if (businessAuthor.hasCanEditInfoAuth()) {
      const age = formData.identityInfo?.age
      if (age >= 28 && formData.identityInfo?.politicalStatusCode === MEMBER_DICT_VALUE) {
        form.setFieldsValue({
          politicalStatusCode: '',
        })
      }
    }
  }

  useMount(() => {
    form.setFieldsValue({
      name: formData.identityInfo?.name,
      usedName: formData.identityInfo?.usedName,
      nationalityCode: formData.identityInfo?.nationalityCode,
      politicalStatusCode: formData.identityInfo?.politicalStatusCode,
      maritalStatusCode: formData.identityInfo?.maritalStatusCode,
      contactPhone: formData.identityInfo?.contactPhone,
      address: {
        address: formData.identityInfo?.currentAddressName,
        detail: formData.identityInfo?.currentAddress,
      },
      interestsOrSpecialties: formData.identityInfo?.interestsOrSpecialties,
    })
    validateMember()
  })

  // 完成
  const handleComplete = (values): void => {
    let currentAddressObj = {}
    if (Array.isArray(values.address.address)) {
      setIsCompleteChangeAddress(true)
      currentAddressObj = {
        currentProvinceCode: values.address.address[0],
        currentCityCode: values.address.address[1],
        currentAreaCode: values.address.address[2],
        currentStreetCode: values.address.address[3],
      }
    }

    completeItem('identityInfo', {
      politicalStatusCode: values.politicalStatusCode,
      ...currentAddressObj,
      currentAddress: values.address.detail,
      usedName: values.usedName,
    })
  }

  // 取消
  const handleCancel = (): void => {
    const valueAddress = form.getFieldValue('address')
    form.setFieldsValue({
      politicalStatusCode: formData.identityInfo?.politicalStatusCode,
      address: {
        address:
          Array.isArray(valueAddress.address) && isCompleteChangeAddress
            ? [
                formData.identityInfo?.currentProvinceCode,
                formData.identityInfo?.currentCityCode,
                formData.identityInfo?.currentAreaCode,
                formData.identityInfo?.currentStreetCode,
              ]
            : formData.identityInfo?.currentAddressName,
        detail: formData.identityInfo?.currentAddress,
      },
      usedName: formData.identityInfo?.usedName,
    })
    validateMember()
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        indexs="personBaseInfo"
        title="个人基本信息"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <FormItemGrid colCount={2}>
          <Item field="name" />
          <Item field="usedName" />
          <Item field="nationalityCode" />
          <Item field="politicalStatusCode" />
          <Item field="maritalStatusCode" />
          <Item field="contactPhone" />
          <Item field="address" />
          <Item field="interestsOrSpecialties" />
        </FormItemGrid>
      </EditCard>
    </ItemGroup>
  )
}
