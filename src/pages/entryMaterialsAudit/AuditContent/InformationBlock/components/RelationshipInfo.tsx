import React, { useContext } from 'react'
import { Item, ItemGroup, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Card } from 'antd'
import { useMount } from 'ahooks'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { RANKDICT_OS } from '@/pages/entryMaterialsAudit/config/config'
import dictService from '../../../dictService'

const { getDictName } = dictService

export const schema: SchemaType = {
  emergencyContactName: {
    label: '紧急联系人',
    component: 'Input.Text',
  },
  emergencyContactPhone: {
    label: '紧急联系电话',
    component: 'Input.Text',
  },
  memberRelationCode: {
    label: '与本人关系',
    component: 'Input.Text',
  },
  relationshipInfos: {
    type: 'array',
    visible: Observer({
      watch: 'emergencyContactName',
      action: (_) => {
        // O/S序列隐藏除了紧急联系人以外的其他字段
        return !(
          informationBlockService?.formData?.entryInfo?.mainSequenceCode === RANKDICT_OS.S ||
          informationBlockService?.formData?.entryInfo?.mainSequenceCode === RANKDICT_OS.O
        )
      },
    }),
    item: {
      label: (({ index }) => {
        return `家庭成员${index + 1}`
      }) as any,
      type: 'object',
      fields: {
        memberName: {
          label: '姓名',
          component: 'Input.Text',
        },
        memberRelationCode: {
          label: '与本人关系',
          component: 'Input.Text',
        },
        contactPhone: {
          label: '联系电话',
          component: 'Input.Text',
        },
        workUnitAndPosition: {
          label: '工作单位与职位',
          component: 'Input.Text',
        },
      },
    },
  },
}

export default function RelationshipInfo() {
  const { form } = useContext(Context)
  const { formData } = informationBlockService

  useMount(() => {
    const mainRelationName = getDictName('BM_GX', formData.entryInfo?.memberRelationCode)

    form.setFieldsValue({
      emergencyContactName: formData.entryInfo?.emergencyContactName,
      emergencyContactPhone: formData.entryInfo?.emergencyContactPhone,
      memberRelationCode:
        mainRelationName === '其他'
          ? `${mainRelationName}(${formData.entryInfo.memberRelationRemark || ''})`
          : mainRelationName,
      relationshipInfos: formData.relationshipInfos.map((item) => {
        const relationName = getDictName('BM_GX', item.memberRelationCode)

        return {
          memberName: item.memberName,
          memberRelationCode:
            relationName === '其他'
              ? `${relationName}(${item.memberRelationRemark || ''})`
              : relationName,
          contactPhone: item.contactPhone,
          workUnitAndPosition: item.workUnitAndPosition,
        }
      }),
    })
  })
  return (
    <ItemGroup schema={schema}>
      <Card title="家庭成员信息">
        <FormItemGrid colCount={2}>
          <Item field="emergencyContactName" />
          <Item field="emergencyContactPhone" />
          <Item field="memberRelationCode" />
        </FormItemGrid>

        <Item field="relationshipInfos">
          {(fields) => {
            return (
              <>
                {fields.map((field) => {
                  const { key } = field
                  return (
                    <Item {...field} key={key} wrapperCol={{ span: 19 }} labelCol={{ span: 5 }}>
                      <FormItemGrid colCount={2}>
                        <Item field="memberName" />
                        <Item field="memberRelationCode" />
                        <Item field="contactPhone" />
                        <Item field="workUnitAndPosition" />
                      </FormItemGrid>
                    </Item>
                  )
                })}
              </>
            )
          }}
        </Item>
      </Card>
    </ItemGroup>
  )
}
