import React, { useContext } from 'react'
import { useMount } from 'ahooks'
import { Item, ItemGroup, Observer, FormItemGrid } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Card, DatePicker } from 'antd'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { WATCH_MODES, FORM_MODE } from '@/constants/review'
import moment from 'moment'
import AttachFile from '@/components/AttachFile'
import { TIME_GRADUATE } from '@/pages/entryMaterialsAudit/config/config'
import EditCard from './EditCard'
import { DatePickerWithUpToNow } from './DatePickerWithUpToNow'

export const schema: SchemaType = {
  leaveCertificates: {
    label: '离职证明',
    props: Observer({
      watch: `#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`,
      action: (mode) => {
        return {
          readOnly: mode !== FORM_MODE.MODIFY,
          dragSort: true,
          fileSizeLimit: 10,
          fileLengthLimit: 10,
          formatList: ['jpg', 'png', 'pdf', 'doc', 'docx'],
        }
      },
    }),
    component: AttachFile,
  },
  companyName: {
    label: '企业名称',
    component: Observer({
      watch: `#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    required: true,
    props: {
      maxLength: 25,
    },
  },
  positionName: {
    label: '所担任职位',
    component: Observer({
      watch: `#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    required: true,
    props: {
      maxLength: 25,
    },
  },
  timeBegin: {
    label: '开始日期',
    component: Observer({
      watch: `#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM')) || '--'}</div>
            }
        }
      },
    }),
    required: [true, '请选择开始日期'],
    props: Observer({
      watch: 'timeEnd',
      action: (timeEnd, formParams) => {
        return {
          onChange: (value) => {
            if (value?.isAfter(moment(timeEnd).startOf('month'))) {
              formParams.form.setFieldValue('timeEnd', null)
            }
          },
          disabledDate: (current) => {
            return current && current >= moment().endOf('month')
          },
          picker: 'month',
        }
      },
    }),
    rules: [
      {
        validator: (_, value) => {
          if (value && value >= moment().endOf('month')) {
            return Promise.reject(new Error(`仅允许选择${moment().format('YYYY-MM')}及之前的年月`))
          }
          return Promise.resolve()
        },
      },
    ],
  },
  timeEnd: {
    label: '结束日期',
    component: Observer({
      watch: [`#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`, 'timeEndIsNow'],
      action: ([mode, timeEndIsNow], formParams) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => (
              <DatePickerWithUpToNow
                {...props}
                format="YYYY-MM"
                onChange={({ value, isUpToNow }) => {
                  const $isUpToNow = isUpToNow
                    ? TIME_GRADUATE.UP_TO_NOW
                    : TIME_GRADUATE.NOT_UP_TO_NOW
                  formParams.form.setFieldValue('timeEndIsNow', $isUpToNow)
                  props.onChange(value)
                }}
                upToNow={timeEndIsNow === TIME_GRADUATE.UP_TO_NOW}
              />
            )
          default:
            return (props) => {
              if (timeEndIsNow === TIME_GRADUATE.UP_TO_NOW) {
                return '至今'
              }
              return <div>{(props.value && moment(props.value).format('YYYY-MM')) || '--'}</div>
            }
        }
      },
    }),
    props: Observer({
      watch: 'timeBegin',
      action: (timeBegin) => {
        const disabledDate = timeBegin
          ? (current) => current && current < moment(timeBegin).startOf('month')
          : undefined
        return {
          disabledDate,
          picker: 'month',
        }
      },
    }),
    rules: Observer({
      watch: 'timeBegin',
      action: (timeBegin) => {
        const rules: any = [
          {
            required: true,
            message: '请选择结束日期',
          },
        ]
        if (timeBegin) {
          rules.push({
            validator: (_, value) => {
              if (value && value < moment(timeBegin).startOf('month')) {
                return Promise.reject(new Error(`仅允许选择「开始日期」所在及之后的年月或“至今”`))
              }
              return Promise.resolve()
            },
          })
        }
        return rules
      },
    }),
  },
  timeEndIsNow: {
    label: '',
    component: () => <div />,
  },
  timeEndWork: {
    label: '最后工作结束日期',
    component: Observer({
      watch: `#${WATCH_MODES.RESIGNATION_CERTIFICATE_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    props: Observer({
      watch: ['timeBegin', 'timeEnd', 'timeEndIsNow'],
      action: ([timeBegin, timeEnd, timeEndIsNow]) => {
        return {
          disabledDate: (current) => {
            const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 预计报到日期
            const beginDay =
              timeEndIsNow === TIME_GRADUATE.UP_TO_NOW
                ? moment(timeBegin).startOf('month')
                : moment(timeEnd).startOf('month')
            return current.isBefore(beginDay, 'day') || current.isAfter(timeEntry, 'day')
          },
        }
      },
    }),
    rules: Observer({
      watch: ['timeBegin', 'timeEnd', 'timeEndIsNow'],
      action: ([timeBegin, timeEnd, timeEndIsNow], formParams) => {
        const beginValue =
          timeEndIsNow === TIME_GRADUATE.UP_TO_NOW
            ? formParams.form.getFieldValue('timeBegin')
            : formParams.form.getFieldValue('timeEnd')
        const rules: any = [
          {
            required: true,
            message: '请选择最后工作结束日期',
          },
        ]
        if (beginValue) {
          rules.push({
            validator: (_, value) => {
              const timeEntry = informationBlockService.formData.entryInfo?.timeEntry // 预计报到日期
              const beginDay = moment(beginValue).startOf('month')
              if (
                value &&
                (value.isAfter(timeEntry, 'day') || value.isBefore(beginDay, 'day')) &&
                timeEntry &&
                beginValue
              ) {
                return Promise.reject(
                  new Error(
                    `仅允许选择${beginDay.format('YYYY-MM-DD')}~${moment(timeEntry).format(
                      'YYYY-MM-DD',
                    )}之间的日期`,
                  ),
                )
              }
              return Promise.resolve()
            },
          })
        }
        return rules
      },
    }),
  },
  // 招聘工作经历主键ID,不处理该字段，直接返回给接口
  recruitWorkExperienceId: {
    label: '',
    component: () => <div />,
  },
}

export default function ResignationCertificate() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService

  const setWorkExperiencesFormFields = () => {
    // 接口返回数组，但是只有一段，临时处理
    const workExperiences = formData.workExperiences[0]

    // 由于服务端选择了至今的情况下， timeEndIsNow 传1 timeEnd 不返回。
    // 但是 组件需要处理 服务端返回至今的回显，所以在这里对数据做一层处理
    // TODO 采用拆分的方式处理 至今组件
    if (workExperiences.timeEndIsNow === TIME_GRADUATE.UP_TO_NOW) {
      workExperiences.timeEnd = moment().valueOf()
    }

    form.setFieldsValue({
      leaveCertificates: workExperiences?.leaveCertificates || [],
      companyName: workExperiences?.companyName,
      positionName: workExperiences?.positionName,
      timeBegin: workExperiences?.timeBegin ? moment(workExperiences.timeBegin) : null,
      timeEnd: workExperiences?.timeEnd ? moment(workExperiences.timeEnd) : null,
      timeEndIsNow: workExperiences?.timeEndIsNow,
      timeEndWork: workExperiences?.timeEndWork ? moment(workExperiences.timeEndWork) : null,
      recruitWorkExperienceId: workExperiences?.recruitWorkExperienceId,
    })
  }

  const handleCancel = () => {
    setWorkExperiencesFormFields()
  }

  const handleComplete = (values) => {
    completeItem('workExperiences', values)
  }

  useMount(() => {
    setWorkExperiencesFormFields()
  })

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="工作经历及证明"
        indexs="workExperiences"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <FormItemGrid colCount={2} fillEmpty>
          <Item field="companyName" />
          <Item field="positionName" />
          <Item field="timeBegin" />
          <Item field="timeEnd" />
          <Item field="timeEndWork" />
          <Item field="recruitWorkExperienceId" hidden />
          <Item field="timeEndIsNow" hidden />
        </FormItemGrid>
        <Item field="leaveCertificates" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
      </EditCard>
    </ItemGroup>
  )
}
