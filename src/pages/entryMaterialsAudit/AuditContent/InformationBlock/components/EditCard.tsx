import React, { useState, useContext, useEffect } from 'react'
import { Card, CardProps, Button, Space } from 'antd'
import { Layout } from '@amazebird/antd-schema-form'
import { omit } from 'lodash-es'
import { FORM_MODE, WATCH_MODES, WATCH_MODES_MAPS } from '@/constants/review'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import dmsService from '@/services/dms'
import reviewService from '@/services/preEntry/review'
import { MODIFY_MATERIALS } from '@/constants/dms/eventName'
import { reportLog } from '@galaxy/utils'
import subEvent from '@/pages/entryMaterialsAudit/utils/subEvent'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { ProcessProgress } from '@/services/preEntry/list'
import { useInitService } from '@/pages/entryMaterialsAudit/initContext'
import { getUrlParam } from '@/utils/utils'
import { getEducationExperienceValidateFields, validateFields } from '../../../config/config'

// 埋点参数； basic_info：个人基本信息；id_card_info：身份证信息；education_info：教育经历信息；health_info：健康相关信息；other_info：其他信息
enum ObmMaterialModule {
  'personBaseInfo' = 'basic_info',
  'identityInfo' = 'id_card_info',
  'educationExperience' = 'education_info',
  'healthInfo' = 'health_info',
  'otherInfo' = 'other_info',
}

interface EditCardProps extends CardProps {
  indexs:
    | 'personBaseInfo'
    | 'identityInfo'
    | 'educationExperience'
    | 'healthInfo'
    | 'otherInfo'
    | 'bankCardInfo'
    | 'workExperiences'
  onComplete?: (val) => void
  onCancel?: (cancelToOrigin?: boolean) => void
}

const EditCard: React.FC<EditCardProps> = (props) => {
  const { formData, completeItem, originFormData } = informationBlockService
  const { form } = useContext(Context)
  const [isShowEdit, setIsShowEdit] = useState<boolean>(true)
  const [cancelToOrigin, setCancelToOrigin] = useState(false) // 缓存数据 取消还原为正式初始数据
  const { hasCanEditInfoAuth } = businessAuthor
  const service = useInitService()

  /**
   * 点击修改信息
   * mode 为 modify 时，修改信息 mode 为 detail 时，查看信息
   * @returns void
   */
  const handleModeStatus = (mode: 'modify' | 'detail'): void => {
    setIsShowEdit(mode === 'detail')
    const MODE_STATUS = mode === 'modify' ? FORM_MODE.MODIFY : FORM_MODE.DETAIL
    switch (props.indexs) {
      case 'personBaseInfo':
        form.setState({ [WATCH_MODES.PERSON_BASE_INFO_MODE]: MODE_STATUS })
        break
      case 'identityInfo':
        form.setState({ [WATCH_MODES.IDENTITY_INFO_MODE]: MODE_STATUS })
        break
      case 'educationExperience':
        form.setState({ [WATCH_MODES.EDUCATION_EXPERIENCE_MODE]: MODE_STATUS })
        break
      case 'workExperiences':
        form.setState({ [WATCH_MODES.RESIGNATION_CERTIFICATE_MODE]: MODE_STATUS })
        break
      case 'healthInfo':
        form.setState({ [WATCH_MODES.HEALTH_INFO_MODE]: MODE_STATUS })
        break
      case 'bankCardInfo':
        form.setState({ [WATCH_MODES.BANK_INFO_FORM_MODE]: MODE_STATUS })
        break
      case 'otherInfo':
        form.setState({ [WATCH_MODES.OTHER_INFO_MODE]: MODE_STATUS })
        break
      default:
        break
    }
  }

  /**
   * 还原对应模块数据为正式数据
   */
  const resetModuleFormData = () => {
    const resetFiled = validateFields[props.indexs]
    const resetFiledValues: any = {}
    const { identityInfo, entryInfo, bankCardInfo } = originFormData
    const {
      usedName,
      currentProvinceCode,
      currentCityCode,
      currentAreaCode,
      currentStreetCode,
      politicalStatusCode,
      currentAddress,
      currentAddressName,
      houseRegisterTypeCode,
      idCardIssuingAuthority,
      timeIdCardStart,
      timeIdCardEnd,
      residenceAddress,
      idCardTypeCode,
      ethnicCode,
    } = identityInfo ?? {}

    const {
      timeHealthEnd,
      isDisabledCode,
      disabilityCertificateTypeCode,
      disabilityCertificateRankCode,
      isVeteranCode,
      disabilityCertPeriodCode,
      timeDisabilityCertificate,
      otherAttachments,
      timeCertificateProvide,
      timeValidPeriodEnd,
      healthMaterialsType,
    } = entryInfo ?? {}

    const { bankAccount, bankPlaceProvinceCode, bankPlaceCityCode, branchBankInfo } =
      bankCardInfo ?? {}

    switch (props.indexs) {
      case 'personBaseInfo':
        completeItem('identityInfo', {
          currentProvinceCode,
          currentCityCode,
          currentAreaCode,
          currentStreetCode,
          politicalStatusCode,
          currentAddress,
          currentAddressName,
          usedName,
        })
        break
      case 'identityInfo':
        completeItem('identityInfo', {
          houseRegisterTypeCode,
          idCardIssuingAuthority, // 空返回''，否则服务端不知道这个字段是否改动
          timeIdCardStart,
          timeIdCardEnd,
          residenceAddress,
          idCardTypeCode,
          ethnicCode,
        })
        break
      case 'educationExperience':
        resetFiled?.forEach((key) => {
          resetFiledValues[key] = originFormData?.educationExperiences
        })
        completeItem('educationExperiences', resetFiledValues?.educationExperiences)
        break
      case 'workExperiences':
        resetFiled?.forEach((key) => {
          // 工作经历只有一段，但是接口接收数组，临时处理
          resetFiledValues[key] = originFormData?.workExperiences[0]?.[key]
        })
        completeItem('workExperiences', resetFiledValues)
        break
      case 'healthInfo':
        completeItem('entryInfo', {
          timeHealthEnd,
          timeCertificateProvide,
          timeValidPeriodEnd,
          healthMaterialsType,
        })
        break
      case 'otherInfo':
        completeItem('entryInfo', {
          isDisabledCode,
          disabilityCertificateTypeCode,
          disabilityCertificateRankCode,
          isVeteranCode,
          disabilityCertPeriodCode,
          timeDisabilityCertificate,
          otherAttachments,
        })
        break
      case 'bankCardInfo':
        completeItem('bankCardInfo', {
          bankAccount,
          bankPlaceCityCode,
          bankPlaceProvinceCode,
          branchBankInfo,
        })
        break
      default:
        break
    }
  }

  /**
   * 点击完成
   * @param indexs
   * @returns void
   */
  const handleComplete = (): void => {
    // 教育经历多段特殊处理validateFields

    form
      .validateFields(
        props.indexs === 'educationExperience'
          ? getEducationExperienceValidateFields(formData.educationExperiences.length)
          : validateFields[props.indexs],
      )
      .then((values) => {
        props.onComplete && props.onComplete(values)
        handleModeStatus('detail')
      })
      .catch((err: any) => {
        setTimeout(() => {
          form.scrollToField(err.errorFields[0]?.name, { behavior: 'smooth', block: 'center' })
        }, 500)
      })
  }

  const handleDraft = async () => {
    if (
      hasCanEditInfoAuth() &&
      [ProcessProgress.Audit, ProcessProgress.PendingReview].includes(
        formData?.processInfo?.processProgress,
      )
    ) {
      const entryMaterialId = getUrlParam('id', window.location.href)
      const $formData = await reviewService.getEntryMaterialDetail(entryMaterialId, {
        noMessage: true,
      })
      reviewService.saveDraft({
        formInstance: form,
        formData,
        updateCallback: service.upLoad,
        updateFormData: $formData,
      })
    }
  }

  /**
   * 点击取消
   * @param indexs
   * @returns void
   */
  const handleCancel = (): void => {
    if (cancelToOrigin) {
      resetModuleFormData()
    }
    props.onCancel && props.onCancel()
    handleModeStatus('detail')
    setCancelToOrigin(false)
    handleDraft()
  }

  /**
   * 判断是否有修改信息的权限
   * @returns boolean
   */
  const isCanEdit = (): boolean => {
    if (hasCanEditInfoAuth() && isShowEdit) {
      return true
    }
    return false
  }

  useEffect(() => {
    const $draftState = reviewService.handleStateAndDraftData()
    const $key = WATCH_MODES_MAPS[props.indexs]
    if ($draftState && $draftState[$key] === FORM_MODE.MODIFY) {
      handleModeStatus('modify')
      setCancelToOrigin(true)
    }
  }, [])

  useEffect(() => {
    try {
      reportLog(`材料审核-${props.title}`, {
        contexts: {
          business: {
            canEdit: hasCanEditInfoAuth() ? 'true' : 'false',
          },
        },
      })
    } catch (error) {
      console.log(error)
    }
  }, [])

  useEffect(() => {
    subEvent.on(props.indexs, (cb) => {
      if (hasCanEditInfoAuth()) {
        cb?.(() => {
          handleModeStatus('modify')
        })
      }
    })
    return () => {
      subEvent.del(props.indexs)
    }
  }, [isShowEdit])

  return (
    <Card
      {...props}
      extra={
        isCanEdit() ? (
          <a
            onClick={() => {
              dmsService.trace(MODIFY_MATERIALS, {
                obmMaterialModule: ObmMaterialModule[props.indexs],
              })
              handleModeStatus('modify')
            }}
          >
            修改信息
          </a>
        ) : null
      }
    >
      {props.children}
      {!isShowEdit && (
        <Layout align="right">
          <Space size="middle">
            <Button
              onClick={() => {
                handleCancel()
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                handleComplete()
              }}
            >
              完成
            </Button>
          </Space>
        </Layout>
      )}
    </Card>
  )
}

export default EditCard
