import React, { useEffect, useState } from 'react'
import { DatePicker } from 'antd'
import moment from 'moment'

/**
 * @description: 自定义DatePicker组件，用于添加至今选项,至今使用当前日期(moment())来标识
 * @param {*} param1
 * @return {*}
 */
export const DatePickerWithUpToNow = ({
  format = 'YYYY-MM-DD',
  upToNow = false,
  value,
  onChange,
  ...restProps
}) => {
  const [dateValue, setDateValue] = useState(value)
  const [isUpToNow, setIsUpToNow] = useState(upToNow)
  const [open, setOpen] = useState(false)

  useEffect(() => {
    if (value === null && upToNow !== true) {
      setIsUpToNow(false) // Reset Up to Now if value is explicitly set to null from outside
    }
    setDateValue(value)
  }, [value])

  const handleUpToNow = () => {
    setIsUpToNow(true)
    const newValue = moment()
    setDateValue(newValue)
    onChange({ value: newValue, isUpToNow: true })
    setOpen(false)
  }

  const onValueChange = (newValue) => {
    setIsUpToNow(false)
    if (newValue === null) {
      setDateValue(null)
      onChange({ value: null, isUpToNow: false })
    } else {
      setDateValue(newValue)
      onChange({ value: newValue, isUpToNow: false })
    }
  }

  const onOpenChange = (newOpen) => {
    setOpen(newOpen)
  }

  return (
    <DatePicker
      value={isUpToNow && dateValue !== null ? moment() : dateValue}
      onChange={onValueChange}
      onOpenChange={onOpenChange}
      open={open}
      format={(val) => {
        if (isUpToNow) {
          return '至今'
        }
        return val.format(format)
      }}
      showToday={false}
      renderExtraFooter={() => <a onClick={handleUpToNow}>至今</a>}
      {...restProps}
    />
  )
}
