import React, { useContext } from 'react'
import { useMount } from 'ahooks'
import { Image, Radio, DatePicker, Input } from 'antd'
import type { SchemaType } from '@amazebird/schema-form'
import { Item, ItemGroup, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import dayjs from 'dayjs'
import moment from 'moment'
import { FORM_MODE, WATCH_MODES, PROCESS_PROGRESS_STATUS } from '@/constants/review'
import ossService from '@/pages/entryMaterialsAudit/ossService'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { ID_CARD_TYPE } from '@/constants'
import EditCard from './EditCard'
import { ObserverOptions } from '../../../config/config'

const { TextArea } = Input
const { RangePicker } = DatePicker

const isTempIdCardTimeLimit = {
  minStartTime: dayjs().subtract(3, 'month').startOf('day'),
  maxStartTime: dayjs().endOf('day'),
}

export const schema: SchemaType = {
  idCardFacePhotoUrl: {
    label: '人像面照',
    component: (props) => {
      return props.value ? <Image width={300} src={props.value} /> : <div>无人像照片</div>
    },
  },
  idCardEmblemPhotoUrl: {
    label: '非人像面照',
    component: (props) => {
      return props.value ? <Image width={300} src={props.value} /> : <div>无非人像照片</div>
    },
  },
  houseRegisterTypeCode: {
    label: '户籍类型',
    component: Observer({
      watch: `#${WATCH_MODES.IDENTITY_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <Radio.Group {...props} />
          default:
            return 'Select.Text'
        }
      },
    }),
    required: [true, '请选择户籍类型'],
    options: ObserverOptions('BM_HP'),
  },
  idCardTypeCode: {
    label: '证件类型',
    options: ObserverOptions('BM_ZJLX'),
    component: 'Select.Text',
    required: [true, '请选择证件类型'],
    placeholder: '请选择证件类型',
  },
  idCardNum: {
    label: '证件号码',
    component: 'Input.Text',
  },
  idCardIssuingAuthority: {
    label: '签发机关',
    component: Observer({
      watch: `#${WATCH_MODES.IDENTITY_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    placeholder: '请输入签发机关',
    rules: Observer({
      watch: 'idCardTypeCode',
      action: (idCardTypeCode) => {
        return [
          {
            required: ['01', '02'].includes(idCardTypeCode),
          },
          {
            max: 20,
            type: 'string',
          },
        ]
      },
    }),
  },
  timeIdCard: {
    label: '有效期限',
    component: Observer({
      watch: [`#${WATCH_MODES.IDENTITY_INFO_MODE}`, 'idCardTypeCode'],
      action: ([mode, idCardTypeCode]) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => {
              // 非临时身份证
              if (idCardTypeCode !== ID_CARD_TYPE.ICARD) {
                return <RangePicker {...props} />
              }
              // 临时身份证开始时间：仅支持选择【（当天-3个月+1d）～当天】的日期 （结束时间禁用）
              const disabledDate = (currentDate) => {
                return (
                  dayjs(currentDate).isBefore(isTempIdCardTimeLimit.minStartTime) ||
                  dayjs(currentDate).isAfter(isTempIdCardTimeLimit.maxStartTime)
                )
              }
              const [startTime1, endTime1] = props?.value || [null, null]
              // 临时身份证时 结束日期禁用（兼容处理，antd中结束日期禁用且没值时 组件时间选不了）
              const $endTime1 = endTime1 || dayjs().valueOf()
              return (
                <RangePicker
                  {...props}
                  value={[startTime1, moment($endTime1)]}
                  onChange={(date) => {
                    const [startTime] = date
                    const $endTime = startTime
                      ? dayjs(startTime.valueOf()).add(3, 'month').endOf('day').valueOf()
                      : dayjs().valueOf()
                    props.onChange([startTime, moment($endTime)])
                  }}
                  disabled={[false, true]}
                  disabledDate={disabledDate}
                />
              )
            }
          default:
            return (props) => {
              return (
                <div>
                  {props.value && props.value[0] && moment(props.value[0]).format('YYYY-MM-DD')}
                  {' ~ '}
                  {props.value && props.value[1] && moment(props.value[1]).format('YYYY-MM-DD')}
                </div>
              )
            }
        }
      },
    }),
    rules: Observer({
      watch: 'idCardTypeCode',
      action: (idCardTypeCode) => {
        // 临时身份证时 仅可选择 当天 ~ 当天+3个月-1天 之间的范围
        const baseRules = [
          {
            required: true,
            message: '请选择有效期限',
          },
          {
            validator: (rule, value, callback) => {
              if (!value || (value instanceof Array && (!value[0] || !value[1]))) {
                callback('请选择有效期限')
              }
              callback()
            },
          },
        ]
        return idCardTypeCode === ID_CARD_TYPE.ICARD
          ? baseRules.concat([
              {
                validator: (rule, value, callback) => {
                  const [startTime] = value
                  const $startTime = dayjs(startTime).startOf('day').valueOf()
                  const $maxStartTime = dayjs().endOf('day').valueOf()
                  const $minStartTime = dayjs().startOf('day').subtract(3, 'month').valueOf()
                  const $validateIdCardStartTime =
                    $startTime <= $maxStartTime && $startTime >= $minStartTime
                  // 当【证件类型】=临时身份证（02）时，开始时间：仅支持选择【（当天-3个月+1d）～当天】的日期；
                  if (!$validateIdCardStartTime) {
                    return callback(
                      `开始日期应在${dayjs($minStartTime).format('YYYY-MM-DD')}~${dayjs(
                        $maxStartTime,
                      ).format('YYYY-MM-DD')}`,
                    )
                  }
                  return callback()
                },
              },
            ])
          : baseRules
      },
    }),
    required: [true, '请选择有效期限'],
  },
  genderCode: {
    label: '性别',
    component: 'Select.Text',
    options: ObserverOptions('BM_AX'),
  },
  ethnicCode: {
    label: '民族',
    options: ObserverOptions('BM_AE'),
    component: Observer({
      watch: `#${WATCH_MODES.IDENTITY_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    required: [true, '请选择民族'],
    placeholder: '请选择民族',
  },
  timeBirth: {
    label: '出生日期',
    component: (props) => {
      return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
    },
  },
  residenceAddress: {
    label: '身份证地址',
    component: Observer({
      watch: `#${WATCH_MODES.IDENTITY_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <TextArea {...props} showCount maxLength={100} />
          default:
            return 'Input.Text'
        }
      },
    }),
    required: true,
    placeholder: '请输入身份证地址',
  },
}

export default function IdentityInfo() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService
  const { getOssSourceUrl } = ossService

  // 产品要求兼容旧数据 考虑到用户选择不了结束时间 故兼容
  const handleOldData = (timeIdCardStart, timeIdCardEnd) => {
    const $process = formData.processInfo.processProgress
    const $timeIdCardStart = dayjs(timeIdCardStart).startOf('day').valueOf()
    const $maxTimeIdCardStart = dayjs().endOf('day').valueOf()
    const $minTimeIdCardStart = dayjs().startOf('day').subtract(3, 'month').valueOf()
    const $validateIdCardStartTime =
      $timeIdCardStart <= $maxTimeIdCardStart && $timeIdCardStart >= $minTimeIdCardStart
    // 临时身份证 待审核或待重审状态 开始时间有合法值 更新结束时间数据
    if (
      formData.identityInfo?.idCardTypeCode === ID_CARD_TYPE.ICARD &&
      ($process === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
        $process === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED) &&
      $validateIdCardStartTime &&
      timeIdCardStart
    ) {
      return dayjs(timeIdCardStart).add(3, 'month').endOf('day').valueOf()
    }
    return timeIdCardEnd
  }

  const initData = () => {
    const idCardFacePhotoUrl = formData.identityInfo?.idCardFacePhoto?.url
    const idCardEmblemPhotoUrl = formData.identityInfo?.idCardEmblemPhoto?.url
    const temporaryFacePhotoUrl = formData.identityInfo?.temporaryFacePhoto?.url
    const temporaryEmblemPhotoUrl = formData.identityInfo?.temporaryEmblemPhoto?.url
    const otherFacePhotoUrl = formData.identityInfo?.otherDocumentFacePhoto?.url
    const otherEmblemPhotoUrl = formData.identityInfo?.otherDocumentEmblemPhoto?.url
    const idCardTypeCode = formData.identityInfo?.idCardTypeCode

    ;(async () => {
      let faceSourceUrl = ''
      if (idCardTypeCode === '01') {
        faceSourceUrl = idCardFacePhotoUrl
      } else if (idCardTypeCode === '02') {
        faceSourceUrl = temporaryFacePhotoUrl
      } else {
        faceSourceUrl = otherFacePhotoUrl
      }
      let emblemSourceUrl = ''
      if (idCardTypeCode === '01') {
        emblemSourceUrl = idCardEmblemPhotoUrl
      } else if (idCardTypeCode === '02') {
        emblemSourceUrl = temporaryEmblemPhotoUrl
      } else {
        emblemSourceUrl = otherEmblemPhotoUrl
      }
      const faceUrl = await getOssSourceUrl(faceSourceUrl)
      const emblemUrl = await getOssSourceUrl(emblemSourceUrl)
      form.setFieldsValue({
        idCardFacePhotoUrl: faceUrl || '',
        idCardEmblemPhotoUrl: emblemUrl || '',
      })
    })()

    // 兼容旧数据
    const $timeIdCardEnd = handleOldData(
      formData.identityInfo.timeIdCardStart,
      formData.identityInfo.timeIdCardEnd,
    )

    form.setFieldsValue({
      houseRegisterTypeCode: formData.identityInfo.houseRegisterTypeCode,
      idCardTypeCode: formData.identityInfo.idCardTypeCode,
      idCardNum: formData.identityInfo.idCardNum,
      idCardIssuingAuthority: formData.identityInfo.idCardIssuingAuthority || undefined,
      timeIdCard: [
        formData.identityInfo.timeIdCardStart
          ? moment(formData.identityInfo.timeIdCardStart)
          : null,
        $timeIdCardEnd ? moment($timeIdCardEnd) : null,
      ],
      genderCode: formData.identityInfo.genderCode,
      ethnicCode: formData.identityInfo.ethnicCode,
      timeBirth: formData.identityInfo.timeBirth,
      residenceAddress: formData.identityInfo.residenceAddress,
    })
  }

  useMount(() => {
    initData()
  })

  // 完成
  const handleComplete = (values): void => {
    completeItem('identityInfo', {
      houseRegisterTypeCode: values.houseRegisterTypeCode,
      idCardIssuingAuthority: values.idCardIssuingAuthority || '', // 空返回''，否则服务端不知道这个字段是否改动
      timeIdCardStart: values.timeIdCard[0],
      timeIdCardEnd: values.timeIdCard[1],
      residenceAddress: values.residenceAddress,
      idCardTypeCode: values.idCardTypeCode,
      ethnicCode: values.ethnicCode,
    })
    initData()
  }

  // 取消
  const handleCancel = (): void => {
    initData()
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="身份证信息"
        indexs="identityInfo"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <Item field="idCardFacePhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <Item field="idCardEmblemPhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <FormItemGrid colCount={2}>
          <Item field="houseRegisterTypeCode" />
          <Item field="idCardTypeCode" />
          <Item field="idCardNum" />
          <Item field="idCardIssuingAuthority" />
          <Item field="timeIdCard" />
          <Item field="genderCode" />
          <Item field="ethnicCode" />
          <Item field="timeBirth" />
          <Item field="residenceAddress" />
        </FormItemGrid>
      </EditCard>
    </ItemGroup>
  )
}
