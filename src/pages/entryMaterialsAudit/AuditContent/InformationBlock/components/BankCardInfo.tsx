import React, { useContext } from 'react'
import { useMount } from 'ahooks'
import { Item, ItemGroup, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import { FORM_MODE, WATCH_MODES } from '@/constants/review'
import type { SchemaType } from '@amazebird/schema-form'
import { Image } from 'antd'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import ossService from '@/pages/entryMaterialsAudit/ossService'
import EditCard from './EditCard'
import { ObserverOptions } from '../../../config/config'

export const schema: SchemaType = {
  bankCardNumPhotoUrl: {
    label: '卡号面',
    component: (props) => {
      return props.value ? <Image width={300} src={props.value} /> : <div>无卡号面</div>
    },
  },
  bankCardNoNumPhotoUrl: {
    label: '非卡号面',
    component: (props) => {
      return props.value ? <Image width={300} src={props.value} /> : <div>无非卡号面</div>
    },
  },
  bankCardTypeCode: {
    label: '银行卡类型',
    component: 'Select.Text',
    options: ObserverOptions('BM_Banktype'),
  },
  bankAccount: {
    label: '银行账号',
    component: Observer({
      watch: `#${WATCH_MODES.BANK_INFO_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'SeparatorInput'
          default:
            return 'SeparatorText'
        }
      },
    }),
    props: {
      // 20位卡号 + 4个空格
      maxLength: 24,
    },
    rules: Observer({
      watch: `#employmentFormCode`,
      action: (code) => (code === '5' ? [{ required: true }] : []),
    }),
  },
  bankPlaceCode: {
    label: '开户地',
    component: Observer({
      watch: `#${WATCH_MODES.BANK_INFO_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'OpenBankAddress'
          default:
            return 'MyCascaderDetailComponent'
        }
      },
    }),
    options: ObserverOptions('OMS_OPEN_ADDRESS'),
    rules: Observer({
      watch: `#employmentFormCode`,
      action: (code) => (code === '5' ? [{ required: true }] : []),
    }),
  },
  branchBankInfo: {
    label: '开户银行',
    component: Observer({
      watch: `#${WATCH_MODES.BANK_INFO_FORM_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Input.Edit'
          default:
            return 'Input.Text'
        }
      },
    }),
    rules: Observer({
      watch: `#employmentFormCode`,
      action: (code) => {
        const $rules: Record<string, any>[] = [
          {
            validator: (rule, value, callback) => {
              if (value && (value?.length < 2 || value?.length > 50)) {
                return Promise.reject(new Error('请输入2-50个字符'))
              }
              return Promise.resolve()
            },
          },
        ]
        if (code === '5') {
          $rules.unshift({ required: true })
        }
        return $rules
      },
    }),
  },
}

export default function BankCardInfo() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService
  const { getOssSourceUrl } = ossService

  useMount(() => {
    const bankCardNumPhotoUrl = formData.bankCardInfo?.bankCardNumPhoto?.url
    const bankCardNoNumPhotoUrl = formData.bankCardInfo?.bankCardNoNumPhoto?.url
    ;(async () => {
      const numUrl = await getOssSourceUrl(bankCardNumPhotoUrl)
      const noNumPUrl = await getOssSourceUrl(bankCardNoNumPhotoUrl)
      form.setFieldsValue({
        bankCardNumPhotoUrl: numUrl || '',
        bankCardNoNumPhotoUrl: noNumPUrl || '',
      })
    })()

    const bankPlaceCodeHasValue =
      formData.bankCardInfo?.bankPlaceProvinceCode || formData.bankCardInfo?.bankPlaceCityCode
    form.setFieldsValue({
      bankCardTypeCode: formData.bankCardInfo?.bankCardTypeCode,
      bankAccount: formData.bankCardInfo?.bankAccount,
      bankPlaceCode: bankPlaceCodeHasValue
        ? [formData.bankCardInfo?.bankPlaceProvinceCode, formData.bankCardInfo?.bankPlaceCityCode]
        : [],
      branchBankInfo: formData.bankCardInfo?.branchBankInfo,
    })

    form.setState({
      employmentFormCode: formData.entryInfo?.employmentFormCode,
    })
  })

  // 完成
  const handleComplete = (values): void => {
    completeItem('bankCardInfo', {
      bankAccount: values.bankAccount,
      bankPlaceCityCode: values?.bankPlaceCode?.[1] || '',
      bankPlaceProvinceCode: values?.bankPlaceCode?.[0] || '',
      branchBankInfo: values.branchBankInfo,
    })
  }

  // 取消
  const handleCancel = (): void => {
    form.setFieldsValue({
      bankAccount: formData.bankCardInfo?.bankAccount,
      bankPlaceCode: [
        formData.bankCardInfo?.bankPlaceProvinceCode,
        formData.bankCardInfo?.bankPlaceCityCode,
      ],
      branchBankInfo: formData.bankCardInfo?.branchBankInfo,
    })
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="银行账户信息"
        indexs="bankCardInfo"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <Item field="bankCardNumPhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <Item field="bankCardNoNumPhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <FormItemGrid colCount={2}>
          <Item field="bankCardTypeCode" />
          <Item field="bankAccount" />
          <Item field="bankPlaceCode" />
          <Item field="branchBankInfo" />
        </FormItemGrid>
      </EditCard>
    </ItemGroup>
  )
}
