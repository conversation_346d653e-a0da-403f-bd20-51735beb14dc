import React, { useContext } from 'react'
import { Item, ItemGroup, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Radio, DatePicker } from 'antd'
import { useMount } from 'ahooks'
import dayjs from 'dayjs'
import moment from 'moment'
import { FORM_MODE, WATCH_MODES, PROCESS_PROGRESS_STATUS } from '@/constants/review'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import { ObserverOptions, RANKDICT_OS } from '@/pages/entryMaterialsAudit/config/config'
import { isNil } from 'lodash-es'
import {
  ALL_HEALTH_MATERIALS_TYPE,
  DICT_BM_YN,
  HEALTH_MATERIALS_TYPE_O,
  HEALTH_MATERIALS_TYPE_OTHER,
  HEALTH_MATERIALS_TYPE_S,
} from '@/constants/index'
import EditCard from './EditCard'
import ImageGroup from './ImageGroup'

export const schema: SchemaType = {
  healthMaterialsAccessories: {
    label: '附件材料',
    component: (props) => {
      return <ImageGroup width={634} values={props.value} />
    },
    // 如果材料类型为「无」则不显示附件材料，但字段值仍然保留
    visible: Observer({
      watch: ['isHealthCertificateCode', 'healthMaterialsType'],
      action: ([isHealthCertificateCode, healthMaterialsType]) =>
        !isNil(isHealthCertificateCode) && healthMaterialsType !== -1,
    }),
  },
  healthMaterialsType: {
    label: '材料类型',
    component: Observer({
      watch: `#${WATCH_MODES.HEALTH_INFO_MODE}`,
      action: (mode) => {
        // 取接口原始的材料类型，判断是否为「无」，如果是「无」则不可编辑
        const originType = informationBlockService?.originFormData?.entryInfo?.healthMaterialsType
        if (Number(originType) !== -1 && mode === FORM_MODE.MODIFY) {
          return 'Select.Edit'
        }
        return 'Select.Text'
      },
    }),
    required: [true, '请选择材料类型'],
    options: Observer({
      watch: `#${WATCH_MODES.HEALTH_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            if (informationBlockService.formData.entryInfo?.mainSequenceCode === RANKDICT_OS.O) {
              return HEALTH_MATERIALS_TYPE_O
            }
            if (informationBlockService.formData.entryInfo?.mainSequenceCode === RANKDICT_OS.S) {
              return HEALTH_MATERIALS_TYPE_S
            }
            return HEALTH_MATERIALS_TYPE_OTHER
          default:
            return ALL_HEALTH_MATERIALS_TYPE
        }
      },
    }),
  },
  timeCertificateProvide: {
    label: '报告/证明出具日期',
    component: Observer({
      watch: `#${WATCH_MODES.HEALTH_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    visible: Observer({
      watch: 'healthMaterialsType',
      action: (type) => {
        // 为 谷丙转氨酶异常证明 与 哺乳期证明 时隐藏字段
        return [21, 22].includes(Number(type))
      },
    }),
    required: [true, '请选择报告/证明出具日期'],
    rules: [
      {
        validator: (rule, value, callback) => {
          // 仅可选择 当天 ~ 当天+365d 之间的时间
          if (value && value > dayjs().endOf('day')) {
            return Promise.reject(new Error('出具日期应在今天之前'))
          }
          return Promise.resolve()
        },
      },
    ],
    props: {
      disabledDate: (currentDate) => dayjs(currentDate).isAfter(dayjs().endOf('day')),
    },
  },
  timeValidPeriodEnd: {
    label: '有效期结束日期',
    component: Observer({
      watch: `#${WATCH_MODES.HEALTH_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    visible: Observer({
      watch: 'healthMaterialsType',
      action: (type) => {
        // 为 谷丙转氨酶异常证明 与 哺乳期证明 时隐藏字段
        return [21, 22].includes(Number(type))
      },
    }),
    required: [true, '请选择有效期结束日期'],
    rules: [
      {
        validator: (rule, value, callback) => {
          const startTime = dayjs().startOf('day')
          const endTime = dayjs().add(365, 'day').endOf('day')
          // 仅可选择 当天 ~ 当天+365d 之间的时间
          if (value && (value < startTime || value > endTime)) {
            return Promise.reject(
              new Error(
                `结束日期应在${dayjs(startTime).format('YYYY-MM-DD')}到${dayjs(endTime).format(
                  'YYYY-MM-DD',
                )}`,
              ),
            )
          }
          return Promise.resolve()
        },
      },
    ],
    props: {
      disabledDate: (currentDate) => {
        // 仅可选择 当天 至 当天+365d 之间
        const currentDay = dayjs().startOf('day')
        const lastDay = dayjs().add(365, 'day').endOf('day')
        return dayjs(currentDate).isBefore(currentDay) || dayjs(currentDate).isAfter(lastDay)
      },
    },
  },
  isHealthCertificateCode: {
    label: '是否持有健康证',
    component: 'Select.Text',
    value: Observer({
      watch: 'healthMaterialsType',
      action: (healthMaterialsType) => {
        if ([13, 14].includes(Number(healthMaterialsType))) {
          return DICT_BM_YN.YES
        }
        return DICT_BM_YN.NO
      },
    }),
    options: ObserverOptions('BM_YN'),
  },
  timeHealthEnd: {
    label: '健康证到期日期',
    component: Observer({
      watch: `#${WATCH_MODES.HEALTH_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => <DatePicker {...props} />
          default:
            return (props) => {
              return <div>{(props.value && moment(props.value).format('YYYY-MM-DD')) || '--'}</div>
            }
        }
      },
    }),
    required: [true, '请选择健康证到期日期'],
    visible: Observer({
      watch: `isHealthCertificateCode`,
      action: (isHealthCertificateCode) => {
        return isHealthCertificateCode === '1'
      },
    }),
    rules: [
      {
        validator: (rule, value, callback) => {
          const currentDay = dayjs().startOf('day').valueOf()
          const lastDay = dayjs().add(365, 'day').endOf('day').valueOf()
          // 仅可选择 当天 ~ 当天+365d 之间的时间
          if (value < currentDay || value > lastDay) {
            return Promise.reject(
              new Error(
                `到期日期应在${dayjs(currentDay).format('YYYY-MM-DD')}到${dayjs(lastDay).format(
                  'YYYY-MM-DD',
                )}`,
              ),
            )
          }
          return Promise.resolve()
        },
      },
    ],
    props: {
      disabledDate: (currentDate) => {
        // 仅可选择（报到日期+1d）至（报到日期+365d）之间
        const currentDay = dayjs().startOf('day')
        const lastDay = dayjs().add(365, 'day').endOf('day')
        return dayjs(currentDate).isBefore(currentDay) || dayjs(currentDate).isAfter(lastDay)
      },
    },
  },
}

export default function HealthInfo() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService

  useMount(() => {
    const $process = formData.processInfo.processProgress
    let $isHealthCertificate = formData.entryInfo?.isHealthCertificateCode
    // 待审核或待重审状态 更新本字段赋值
    if (
      $process === PROCESS_PROGRESS_STATUS.TO_BE_AUDITED ||
      $process === PROCESS_PROGRESS_STATUS.TO_BE_READDICTED
    ) {
      // 当材料类型选择为 「健康证」、「体检报告与健康证」时，系统赋值「是」
      if ([13, 14].includes(Number(formData.entryInfo?.healthMaterialsType))) {
        $isHealthCertificate = DICT_BM_YN.YES
      } else {
        $isHealthCertificate = DICT_BM_YN.NO
      }
    }
    form.setFieldsValue({
      healthMaterialsAccessories: formData.entryInfo?.healthMaterialsAccessories || [],
      healthMaterialsType: formData.entryInfo?.healthMaterialsType,
      isHealthCertificateCode: $isHealthCertificate,
      timeHealthEnd: formData.entryInfo?.timeHealthEnd
        ? moment(formData.entryInfo?.timeHealthEnd)
        : null,
      timeCertificateProvide: formData.entryInfo?.timeCertificateProvide
        ? moment(formData.entryInfo?.timeCertificateProvide)
        : null,
      timeValidPeriodEnd: formData.entryInfo?.timeValidPeriodEnd
        ? moment(formData.entryInfo?.timeValidPeriodEnd)
        : null,
    })
  })

  // 完成
  const handleComplete = (values): void => {
    completeItem('entryInfo', {
      timeHealthEnd: values.timeHealthEnd ? moment(values.timeHealthEnd) : null,
      timeCertificateProvide: values.timeCertificateProvide
        ? moment(values.timeCertificateProvide)
        : null,
      timeValidPeriodEnd: values.timeValidPeriodEnd ? moment(values.timeValidPeriodEnd) : null,
      healthMaterialsType: formData.entryInfo?.healthMaterialsType,
    })
  }

  // 取消
  const handleCancel = (): void => {
    form.setFieldsValue({
      timeHealthEnd: formData.entryInfo?.timeHealthEnd
        ? moment(formData.entryInfo?.timeHealthEnd)
        : null,
      timeCertificateProvide: formData.entryInfo?.timeCertificateProvide
        ? moment(formData.entryInfo?.timeCertificateProvide)
        : null,
      timeValidPeriodEnd: formData.entryInfo?.timeValidPeriodEnd
        ? moment(formData.entryInfo?.timeValidPeriodEnd)
        : null,
      healthMaterialsType: formData.entryInfo?.healthMaterialsType,
    })
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="健康相关信息"
        indexs="healthInfo"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <Item field="healthMaterialsAccessories" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <Item field="healthMaterialsType" />
          <Item field="timeCertificateProvide" />
          <Item field="timeValidPeriodEnd" />
          <Item field="isHealthCertificateCode" />
          <Item field="timeHealthEnd" />
        </div>
      </EditCard>
    </ItemGroup>
  )
}
