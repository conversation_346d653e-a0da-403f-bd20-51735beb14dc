import React, { useContext } from 'react'
import { Item, ItemGroup, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/schema-form'
import { Radio, Tooltip } from 'antd'
import { useMount } from 'ahooks'
import { FORM_MODE, WATCH_MODES } from '@/constants/review'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import informationBlockService from '@/pages/entryMaterialsAudit/AuditContent/InformationBlock/service'
import styles from '@/pages/entryMaterialsAudit/AuditContent/style.module.less'
import dayjs from 'dayjs'
import AttachFile from '@/components/AttachFile'
import EditCard from './EditCard'
import ImageGroup from './ImageGroup'
import { ObserverOptions, DISABILITY_CERTIFICATE, RANKDICT_OS } from '../../../config/config'

export const schema: SchemaType = {
  companyAccumulationFund: {
    label: '原公司公积金账号',
    component: 'Input.Text',
    visible: Observer({
      watch: 'isExistInterestConflictCode',
      action: (_) => {
        // O/S序列隐藏原公司公积金账号字段
        return !(
          informationBlockService?.formData?.entryInfo?.mainSequenceCode === RANKDICT_OS.S ||
          informationBlockService?.formData?.entryInfo?.mainSequenceCode === RANKDICT_OS.O
        )
      },
    }),
  },
  isExistInterestConflictCode: {
    label: '是否存在利益关系冲突',
    component: 'Select.Text',
    options: ObserverOptions('BM_YN'),
  },
  isDisabledCode: {
    label: '是否残障人士',
    component: Observer({
      watch: [`#${WATCH_MODES.OTHER_INFO_MODE}`, 'disabilityCertificatePhotoUrl', '#dataSource'],
      action: ([mode, url, dataSource]) => {
        // 来源是dhr录用，则在录用侧赋默认值后禁用不可修改
        if (dataSource === 'employment') {
          return 'Select.Text'
        }

        switch (mode) {
          case FORM_MODE.MODIFY:
            return (props) => (
              <Tooltip
                title={!url?.length ? '候选人未提交残疾证附件，如有需要，请驳回给候选人修改' : ''}
                placement="topLeft"
              >
                <Radio.Group {...props} />
              </Tooltip>
            )
          default:
            return 'Select.Text'
        }
      },
    }),
    required: [true, '请选择是否残障人士'],
    options: ObserverOptions('BM_YN'),
    disabled: Observer({
      watch: 'disabilityCertificatePhotoUrl',
      action: (mode) => !mode?.length,
    }),
  },
  disabilityCertificateTypeCode: {
    label: '残疾证件类型',
    component: Observer({
      watch: `#${WATCH_MODES.OTHER_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    placeholder: '请选择残疾证件类型',
    required: [true, '请选择残疾证件类型'],
    options: ObserverOptions('BM_CZZM'),
    visible: Observer({
      watch: 'isDisabledCode',
      action: (isDisabledCode) => isDisabledCode === '1',
    }),
  },
  disabilityCertificateRankCode: {
    label: '残疾人证等级',
    component: Observer({
      watch: `#${WATCH_MODES.OTHER_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    placeholder: '请选择残疾人证等级',
    required: [true, '请选择残疾人证等级'],
    options: ObserverOptions('BM_CJRZDJ'),
    visible: Observer({
      watch: ['disabilityCertificateTypeCode', 'isDisabledCode'],
      action: (disabilityCertificateTypeCode) =>
        disabilityCertificateTypeCode[0] === DISABILITY_CERTIFICATE &&
        disabilityCertificateTypeCode[1] === '1',
    }),
  },
  disabilityCertPeriodCode: {
    label: '证件有效期',
    placeholder: '请选择证件有效期',
    component: Observer({
      watch: `#${WATCH_MODES.OTHER_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'Select.Edit'
          default:
            return 'Select.Text'
        }
      },
    }),
    options: ObserverOptions('BM_CJZYXQ'),
    required: [true, '请选择证件有效期'],
    visible: Observer({
      watch: 'isDisabledCode',
      action: (isDisabledCode) => isDisabledCode === '1',
    }),
  },
  timeDisabilityCertificate: {
    label: '证件签发时间',
    component: Observer({
      watch: `#${WATCH_MODES.OTHER_INFO_MODE}`,
      action: (mode) => {
        switch (mode) {
          case FORM_MODE.MODIFY:
            return 'DatePicker.Edit'
          default:
            return 'DatePicker.Text'
        }
      },
    }),
    props: {
      picker: 'date',
      disabledDate: (value) => {
        return dayjs(value).isAfter(dayjs(), 'day')
      },
    },
    required: [true, '请选择证件签发时间'],
    rules: [
      {
        validator: (rule, value, callback) => {
          if (dayjs(value).isAfter(dayjs(), 'day')) {
            callback(`证件签发时间不可选择未来时间`)
          }
          callback()
        },
      },
    ],
    visible: Observer({
      watch: 'isDisabledCode',
      action: (isDisabledCode) => isDisabledCode === '1',
    }),
  },
  disabilityCertificatePhotoUrl: {
    label: '残疾证',
    component: (props) => {
      return <ImageGroup width={634} values={props.value} />
    },
    visible: Observer({
      watch: 'isDisabledCode',
      action: (isDisabledCode) => isDisabledCode === '1',
    }),
  },
  isVeteranCode: {
    label: '是否退伍军人',
    component: Observer({
      watch: [`#${WATCH_MODES.OTHER_INFO_MODE}`, 'veteranCertificatePhotoUrl'],
      action: (mode) => {
        switch (mode[0]) {
          case FORM_MODE.MODIFY:
            return (props) => (
              <Tooltip
                title={
                  !mode[1]?.length ? '候选人未提交退伍证附件，如有需要，请驳回给候选人修改' : ''
                }
                placement="topLeft"
              >
                <Radio.Group {...props} />
              </Tooltip>
            )
          default:
            return 'Select.Text'
        }
      },
    }),
    required: [true, '请选择是否退伍军人'],
    options: ObserverOptions('BM_YN'),
    disabled: Observer({
      watch: 'veteranCertificatePhotoUrl',
      action: (mode) => !mode?.length,
    }),
  },
  veteranCertificatePhotoUrl: {
    label: '退伍证',
    component: (props) => {
      return <ImageGroup width={634} values={props.value} />
    },
    visible: Observer({
      watch: 'isVeteranCode',
      action: (isVeteranCode) => isVeteranCode === '1',
    }),
  },
  otherAttachments: {
    label: '其他附件',
    props: Observer({
      watch: `#${WATCH_MODES.OTHER_INFO_MODE}`,
      action: (mode) => {
        return {
          readOnly: mode !== FORM_MODE.MODIFY,
          dragSort: true,
          fileSizeLimit: 10,
          fileLengthLimit: 10,
          formatList: ['jpg', 'png', 'pdf', 'doc', 'docx'],
        }
      },
    }),
    component: AttachFile,
  },
}

export default function OtherInfo() {
  const { form } = useContext(Context)
  const { formData, completeItem } = informationBlockService

  const initViewFields = async () => {
    form.setFieldsValue({
      disabilityCertificatePhotoUrl: formData.entryInfo?.disabilityCertificatePhotos || [],
      veteranCertificatePhotoUrl: formData.entryInfo?.veteranCertificatePhotos || [],
      companyAccumulationFund: formData.entryInfo?.companyAccumulationFund || '--',
      isExistInterestConflictCode: formData.entryInfo?.isExistInterestConflictCode,
      otherAttachments: formData.entryInfo?.otherAttachments,
    })
  }

  const initEditFields = () => {
    // 可编辑字段不可传入''，否则placeholder不显示，这边根据字段的组件类型做空处理
    form.setFieldsValue({
      isDisabledCode: formData.entryInfo?.isDisabledCode,
      disabilityCertificateTypeCode: formData.entryInfo?.disabilityCertificateTypeCode || undefined,
      disabilityCertificateRankCode: formData.entryInfo?.disabilityCertificateRankCode || undefined,
      isVeteranCode: formData.entryInfo?.isVeteranCode,
      disabilityCertPeriodCode: formData.entryInfo?.disabilityCertPeriodCode || undefined,
      timeDisabilityCertificate: formData.entryInfo?.timeDisabilityCertificate || undefined, // 避免服务端0传入
      otherAttachments: formData.entryInfo?.otherAttachments || [],
    })

    form.setState({
      dataSource: formData.entryInfo?.dataSource,
    })
  }

  useMount(() => {
    initViewFields()
    initEditFields()
  })

  // 完成
  const handleComplete = (values): void => {
    completeItem('entryInfo', {
      isDisabledCode: values.isDisabledCode,
      disabilityCertificateTypeCode: values.disabilityCertificateTypeCode,
      disabilityCertificateRankCode: values.disabilityCertificateRankCode,
      isVeteranCode: values.isVeteranCode,
      disabilityCertPeriodCode: values.disabilityCertPeriodCode,
      timeDisabilityCertificate: values?.timeDisabilityCertificate?.valueOf?.(),
      otherAttachments: values.otherAttachments,
    })
  }

  // 取消
  const handleCancel = (): void => {
    initEditFields()
  }

  return (
    <ItemGroup schema={schema}>
      <EditCard
        title="其他信息"
        indexs="otherInfo"
        onComplete={handleComplete}
        onCancel={handleCancel}
      >
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <Item field="companyAccumulationFund" />
          <Item field="isExistInterestConflictCode" className={styles.is_exist_interest} />
          <Item field="isDisabledCode" />
          <Item field="disabilityCertificateTypeCode" />
          <Item field="disabilityCertificateRankCode" />
          <Item field="disabilityCertPeriodCode" />
          <Item field="timeDisabilityCertificate" />
        </div>
        <Item
          field="disabilityCertificatePhotoUrl"
          wrapperCol={{ span: 19 }}
          labelCol={{ span: 5 }}
        />
        <Item field="isVeteranCode" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <Item field="veteranCertificatePhotoUrl" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
        <Item field="otherAttachments" wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} />
      </EditCard>
    </ItemGroup>
  )
}
