/**
 * @name 现居住地的编辑态度和展示态组件封装
 */
import React, { useState, useRef, useContext } from 'react'
import { Cascader, Input } from 'antd'
import { cloneDeep } from 'lodash-es'
import { useMount, useUpdate, useDeepCompareEffect } from 'ahooks'
import dictService from '@/pages/entryMaterialsAudit/dictService'
import Context from '@/pages/entryMaterialsAudit/AuditContent/Context'
import { WATCH_LOADED_ASYNC_DICT } from '@/pages/entryMaterialsAudit/config/config'

interface Option {
  value: string
  label: string
  children?: Option[]
  isLeaf?: boolean
  loading: boolean
  loadId?: number
}

interface MyAddressComponentProps {
  options: Option[]
  value: {
    address: any[]
    detail: string
  }
  onChange: (value: any) => void
  id: string
}

const MyAddressComponent: React.FC<MyAddressComponentProps> = (props) => {
  const { options, value, onChange, id } = props
  const { form } = useContext(Context)
  const { getAsyncDict } = dictService
  const [addressValue, setAddressValue] = useState<any[]>([])
  const [detailValue, setDetailValue] = useState<string>('')
  const [myOptions, setMyOptions] = useState<Option[]>([])

  useMount(() => {
    const tmpOptions = options.map((item) => {
      return {
        ...item,
        loading: false,
        isLeaf: item.isLeaf || false,
      }
    })
    setMyOptions(tmpOptions)
    setAddressValue(value?.address || [])
    setDetailValue(value?.detail || '')
  })

  /**
   * 级联选择器改变
   * @param value
   * return void
   */
  const cascaderChange = (val): void => {
    setAddressValue(val)
    onChange?.({
      address: val,
      detail: detailValue,
    })
  }

  /**
   * 文本域改变
   * @param e
   * return void
   */
  const textAreaChange = (e): void => {
    setDetailValue(e.target.value)
    onChange?.({
      address: addressValue,
      detail: e.target.value,
    })
  }

  /**
   * 级联选择器加载数据
   * @param selectedOptions  当前选中的options
   * return void
   */
  const loadData = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]
    targetOption.loading = true
    const loadDicts = await getAsyncDict('REGION_CODE', 'GLOBAL', {
      parentId: targetOption.loadId,
    })
    targetOption.loading = false
    targetOption.children = loadDicts.map((item) => {
      return {
        value: item.code,
        label: item.name,
        isLeaf: item.isLeaf || false,
        loading: false,
        loadId: item.id,
      }
    })

    setMyOptions([...myOptions])
    // TODO 比较蠢的做法，后续优化
    // 目前schemaForm 监听不到数组的变化。
    const obj = {} as any
    obj[`${new Date().toLocaleString()}`] = [...myOptions]
    form.setState({ [WATCH_LOADED_ASYNC_DICT]: obj })
  }

  return (
    <div id={id}>
      <Cascader
        value={addressValue}
        onChange={cascaderChange}
        options={myOptions}
        style={{ marginBottom: 16 }}
        placeholder="请选择省市"
        allowClear={false}
        loadData={loadData}
      />
      <Input.TextArea
        value={detailValue}
        showCount
        maxLength={100}
        onChange={textAreaChange}
        placeholder="请填写详细地址"
      />
    </div>
  )
}

const MyAddressDetailComponent: React.FC<MyAddressComponentProps> = (props) => {
  const { options, value, onChange, id } = props
  const labelRef = useRef<string[]>([])
  const update = useUpdate()

  /**
   * 获取级联选择器的label
   * @param arr options
   * @param val value
   * return void
   */
  const getLabel = (arr: Option[], val: any[]): void => {
    arr.forEach((item) => {
      if (item.value === val[0]) {
        labelRef.current?.push(item.label)
        if (item.children && val.length > 1) {
          getLabel(item.children, val.slice(1))
        }
      }
    })
  }

  useDeepCompareEffect(() => {
    if (Object.prototype.toString.call(value?.address) === '[object String]') {
      labelRef.current = value?.address
    } else {
      getLabel(options, value?.address || [])
    }
    update()
  }, [value, options])

  return (
    <div id={id}>
      {Array.isArray(labelRef.current) ? labelRef.current?.join('/') : labelRef.current || '-'}
      {value?.detail || '-'}
    </div>
  )
}

export { MyAddressComponent, MyAddressDetailComponent }
