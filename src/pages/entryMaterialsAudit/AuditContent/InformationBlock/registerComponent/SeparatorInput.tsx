import React, { useState } from 'react'
import { Input } from 'antd'

interface IProps {
  split?: number
  value: string
  onChange: (value: string) => void
}

const formatNumberSpacing = (string: string | number, split) => {
  const formatReg = new RegExp(`(.{${split}})`, 'g')
  if (string) {
    return `${string}`.replace(/\D/g, '').replace(formatReg, '$1 ').replace(/\s?$/g, '')
  }
  return ''
}

const SeparatorInput = ({ onChange, value: initValue, split = 4, ...props }: IProps) => {
  const [value, setValue] = useState(formatNumberSpacing(initValue, split))

  const handleInputChange = (e) => {
    const inputValue = e.target.value.replace(/\s/g, '')
    const formattedValue = formatNumberSpacing(inputValue, split)
    setValue(formattedValue)
    onChange?.(inputValue)
  }

  return <Input value={value} onChange={handleInputChange} {...props} />
}

interface SeparatorTextProps {
  split?: number
  value: string
}
const SeparatorText = ({ value, split = 4 }: SeparatorTextProps) => {
  return <text>{formatNumberSpacing(value, split)}</text>
}

export { SeparatorInput, SeparatorText }
