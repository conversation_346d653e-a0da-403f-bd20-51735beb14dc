import React, { useRef } from 'react'
import { Cascader, Input } from 'antd'
import { useDeepCompareEffect, useUpdate } from 'ahooks'

interface Option {
  value: string
  label: string
  children?: Option[]
}

interface MyCascaderDetailComponentProps {
  options: Option[]
  value: any[]
  onChange: (value: any) => void
}

const MyCascaderDetailComponent: React.FC<MyCascaderDetailComponentProps> = (props) => {
  const { options, value, onChange } = props
  const labelRef = useRef<string[]>([])
  const update = useUpdate()

  const getLabel = (arr: Option[], val: any[]): void => {
    arr.forEach((item) => {
      if (item.value === val[0]) {
        labelRef.current?.push(item.label)
        if (item.children && val.length > 1) {
          getLabel(item.children, val.slice(1))
        }
      }
    })
  }

  useDeepCompareEffect(() => {
    getLabel(options, value || [])
    update()
  }, [options, value])

  return <div>{labelRef.current?.join('/') || '--'}</div>
}

export { MyCascaderDetailComponent }
