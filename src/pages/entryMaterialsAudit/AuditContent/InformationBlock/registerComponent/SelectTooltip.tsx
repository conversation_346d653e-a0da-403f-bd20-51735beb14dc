/**
 * 选择框，鼠标悬浮显示完整内容
 */
import React from 'react'
import { Select, Tooltip } from 'antd'

const { Option } = Select

interface SelectTooltipProps {
  options: any[]
  noOptionsTip: string // 无选项时的提示
}

const SelectTooltip: React.FC<SelectTooltipProps> = (props) => {
  const { options, noOptionsTip, ...restProps } = props

  return (
    <Tooltip title={options.length === 0 && noOptionsTip ? noOptionsTip : ''} placement="topLeft">
      <Select {...restProps} disabled={noOptionsTip ? options.length === 0 : false}>
        {props.options.map((item) => (
          <Option key={item.value} value={item.value}>
            <Tooltip placement="topLeft" title={item.label}>
              {item.label}
            </Tooltip>
          </Option>
        ))}
      </Select>
    </Tooltip>
  )
}

export default SelectTooltip
