import { registerComponent } from '@amazebird/antd-schema-form'
import { MyAddressComponent, MyAddressDetailComponent } from './MyAddressComponent'
import { MyCascaderDetailComponent } from './MyCascaderDetailComponent'
import { SeparatorInput, SeparatorText } from './SeparatorInput'
import SelectTooltip from './SelectTooltip'
import { OpenBankAddress } from './OpenBankAddress'

registerComponent('MyAddressEditComponent', MyAddressComponent)
registerComponent('SeparatorInput', SeparatorInput)
registerComponent('SeparatorText', SeparatorText)
registerComponent('MyAddressDetailComponent', MyAddressDetailComponent)
registerComponent('OpenBankAddress', OpenBankAddress)
registerComponent('MyCascaderDetailComponent', MyCascaderDetailComponent)
registerComponent('SelectTooltip', SelectTooltip)
