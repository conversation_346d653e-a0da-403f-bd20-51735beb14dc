/**
 * @name 开户银行编辑态组件封装
 * 开户银行最多只选择两层，限制为两层
 * 格式为：省 + 市辖区/县
 */
import React, { useState, useContext } from 'react'
import { Cascader, CascaderProps } from 'antd'
import { useMount } from 'ahooks'

const OpenBankAddress: React.FC<CascaderProps> = (props) => {
  const { options } = props
  const [myOptions, setMyOptions] = useState<CascaderProps['options']>([])

  useMount(() => {
    const tmpOptions = options.map((item) => {
      if (item.children) {
        item.children?.forEach((ele) => {
          // 将第三层置空
          if (ele?.children) {
            // eslint-disable-next-line no-param-reassign
            ele.children = null
          }
        })
      }
      return item
    })
    setMyOptions(tmpOptions)
  })

  return (
    <Cascader
      {...props}
      options={myOptions}
      style={{ marginBottom: 16 }}
      placeholder="请选择省市"
    />
  )
}

export { OpenBankAddress }
