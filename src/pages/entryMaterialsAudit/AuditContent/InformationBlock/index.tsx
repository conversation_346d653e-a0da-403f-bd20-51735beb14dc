import PersonPhoto, { schema as personPhotoSchema } from './components/PersonPhoto'
import WorkCardPhoto, { schema as workCardPhotoSchema } from './components/WorkCardPhoto'
import PersonBaseInfo, { schema as personBaseInfoSchema } from './components/PersonBaseInfo'
import IdentityInfo, { schema as identityInfoSchema } from './components/IdentityInfo'
import BankCardInfo, { schema as bankCardInfoSchema } from './components/BankCardInfo'
import EducationExperience, {
  schema as educationExperienceSchema,
} from './components/EducationExperience'
import ResignationCertificate, {
  schema as resignationCertificateSchema,
} from './components/ResignationCertificate'
import HealthInfo, { schema as healthInfoSchema } from './components/HealthInfo'
import RelationshipInfo, { schema as relationshipInfoSchema } from './components/RelationshipInfo'
import OtherInfo, { schema as otherInfoSchema } from './components/OtherInfo'

export default PersonPhoto

export {
  PersonPhoto,
  WorkCardPhoto,
  PersonBaseInfo,
  IdentityInfo,
  BankCardInfo,
  EducationExperience,
  ResignationCertificate,
  HealthInfo,
  RelationshipInfo,
  OtherInfo,
  personPhotoSchema,
  workCardPhotoSchema,
  personBaseInfoSchema,
  identityInfoSchema,
  bankCardInfoSchema,
  educationExperienceSchema,
  resignationCertificateSchema,
  healthInfoSchema,
  relationshipInfoSchema,
  otherInfoSchema,
}
