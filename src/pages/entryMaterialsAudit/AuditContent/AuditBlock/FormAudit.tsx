/**
 * @name FormAudit 表单审核组件
 */

import React, { useContext } from 'react'
import { Item, ItemGroup } from '@amazebird/antd-schema-form'
import { Card } from 'antd'
import { useMount } from 'ahooks'
import { useStore } from '@/stores'
import { FORM_MODE, WATCH_MODES, VIEW_MODE } from '@/constants/review'
import businessAuthor from '../../businessAuthor'
import {
  personPhotoAuditSchema,
  workCardPhotoAuditSchema,
  personBaseInfoAuditSchema,
  identityInfoAuditSchema,
  bankCardInfoAuditSchema,
  educationExperienceAuditSchema,
  resignationCertificateAuditSchema,
  healthInfoAuditSchema,
  relationshipInfoAuditSchema,
  otherInfoAuditSchema,
} from '../../config/auditConfig'
import Context from '../Context'

interface FormAuditProps {
  indexes: string
}

const FormAudit: React.FC<FormAuditProps> = React.memo((props) => {
  const { indexes } = props
  const { form } = useContext(Context)
  const { auditStatus } = useStore()
  const { getAuditInitialViewStatusGroup } = businessAuthor
  const viewStatus = getAuditInitialViewStatusGroup().form

  const layoutCol = {
    wrapperCol: { span: viewStatus === VIEW_MODE.EDIT ? 8 : 19 },
    labelCol: { span: 5 },
  }

  useMount(() => {
    if (viewStatus !== VIEW_MODE.NONE) {
      const mode = viewStatus === VIEW_MODE.EDIT ? FORM_MODE.MODIFY : FORM_MODE.DETAIL
      form.setState({ [WATCH_MODES.AUDIT_FORM_MODE]: mode })
      form.setFieldsValue({
        [`${indexes}_itemAuditResult`]: auditStatus[indexes].itemAuditResult,
        [`${indexes}_noPassReasonCodes`]: auditStatus[indexes].noPassReasonCodes,
        [`${indexes}_noPassReason`]: auditStatus[indexes].noPassReason,
      })
    }
  })

  const schema = {
    personPhoto: personPhotoAuditSchema(),
    workCardPhoto: workCardPhotoAuditSchema(),
    personBaseInfo: personBaseInfoAuditSchema(),
    identityInfo: identityInfoAuditSchema(),
    bankCardInfo: bankCardInfoAuditSchema(),
    educationExperience: educationExperienceAuditSchema(),
    resignationCertificate: resignationCertificateAuditSchema(),
    healthInfo: healthInfoAuditSchema(),
    relationshipInfo: relationshipInfoAuditSchema(),
    otherInfo: otherInfoAuditSchema(),
  }

  return viewStatus === VIEW_MODE.NONE ? null : (
    <ItemGroup schema={schema[indexes]}>
      <Card bordered={false} bodyStyle={{ backgroundColor: '#f0f0f0' }}>
        <Item field={`${indexes}_itemAuditResult`} {...layoutCol} />
        <Item field={`${indexes}_noPassReasonCodes`} {...layoutCol} />
        <Item field={`${indexes}_noPassReason`} {...layoutCol} />
      </Card>
    </ItemGroup>
  )
})

export default FormAudit
