/**
 * @name ModalAudit 弹窗审核组件
 */

import React, { useContext } from 'react'
import { Item, ItemGroup } from '@amazebird/antd-schema-form'
import { useMount } from 'ahooks'
import { FORM_MODE, WATCH_MODES, VIEW_MODE } from '@/constants/review'
import { renderTitle } from '@/pages/entryMaterialsAudit/config/config'

interface ModalAuditProps {
  indexes: string
  form: any
  fieldsValue: Record<string, any>
}

const layoutCol = {
  wrapperCol: { span: 24 },
  labelCol: { span: 0 },
}

const ModalAudit: React.FC<ModalAuditProps> = (props) => {
  const { indexes, form, fieldsValue } = props

  useMount(() => {
    form.setState({ [WATCH_MODES.AUDIT_FORM_MODE]: FORM_MODE.MODIFY })
    form.setFieldsValue({
      [`${indexes}_itemAuditResult`]: fieldsValue[`${indexes}_itemAuditResult`],
      [`${indexes}_noPassReasonCodes`]: fieldsValue[`${indexes}_noPassReasonCodes`],
      [`${indexes}_noPassReason`]: fieldsValue[`${indexes}_noPassReason`],
    })
  })

  return (
    <ItemGroup>
      <div style={{ display: 'flex', alignContent: 'center' }}>
        <div
          style={{
            minWidth: 100,
            textAlign: 'right',
            height: 32,
            lineHeight: '32px',
          }}
        >{`${renderTitle[indexes]}：`}</div>
        <div
          style={{
            flex: '1 1 0',
            overflow: 'hidden',
          }}
        >
          <Item field={`${indexes}_itemAuditResult`} {...layoutCol} />
          <Item field={`${indexes}_noPassReasonCodes`} {...layoutCol} />
          <Item field={`${indexes}_noPassReason`} {...layoutCol} />
        </div>
      </div>
    </ItemGroup>
  )
}

export default ModalAudit
