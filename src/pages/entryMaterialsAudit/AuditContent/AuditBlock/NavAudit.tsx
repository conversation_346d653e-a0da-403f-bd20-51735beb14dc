/**
 * @name NavAudit 导航审核组件
 */

import React from 'react'
import { VIEW_MODE } from '@/constants/review'
import { STASTUS_COLOR } from '@/constants/color'
import { EntryMaterialAuditInfo } from '@/types/entryMaterialsAudit'
// import auditBlockService from './service'
import businessAuthor from '../../businessAuthor'

interface NavAuditProps {
  indexes: string
  auditStatus: EntryMaterialAuditInfo
}

const NavAudit: React.FC<NavAuditProps> = (props) => {
  const { indexes, auditStatus } = props
  const { getAuditInitialViewStatusGroup } = businessAuthor
  const viewStatus = getAuditInitialViewStatusGroup().nav

  /**
   * 根据审核状态渲染不同的组件
   * @returns JSX.Element
   */
  const renderViewDom = (): JSX.Element => {
    if (auditStatus[indexes]?.itemAuditResult === 0) {
      return <span style={{ color: STASTUS_COLOR.RED }}>不符合要求</span>
    }
    if (auditStatus[indexes]?.itemAuditResult === 1) {
      return <span style={{ color: '#19b391' }}>符合要求</span>
    }
    return <span style={{ color: STASTUS_COLOR.GRAY }}>待审核</span>
  }

  /**
   * 根据key值渲染不同的组件
   * @param key
   * @returns JSX.Element | null
   */
  const handleAudit = (key): JSX.Element | null => {
    if (viewStatus === VIEW_MODE.NONE) {
      return null
    }
    const auditMap: { [key: string]: () => JSX.Element } = {
      personPhoto: renderViewDom,
      workCardPhoto: renderViewDom,
      personBaseInfo: renderViewDom,
      identityInfo: renderViewDom,
      bankCardInfo: renderViewDom,
      educationExperience: renderViewDom,
      resignationCertificate: renderViewDom,
      healthInfo: renderViewDom,
      relationshipInfo: renderViewDom,
      otherInfo: renderViewDom,
    }
    return auditMap[key]?.() || null
  }

  return handleAudit(indexes)
}

export default NavAudit
