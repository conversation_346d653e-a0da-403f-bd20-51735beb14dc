import UploadSDK from '@galaxy/upload'
import { type } from 'os'

class OssService {
  upload = new UploadSDK({
    clientCode: 'ENTRY',
    oss: {
      service: '/admin/entry',
    },
  })

  getOssSourceUrl = async (uuid: string | string[]): Promise<string | string[]> => {
    let returnUrl = '' as string | string[]
    try {
      if (Array.isArray(uuid)) {
        const res = await Promise.all(uuid.map((item) => this.upload.download(item)))
        returnUrl = res.map((item) => item.url)
      }

      if (typeof uuid === 'string' && uuid) {
        const res = await this.upload.download(uuid)
        returnUrl = res.url
      }
      return returnUrl
    } catch (error) {
      if (typeof uuid === 'string') {
        return ''
      }
      return []
    }
  }
}

export default new OssService()
