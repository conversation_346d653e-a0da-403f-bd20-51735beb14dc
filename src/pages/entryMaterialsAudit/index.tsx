/**
 * @description 材料审核
 */
import React, { useCallback, useEffect, useState } from 'react'
import useTimer, { CallbackType } from '@/hooks/useTimer'
import dmsService from '@/services/dms'
import { PAGE_VIEW } from '@/constants/dms/eventName'
import { MATERIAL_DETAIL } from '@/constants/dms/pageName'
import BaseCollapse from '@/components/BaseCollapse'
import TopBaseInfo from './TopBaseInfo'
import AuditContent from './AuditContent'
import initWrapper from './initWrapper'
import businessAuthor from './businessAuthor'

// 用于埋点参数
enum Process {
  'to_be_submitted',
  'to_be_resubmitted',
  'to_be_reviewed',
  'to_be_reviewed_twice',
  'to_be_reviewed_again',
  'failed',
  'passed',
  'special_passed',
  'closed',
}

const MaterialsAuditForm: React.FC = () => {
  const [topBaseInfoCollapse, setTopBaseInfoCollapse] = useState(false)
  // 页面逻辑，切换下一页会重新渲染
  useEffect(() => {
    dmsService.injectPageContext({
      pageName: MATERIAL_DETAIL,
      obmProgress: Process[businessAuthor.getProcess()],
    })
  }, [])

  const pageView = useCallback<CallbackType>(({ duration, start, end }) => {
    dmsService.trace(PAGE_VIEW, {
      timeBehavior: end,
      pageStayTime: duration,
      timeBehaviour__when__: start,
    })
  }, [])
  useTimer(pageView)

  return (
    <>
      <BaseCollapse callback={(collapse) => setTopBaseInfoCollapse(collapse)}>
        <TopBaseInfo />
      </BaseCollapse>
      <AuditContent topBaseInfoCollapse={topBaseInfoCollapse} />
    </>
  )
}

export default initWrapper(MaterialsAuditForm)
