/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React from 'react'
import { Result, Typography } from 'antd'
import { SmileOutlined, SmileTwoTone, CopyrightTwoTone } from '@ant-design/icons'
import config from '@/config'
import style from './style.module.less'

const { Paragraph, Text } = Typography
const { projectName } = config
const ProtalMain = function () {
  return (
    <div className={style.homePage}>
      <Result icon={<SmileOutlined />} title={`欢迎使用${projectName}`}>
        <div className="desc">
          <Paragraph>
            <Text
              strong
              style={{
                fontSize: 16,
              }}
            >
              关于
              {projectName}
            </Text>
          </Paragraph>
          <Paragraph>
            <SmileTwoTone />
            &nbsp;&nbsp;为人资业务赋能，提高内部效率。
          </Paragraph>
          <Paragraph>
            <CopyrightTwoTone />
            &nbsp;&nbsp;由技术支持部效能研发组提供技术支持
          </Paragraph>
        </div>
      </Result>
    </div>
  )
}

export default ProtalMain
