.attachFile {
  margin: 5px 0px;
}

.imageUpload {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;

  .imgItem {
    position: relative;
    display: inline-block;
    margin-right: 5px;

    &:hover .deleteIcon {
      display: block;
    }

    .deleteIcon {
      position: absolute;
      display: none;
      padding: 3px;
      right: 3px;
      top: 3px;
      z-index: 99;
      color: white;
      font-size: 12px;
      border-radius: 50%;
    }

    .deleteIcon:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  :global {
    .brick-upload-upload-image-btn {
      display: none;
    }

    .ant-upload-draggable-list-item {
      width: fit-content;
      display: inline-block;
    }

    .brick-upload-upload-image-item_icon_box {
      display: none;
    }
  }
}

.fileUploadWrapper {

  .typeText {
    vertical-align: top;
  }

  .tip {
    color: rgba(0, 0, 0, 0.5);
    margin: 10px 0px;
  }

  .hideUploadBtn {
    display: block !important;

    :global {
      .brick-upload-upload-top {
        display: none;
      }
    }
  }

  :global {
    .brick-upload-upload {
      display: inline-block;
    }

    .brick-upload-upload-list-item {
      margin: 8px 0px;
    }

    .ant-upload-draggable-list-item {
      flex: 1;
      margin-left: 5px;
      border-top: 2px dashed rgba(0, 0, 0, 0);
      border-bottom: 2px dashed rgba(0, 0, 0, 0);
    }

    .ant-upload-draggable-list-item.drop-over-downward {
      border-bottom-color: #1890ff;
    }

    .ant-upload-draggable-list-item.drop-over-upward {
      border-top-color: #1890ff;
    }
  }

  .uploadBtn {
    margin-right: 5px;
  }

  .uploadTip {
    color: rgba(0, 0, 0, 0.45);
  }

  .fileItem {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-top: 5px;

    &:hover {
      background-color: #F5F5F5;
    }

    .fileIcon {
      margin-right: 5px;
      width: 15px;
    }

    .name {
      color: #14CCA0;
      flex: 1;
    }

    .deleteIcon {
      float: right;
      width: 15px;
    }
  }


}
