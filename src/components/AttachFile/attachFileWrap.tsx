import React, { useEffect, useState } from 'react'
import ossService from '@/pages/entryMaterialsAudit/ossService'

export default function AttachFileWrapp(WrappedComponent) {
  return ({ value = [], ...rest }) => {
    const [transformValue, setTransformValue] = useState({})
    const [hasHandle, setHasHandle] = useState(false)

    const transformValueFun = async () => {
      const $value = []
      const $imgList = []
      const $files = []
      const result = await ossService.getOssSourceUrl(value?.map((v) => v.url))
      value?.forEach((v, index) => {
        const current = {
          ...v,
          uid: v.url,
          uuid: v.url,
          url: result[index],
          name: v.fileName || v.name,
        }
        v.attachmentType === 0 ? $imgList.push(current) : $files.push(current)
        $value.push(current)
      })
      setTransformValue({
        value: $value,
        imageList: $imgList,
        fileList: $files,
      })
      setHasHandle(true)
    }

    useEffect(() => {
      transformValueFun()
    }, [value])

    if (!hasHandle) {
      return null
    }
    return <WrappedComponent {...rest} {...transformValue} />
  }
}
