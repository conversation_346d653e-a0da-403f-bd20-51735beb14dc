import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react'
import { UploadOutlined } from '@ant-design/icons'
import { Button, message, Image, Tooltip } from 'antd'
import { Upload } from '@galaxy/upload-component'
import { FileProps } from './types'

import styles from './styles.module.less'

const imgFormat = ['jpg', 'png']

interface IProps {
  value?: FileProps[]
  ossServiceParams: any
  sizeLimit: number
  fileLengthLimit?: number
  [x: string]: any
}

const fileFormatLimit = ['.jpg', '.png', '.doc', '.docx', '.pdf']

const FileUpload: React.FC<IProps> = ({
  value,
  ossServiceParams,
  sizeLimit,
  fileLengthLimit,
  onChange,
}: IProps) => {
  const [currentImgIndex, setCurrentImgIndex] = useState(0)
  const [previewUrl, setPreviewUrl] = useState('')
  const [fileList, setFileList] = useState<FileProps[]>([])
  const [previewVisible, setPreviewVisible] = useState(false)
  const keyRef = useRef(0)
  const filesUploadParams = useRef([])

  // 用来重新渲染
  const imgKeyId = useMemo(() => {
    keyRef.current += 1
    return keyRef.current
  }, [fileList])

  const getFileFormat = (fileName) => {
    const useFileName = fileName || ''
    return useFileName.slice(useFileName.lastIndexOf('.') + 1)
  }

  const beforeUpload = async (file) => {
    const size = file.size / 1024 / 1024
    const type = `.${getFileFormat(file.name)}`

    if (size > sizeLimit) {
      message.warn(`单个附件大小不超过 ${sizeLimit} M`)
      return false
    }

    if (!fileFormatLimit.includes(type)) {
      message.warn('请上传对应格式的文件')
      return false
    }

    filesUploadParams.current.push(file)
    return file
  }

  const handlePreview = async (file) => {
    const { url, name } = file || {}
    const type = getFileFormat(name)

    // 从图片类型文件中找到当前预览图片的下标

    const index = fileList
      .filter((item) => {
        const t = getFileFormat(item.name)
        return imgFormat.includes(t)
      })
      .findIndex((v) => v.uuid === file.uuid)

    if (imgFormat.includes(type)) {
      setPreviewUrl(url)
      setCurrentImgIndex(index || 0)
      setPreviewVisible(true)
    }
  }

  const uploadButton = (
    <>
      <Button className={styles.uploadBtn} icon={<UploadOutlined />}>
        点击上传
      </Button>
      <span className={styles.uploadTip}>
        支持上传jpg、png、pdf、doc、docx格式文件；每个文件大小不可超过10M；最多上传10个附件
      </span>
    </>
  )

  useEffect(() => {
    if (!value || !Array.isArray(value) || value.length === 0) {
      return
    }

    setFileList(value)
  }, [value])

  const imgFiles = useMemo(() => {
    return fileList.filter((v) => {
      if (!v) {
        return false
      }
      const useName = v.fileName || v.name || ''
      const type = useName.slice(useName.lastIndexOf('.') + 1)
      return imgFormat.includes(type)
    })
  }, [fileList])

  return (
    <div className={styles.fileUploadWrapper}>
      <span className={styles.typeText}>附件：</span>
      <Upload
        value={fileList}
        accept={fileFormatLimit.join(',')}
        multiple
        /**
         * TODO
         * 组件内部使用排序功能会与 ImageUpload 组件的排序功能冲突，导致 react-dnd 会报  Cannot have two HTML5 backends at the same time.
         * 因此通过 dragSort={false} 禁用内部组件的拖拽功能，通过 fileNameRender 中自己实现拖拽功能
         *
         * 也许是因为 FileUpload 内部拖拽组件和 ImageUpload 中拖拽组件的层级不一致导致的报错，相同层级则不会报错
         */
        maxNum={fileLengthLimit}
        fileSizeLimit={fileLengthLimit}
        onChange={(files) => {
          const notMergeSizeParamsFiles = files.slice(-filesUploadParams.current.length)
          const normalFiles = files.slice(0, -filesUploadParams.current.length)

          const newMergeSizeParamsFiles = notMergeSizeParamsFiles.map((file, index) => ({
            ...file,
            size: filesUploadParams.current[index]?.size || 0,
          }))

          // 清空缓存文件参数列表
          filesUploadParams.current = []

          const newFiles = [...normalFiles, ...newMergeSizeParamsFiles]
          onChange(newFiles)
        }}
        beforeUpload={beforeUpload}
        className={fileList.length >= fileLengthLimit ? styles.hideUploadBtn : null}
        fileNameRender={(_, file) => {
          const type = getFileFormat(file.name)
          const isImg = imgFormat.includes(type)
          const originNode = (
            <div style={{ flex: 1 }} onClick={() => handlePreview(file)}>
              <span style={isImg ? { color: '#1ABB9C', cursor: 'pointer' } : null}>
                {file.name}
              </span>
            </div>
          )
          return (
            <div className="ant-upload-draggable-list-item" style={{ cursor: 'move' }}>
              {(file as any).status === 'error' ? (
                <Tooltip title="上传失败">{originNode}</Tooltip>
              ) : (
                originNode
              )}
            </div>
          )
        }}
        {...ossServiceParams}
      >
        {fileList.length >= fileLengthLimit ? null : uploadButton}
      </Upload>
      <Image.PreviewGroup
        key={imgKeyId}
        preview={{
          visible: previewVisible,
          current: currentImgIndex,
          scaleStep: 0.5,
          src: previewUrl,
          onVisibleChange: (v) => {
            setPreviewVisible(v)
          },
        }}
      >
        {imgFiles.map((v) => (
          <Image key={v.uuid} width={200} style={{ display: 'none' }} src={v.url} />
        ))}
      </Image.PreviewGroup>
    </div>
  )
}

export default FileUpload
