import React, { useEffect } from 'react'
import { Image } from 'antd'
import PdfPreview from '@/components/PdfPreview'
import DocPreview from '@/components/DocPreview'
import { EntryOtherAttachments } from '@/types/entryMaterialsAudit'

type IProps = {
  openWaterMark?: boolean
  pdfWidth?: number
  docHeight?: number
  docWidth?: number
  imgWidth?: string | number
  fileList: EntryOtherAttachments[]
}

const Preview = ({
  fileList,
  imgWidth = 634,
  docHeight,
  docWidth,
  pdfWidth,
  openWaterMark = true,
}: IProps) => {
  /**
   * 创建预览组件
   * @returns
   */
  const createPreviewComponent = (list: EntryOtherAttachments[]) => {
    const previewComponents = []
    list.forEach((item) => {
      const { fileName, name, url, uid } = item
      const useName = fileName || name || ''
      const type = useName.slice(useName.lastIndexOf('.') + 1)
      switch (type) {
        case 'jpg':
          previewComponents.push(<Image key={uid} width={imgWidth} src={url} />)
          break
        case 'png':
          previewComponents.push(<Image key={uid} width={imgWidth} src={url} />)
          break
        case 'pdf':
          previewComponents.push(
            <PdfPreview pdfUrl={url} pdfWidth={pdfWidth} openWaterMark={openWaterMark} />,
          )
          break
        case 'doc':
          previewComponents.push(
            <DocPreview
              docHeight={docHeight}
              docWidth={docWidth}
              docUrl={url}
              openWaterMark={openWaterMark}
            />,
          )
          break
        case 'docx':
          previewComponents.push(
            <DocPreview
              docHeight={docHeight}
              docWidth={docWidth}
              docUrl={url}
              openWaterMark={openWaterMark}
            />,
          )
          break
      }
    })

    return previewComponents
  }

  return (
    <div>
      <Image.PreviewGroup>{...createPreviewComponent(fileList)}</Image.PreviewGroup>
    </div>
  )
}

export default Preview
