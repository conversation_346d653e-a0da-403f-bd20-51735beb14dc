import React, { useState, useEffect, useRef } from 'react'
import { Image } from 'antd'
import { Upload } from '@galaxy/upload-component'
import { FileProps } from './types'

import styles from './styles.module.less'

interface IProps {
  initValue: FileProps[]
  [x: string]: any
}

/**
 * 用 Image 实现该图片功能的原因：
 *
 * 1. antd 的 upload 组件与 @galaxy/upload-component 文件属性不一致，为了避免多余数据的处理以及后期维护复杂度
 * 2. @galaxy/upload-component 无法自定义图片预览效果，与 FileUpload 预览效果不一致
 *
 * 因为以上两个原因而选用 Image 组件进行图片的展示和预览效果
 */
const ImageUpload: React.FC<IProps> = ({ initValue, onChange }) => {
  const [fileList, setFileList] = useState<FileProps[]>([])
  const isInit = useRef(false)

  useEffect(() => {
    if (!initValue || !Array.isArray(initValue) || initValue.length === 0 || isInit.current) {
      return
    }
    setFileList(initValue)
    isInit.current = true
  }, [initValue])

  useEffect(() => {
    onChange?.(fileList)
  }, [fileList])

  return (
    <div className={styles.imageUpload}>
      <span>图片：</span>
      {fileList.length > 0 ? (
        <Image.PreviewGroup>
          <Upload
            listType="picture-card"
            oss={{
              service: '/admin/entry',
            }}
            value={fileList}
            onChange={(files) => {
              setFileList(files as any)
            }}
          />
        </Image.PreviewGroup>
      ) : (
        '未上传'
      )}
    </div>
  )
}

export default ImageUpload
