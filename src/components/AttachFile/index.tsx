import React from 'react'
import { EntryOtherAttachments } from '@/types/entryMaterialsAudit'
import ImageUpload from './ImageUpload'
import FileUpload from './FileUpload'
import Preview from './Preview'
import styles from './styles.module.less'
import AttachFileWrapp from './attachFileWrap'

type AttachmentsType = EntryOtherAttachments & {
  uid: string | number
  uuid: string | number
  url: string
  name: string
}

interface IProps {
  value: AttachmentsType[]
  /**
   * 是否只读
   */
  readOnly?: boolean
  /**
   * 文件大小显示，以 m 为单位
   */
  fileSizeLimit: number
  /**
   * 文件长度限制
   */
  fileLengthLimit: number
  [x: string]: any
}

const OssServiceParams = {
  clientCode: 'ENTRY',
  oss: {
    service: '/admin/entry',
  },
}

const AttachFile = ({
  value = [],
  imageList = [],
  fileList = [],
  readOnly,
  fileSizeLimit,
  fileLengthLimit,
  onChange,
}: IProps) => {
  /**
   * 文件改变回调 向外部表单传值
   * @param current 当前改变的数组 文件或图片
   * @param isImg 是否为图片
   */
  const onFileChange = (current, isImg?) => {
    const otherList = isImg ? fileList : imageList

    const currentList = current.map((i) => ({
      ...i,
      url: i.uuid,
      fileName: i.fileName || i.name,
      attachmentType: isImg ? 0 : 1,
    }))

    const newOtherList = otherList.map((i) => ({
      ...i,
      url: i.uuid,
      fileName: i.fileName || i.name,
      attachmentType: !isImg ? 0 : 1,
    }))

    const result = [...currentList, ...newOtherList]

    // 检查新的文件列表和旧的文件列表是否相同
    if (JSON.stringify(result) !== JSON.stringify(value)) {
      onChange?.(result)
    }
  }

  /**
   * 预览组件
   */
  const PreviewComponent = () => {
    if (
      (!Array.isArray(imageList) && !Array.isArray(fileList)) ||
      (fileList.length === 0 && imageList.length === 0)
    ) {
      return <span>未上传</span>
    }
    return (
      <>
        <Preview fileList={imageList} />
        <Preview fileList={fileList} />
      </>
    )
  }

  return (
    <div className={styles.attachFile}>
      {!readOnly ? (
        <>
          <ImageUpload initValue={imageList} onChange={(files) => onFileChange(files, 'IS_IMG')} />
          <FileUpload
            key={JSON.stringify(fileList)}
            value={fileList}
            sizeLimit={fileSizeLimit}
            ossServiceParams={OssServiceParams}
            fileLengthLimit={fileLengthLimit}
            onChange={(files) => onFileChange(files)}
          />
        </>
      ) : (
        PreviewComponent()
      )}
    </div>
  )
}

export default AttachFileWrapp(AttachFile)
