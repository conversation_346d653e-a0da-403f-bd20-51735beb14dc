import React, { useEffect, useMemo } from 'react'
import { useActivate } from '@/components/cacheRoute'
import { useStore } from '@/stores'
import Header from './Header'
import style from './style.module.less'
import Content from './Content'

interface IProps {
  children: any
  needBack?: any
  title?: string
}

// item.component
const BaseContainer: React.FC<IProps> = function ({ children, needBack, title }: IProps) {
  const { setTitle, setBack } = useStore()

  useEffect(() => {
    setTitle(title)
    setBack(needBack)
  }, [title, needBack])

  useActivate(() => {
    setTitle(title)
  })

  return (
    <main className={style.container__layout}>
      <Header defaultTitle={title} />
      <Content>{children}</Content>
    </main>
  )
}

export default BaseContainer
