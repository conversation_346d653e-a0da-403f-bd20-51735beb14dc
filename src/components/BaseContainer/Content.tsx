import React from 'react'
import { useStore } from '@/stores'
import classNames from 'classnames'
import style from './style.module.less'

type IProps = {
  children?: any
}

const Content: React.FC<IProps> = ({ children }) => {
  const { closely } = useStore()
  return (
    <div className={`${style.container__content}`}>
      <div className={classNames({ [style.container__main]: true, [style.closely]: !!closely })}>
        {children}
      </div>
    </div>
  )
}

export default Content
