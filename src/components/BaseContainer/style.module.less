.container__layout {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 94px);
  margin-bottom: -24px; // 因为主应用容器有一个margin:20的边界
}

.container__content {
  flex-grow: 1;
  overflow: auto;
}

.container__main {
  height: 100%;
  padding: 24px;
}

.containerHeader {
  width: 100%;
  background-color: #fff;
  display: flex;
  padding: 0px 24px 17px 24px;
  justify-content: space-between;
  max-height: 49px;
  .containerTitle {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .containerExtra {
    & > * {
      margin-left: 12px;
    }
    [data-back~='true'] {
      position: absolute;
      margin-top: -46px;
      right: 24px;
    }
    [class~='baseContainer_backBtn'] {
      font-size: 14px;
      cursor: pointer;
      color: #595959;
    }
  }
}

.closely {
  padding: 0;
}

.back {
  position: absolute;
  top: -40px;
  right: 24px;

  [class~='baseContainer_backBtn'] {
    font-size: 14px;
    cursor: pointer;
    color: #595959;
  }
}
