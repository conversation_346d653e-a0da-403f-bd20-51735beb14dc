import React from 'react'
import { useStore } from '@/stores'
import { RollbackOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { IS_MICROCONTEXT } from '@/constants'
import style from './style.module.less'

interface IProps {
  defaultTitle?: string
}

// item.component
const Header: React.FC<IProps> = function ({ defaultTitle }) {
  const navigate = useNavigate()

  const store = useStore()
  const { extra, title, needBack } = store

  if (extra || defaultTitle || title || needBack) {
    return (
      <>
        {extra || defaultTitle || title ? (
          <div className={style.containerHeader}>
            <div className={style.containerTitle}>{title || defaultTitle}</div>
            <div className={style.containerExtra}>{extra}</div>
          </div>
        ) : null}
        <div className={style.back}>
          {typeof needBack === 'boolean' ? (
            needBack && (
              <span
                data-back={IS_MICROCONTEXT}
                className="baseContainer_backBtn"
                onClick={() => navigate(-1)}
              >
                <RollbackOutlined />
                返回
              </span>
            )
          ) : (
            <span data-back={IS_MICROCONTEXT}>{needBack}</span>
          )}
        </div>
      </>
    )
  }

  return null
}

export default Header
