import React, { useEffect, useRef, useState, useContext } from 'react'
import { CloseOutlined, CheckOutlined } from '@ant-design/icons'
import classnames from 'classnames'
import Context from './Context'
import Style from './style.module.less'

interface Iprops {
  className?: string | null
}

const AnchorLine: React.FC<Iprops> = ({ className }: Iprops) => {
  const { isInit, scrollPosition, anchorItems, setAnchorItemsFun } = useContext(Context)
  // 所有节点的信息
  const domsRef: any = useRef()
  // 当前节点
  const [currentIndex, setCurrentIndex] = useState<number>(0)

  // 当节点初始化完成后，获取所有节点的信息
  useEffect(() => {
    if (isInit) {
      const doms: any = document.querySelectorAll('[data-type="AnchorItem"]')
      domsRef.current = doms
      const arr: any = []
      for (let i = 0; i < doms.length; i++) {
        if (!anchorItems.current?.[i]) {
          const title: string = doms[i].getAttribute('date-title')
          const message: string | React.ReactNode = doms[i].getAttribute('data-message')

          arr.push({
            title,
            message,
          })
        }
      }
      setAnchorItemsFun(anchorItems.current?.concat(arr))
    }
  }, [isInit])

  // 当AnchorContext滚动时，根据滚动的距离，判断当前的节点
  useEffect(() => {
    const doms = domsRef.current
    for (let i = 0; i < doms?.length; i++) {
      const top = doms[i].offsetTop
      if (top >= scrollPosition) {
        setCurrentIndex(i)
        break
      }
      // 修复最后一项定位不准的问题
      if (
        doms.length - 2 > 0 &&
        doms[doms.length - 2].offsetTop &&
        top > doms[doms.length - 2].offsetTop &&
        top < scrollPosition
      ) {
        setCurrentIndex(doms.length - 1)
        break
      }
    }
  }, [scrollPosition])

  // 状态显示节点不同的样式
  const getStatus = (index: number, status: 'success' | 'error'): string => {
    if (status) {
      return Style[status]
    }
    if (index === currentIndex) {
      return Style.currentItem
    }
    if (index < currentIndex) {
      return Style.beforeItem
    }
    return ''
  }

  // icon显示判定
  const getIcon = (index: number, status: 'success' | 'error'): number | React.ReactNode => {
    if (status === 'success' || status === 'error') {
      return status === 'success' ? <CheckOutlined /> : <CloseOutlined />
    }
    return index + 1
  }

  /**
   * 点击锚点，滚动到对应的节点。
   * @param index 点击的节点
   */
  const handleClick = (index: number): void => {
    domsRef.current[index].scrollIntoView(true)
    setCurrentIndex(index)
  }

  return (
    <div className={`${className} ${Style.AnchorsLine}`}>
      {!!isInit &&
        !!anchorItems.current?.length &&
        anchorItems.current.map((item, index) => (
          <div
            className={`${Style.AnchorsLineItem} ${getStatus(index, item.status)}`}
            onClick={() => {
              handleClick(index)
            }}
            key={item.key || item.id || index}
          >
            {/* icon及连接线 */}
            <div className={Style.AnchorsLineItemLeft}>
              <span className={Style.serialNumber}>{getIcon(index, item.status)}</span>
              {index < anchorItems.current.length - 1 && <span className={Style.Line} />}
            </div>
            {/* 右侧title */}
            <div className={classnames(Style.AnchorsLineItemRight)}>
              <div
                className={classnames(
                  index === currentIndex && Style.blod,
                  item.status === 'error' && Style.errorColor,
                )}
              >
                {item.title}
              </div>
              <div
                style={{
                  marginTop: 10,
                }}
              >
                <div dangerouslySetInnerHTML={{ __html: item.message }} />
              </div>
            </div>
          </div>
        ))}
    </div>
  )
}

AnchorLine.defaultProps = {
  className: null,
}

export default AnchorLine
