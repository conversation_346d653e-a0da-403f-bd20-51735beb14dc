import React, { useState, useMemo, useEffect, useRef } from 'react'
import { useDeepCompareEffect, useUpdate } from 'ahooks'
import { cloneDeep } from 'lodash'
import Context from './Context'
import styles from './style.module.less'
import { AnchorItems } from './types'

interface Props {
  children: React.ReactNode
  className?: string | null
  style?: React.CSSProperties
}

const Anchors: React.FC<Props> = ({ children, className, style }: Props) => {
  // 初始化状态判定
  const [isInit, setIsInit] = useState<boolean>(false)
  // 锚点状态栏数据状态集合
  const anchorItems = useRef<AnchorItems[]>([])
  // 滚动距离
  const [scrollPosition, setScrollPosition] = useState<number>(0)
  // 刷新态
  const upDate = useUpdate()

  /**
   * 设置锚点状态栏数据状态集合
   * @param items
   */
  const setAnchorItemsFun = (items: AnchorItems[]): void => {
    anchorItems.current = items
    upDate()
  }

  /**
   * 外部依赖需手动触发的校验
   * @param title 标题
   * @param status 标题状态 success | error 主要是必填项校验
   * @param message 标题下的状态信息
   */
  const setAnchorStatusFun = (
    title: string,
    status: 'success' | 'error',
    message: string | React.ReactNode,
  ): void => {
    const index = anchorItems.current?.findIndex((item) => item.title === title)
    const copyAnchorItems = cloneDeep(anchorItems.current)
    if (index !== -1) {
      copyAnchorItems[index].status = status
      copyAnchorItems[index].message = message
    }
    setAnchorItemsFun(copyAnchorItems)
  }

  /**
   * 设置滚动距离
   * @param top 滚动距离
   */
  const setScrollPositionFun = (top: number): void => {
    setScrollPosition(top)
  }

  useEffect(() => {
    setIsInit(true)
  }, [children])

  const fireBaseProviderValue = useMemo(
    () => ({
      isInit,
      anchorItems,
      scrollPosition,
      setAnchorItemsFun,
      setAnchorStatusFun,
      setScrollPositionFun,
    }),
    [isInit, anchorItems.current, scrollPosition],
  )

  return (
    <div className={`${styles.Anchors} ${className || {}}`} style={style}>
      <Context.Provider value={fireBaseProviderValue}>{children}</Context.Provider>
    </div>
  )
}

Anchors.defaultProps = {
  className: null,
  style: {},
}

export default Anchors
