.AnchorsContext {
    height: 100%;
    overflow-y:auto ;
  }
  .Anchors {
    * {
      scroll-behavior: smooth;
    }
  }
  .AnchorsLine {
    display: flex;
    height: 100%;
    overflow-y: auto;
    flex-direction: column;
    .currentItem {
      .serialNumber {
        background-color: rgb(25,179,145) !important;
        border:1px solid rgb(25,179,145) !important;
        color:#fff !important;
      }
      // .Line {
      //   background-color: rgb(25,179,145) !important;
      // }
    }
    .beforeItem {
      .Line {
        background-color: rgb(25,179,145) !important;
      }
    }
    .success {
      .serialNumber {
        background-color: #fff !important;
        border:1px solid rgb(25,179,145) !important;
        color: rgb(25,179,145) !important;
      }
    }
    .error {
      .serialNumber {
        background-color: #fff !important;
        border:1px solid rgb(255,79,81) !important;
        color: rgb(255,79,81) !important;
      }
    }

    .AnchorsLineItem {
      // padding: 10px;
      padding: 16px;
      min-width: 100px;
      display: flex;
      min-height: 80px;
      .AnchorsLineItemLeft {
        width: 22px;
        cursor: pointer;
        .serialNumber {
          border-radius: 50%;
          height: 20px;
          width: 20px;
          line-height: 20px;
          cursor: pointer;
          color:rgb(138,138,138);
          display: inline-block;
          border:1px solid #ccc;
          text-align: center;
          background-color: #fff;
        }
        .Line {
          width: 1px;
          display: block;
          margin: 10px auto 0;
          height: calc(100% - 20px);
          background-color: #ccc;
        }
      }
      .AnchorsLineItemRight {
        width: calc(100% - 22px);
        display: inline-block;
        cursor: pointer;
        padding-left: 10px;
        line-height: 20px;
        color:rgb(138,138,138)
      }
    }

    .defaultColor {
      color: #000,
    }
    .blod{
      font-weight: 500;
      color: #000,
    }
    .errorColor {
      color: rgb(255,110,110);
    }
  }
