import React, { useEffect, useRef, useContext } from 'react'
import { debounce } from 'lodash-es'
import Context from './Context'

interface Iprops {
  children: React.ReactNode
  style?: React.CSSProperties
  className?: string
}
const AnchorContext: React.FC<Iprops> = ({ children, style, className }: Iprops) => {
  const domRef: any = useRef(null)

  const { setScrollPositionFun } = useContext(Context)

  const scrollFun = debounce((e) => {
    if (e.target) {
      setScrollPositionFun(e.target.scrollTop)
    }
  }, 200)

  useEffect(() => {
    const dom = domRef.current
    setScrollPositionFun(0)
    dom.addEventListener('scroll', (e) => {
      scrollFun(e)
    })
  }, [])

  return (
    <div ref={domRef} style={style} className={className} data-type="AnchorContext">
      {children}
    </div>
  )
}

AnchorContext.defaultProps = {
  style: {},
  className: '',
}

export default AnchorContext
