## 表单锚点组件使用案例

```jsx
import { Anchor<PERSON>, AnchorL<PERSON>, AnchorContext, AnchorItem } from '@/components/AnchorLine'
////

const MaterialsAuditForm:React.FC = () => (
    <Anchors className={styles.anchor_box}>
      <AnchorLine className={styles.anchor_line_box} />
      <AnchorContext className={styles.anchor_context_box}>
        <AnchorItem title="个人照片" message="不符合要求">
          <div>个人照片区域</div>
        </AnchorItem>
        <AnchorItem title="工牌照片" message="符合要求">
          <div>工牌照片区域</div>
        </AnchorItem>
        <AnchorItem title="个人基本信息" message="待审核">
          <div>个人基本信息区域</div>
        </AnchorItem>
        <AnchorItem title="身份证信息" message="待审核">
          <div>身份证信息区域</div>
        </AnchorItem>
      </AnchorContext>
    </Anchors>
)

export default MaterialsAuditForm

``` 
