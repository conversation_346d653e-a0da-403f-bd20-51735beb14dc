import React, { useEffect, useContext } from 'react'
import ReactD<PERSON><PERSON>erver from 'react-dom/server'
import Context from './Context'

interface Iprops {
  title: string | React.ReactNode
  children: any
  childProps?: any
  style?: React.CSSProperties
  className?: string | null
  status?: any
  message?: any
}
const AnchorItem: React.FC<Iprops> = ({
  title,
  children,
  childProps,
  style,
  className,
  message,
  status,
}: Iprops) => {
  const child = React.cloneElement(children, childProps)

  const { setAnchorStatusFun } = useContext(Context)

  const domToString = (element) => {
    if (!element) {
      return ''
    }
    const string = ReactDOMServer.renderToString(element)
    return string
  }

  useEffect(() => {
    setAnchorStatusFun(title, status, domToString(message))
  }, [status, message])

  return (
    <div
      style={style}
      className={`${className}`}
      data-type="AnchorItem"
      // eslint-disable-next-line react/no-unknown-property
      date-title={title}
      data-message={domToString(message)}
    >
      {child}
    </div>
  )
}

AnchorItem.defaultProps = {
  childProps: {},
  style: {},
  className: '',
  message: '',
  status: '',
}

export default AnchorItem
