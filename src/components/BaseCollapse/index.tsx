import React, { useState, useEffect } from 'react'
import { DownOutlined, UpOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import style from './style.module.less'

interface Iprops {
  defaulutCollapse?: boolean
  defaultClass?: string
  defaultStyle?: any
  direction?: 'horizontal' | 'vertical'
  children?: React.ReactNode
  gap?: number
  callback?: (collapse: boolean) => void
}

const BaseCollapse = (props: Iprops) => {
  const { children, direction, defaulutCollapse, defaultClass, defaultStyle, gap, callback } = props
  const [collapse, setCollapse] = useState(!!defaulutCollapse)

  useEffect(() => {
    callback && callback(collapse)
  }, [collapse])

  const getIcon = () => {
    if (direction === 'horizontal') {
      if (collapse) {
        return <DownOutlined />
      }
      return <UpOutlined />
    }
    if (collapse) {
      return <RightOutlined />
    }
    return <LeftOutlined />
  }

  return (
    <div
      className={classNames(
        style.collapse_cpm,
        {
          [style.horizontal_collapse]: direction === 'horizontal',
          [style.vertical_collapse]: direction === 'vertical',
        },
        defaultClass,
      )}
      style={defaultStyle}
    >
      <div
        className={classNames({
          [style.hide_child]: collapse,
        })}
      >
        {children}
      </div>
      {gap ? <div style={{ width: `${gap}px`, height: `${gap}px` }} /> : null}
      <div
        className={style.icon_wrap}
        onClick={() => {
          setCollapse(!collapse)
        }}
      >
        <div
          className={style.icon_box}
          style={direction === 'horizontal' ? { padding: '0 10px' } : { padding: '10px 0' }}
        >
          {getIcon()}
        </div>
      </div>
    </div>
  )
}

BaseCollapse.defaultProps = {
  direction: 'horizontal',
  defaulutCollapse: false,
  defaultStyle: {},
  gap: 0,
}

export default BaseCollapse
