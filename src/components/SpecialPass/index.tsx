import React, { useEffect, useState } from 'react'
import { RightCircleOutlined } from '@ant-design/icons'
import materialService from '@/services/preEntry/material'
import { Col, Image, Row, Skeleton, Space } from 'antd'
import { SpecialApprovalInfoType } from '@/types/entryMaterialsAudit'
import UploadSdk from '@galaxy/upload'
import ReModal from '../ReModal'
import style from './style.module.less'

type Iprops = {
  id?: number
  dataSource?: SpecialApprovalInfoType
}

const SpecialPassContent: React.FC<Iprops> = ({ id, dataSource }) => {
  const [init, setInit] = useState<boolean>(false)
  const [screenshots, setScreenshots] = useState<{ id: number; url: string }[]>([])
  const [remark, setRemark] = useState<string>()

  const uploadSdk = new UploadSdk({ oss: { service: '/admin/entry' } })

  const getData = async () => {
    let data: SpecialApprovalInfoType | undefined
    if (typeof id !== 'number') {
      data = dataSource
    } else {
      data = await materialService.getSepcialApprovalInfo(id)
    }
    if (!data) {
      return
    }

    const $screenshots = await Promise.all(
      data.screenshots.map((item) => {
        return uploadSdk
          .download(item.url)
          .then((res) => ({
            id: item.id,
            url: res.url,
          }))
          .catch(() => ({
            id: item.id,
            url: '',
          }))
      }),
    )

    setScreenshots($screenshots)
    setRemark(data.remark)
    setInit(true)
  }

  useEffect(() => {
    getData()
  }, [])

  if (!init) {
    return <Skeleton />
  }

  return (
    <div>
      <Row className={style.row}>
        <Col span={4} className={style.label}>
          <span>单据截图：</span>
        </Col>
        <Col span={20}>
          <Image.PreviewGroup>
            <Space>
              {screenshots.map((item, index) => (
                <Image key={item.id} width={80} src={item.url} />
              ))}
            </Space>
          </Image.PreviewGroup>
        </Col>
      </Row>
      {remark && (
        <Row className={style.row}>
          <Col span={4} className={style.label}>
            <span>说明：</span>
          </Col>
          <Col span={20}>
            <span>{remark}</span>
          </Col>
        </Row>
      )}
    </div>
  )
}

const SpecialPass: React.FC<Iprops> = ({ id, dataSource }) => {
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <span>特批通过</span>
      <RightCircleOutlined
        className={style.icon}
        onClick={() => {
          setOpen(true)
        }}
      />
      <ReModal
        open={open}
        title="特批通过材料"
        cancelButtonProps={{ style: { display: 'none' } }}
        destroyOnClose
        width={600}
        onOk={() => {
          setOpen(false)
        }}
        onCancel={() => {
          setOpen(false)
        }}
      >
        <SpecialPassContent dataSource={dataSource} id={id} />
      </ReModal>
    </>
  )
}

export default SpecialPass
