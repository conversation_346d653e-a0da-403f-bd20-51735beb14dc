/**
 * @name 问号icon气泡提示
 */
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import React, { CSSProperties, useMemo } from 'react'
import style from './style.module.less'

interface Iprops {
  beforeContent?: string
  tip?: React.ReactNode | (() => React.ReactNode) // 气泡提示
  pointTips?: (string[] | string)[] | string[] | string // 分点提示
  mark?: string | React.ReactNode // 标题
  width?: string // 气泡宽
  colon?: boolean // 是否显示冒号
  iconStyle?: CSSProperties
}

const QuestionTooltip: React.FC<Iprops> = ({
  beforeContent,
  colon,
  mark,
  tip,
  pointTips,
  width,
  iconStyle,
}: Iprops) => {
  const title = useMemo(() => {
    if (tip) {
      return tip
    }
    if (Array.isArray(pointTips)) {
      return (
        <div>
          {beforeContent && (
            <span key="before" className={style.tip}>
              {beforeContent}
            </span>
          )}
          {pointTips.map((item, index) => {
            if (typeof item === 'string') {
              return (
                // eslint-disable-next-line react/no-array-index-key
                <span key={index} className={style.tip}>
                  {`${index + 1}、${item}`}
                </span>
              )
            }
            return item.map((item1, index1) => (
              // eslint-disable-next-line react/no-array-index-key
              <span key={`${index}-${index1}`} className={style.tip}>
                {`（${index1 + 1}）${item1}`}
              </span>
            ))
          })}
        </div>
      )
    }
    if (pointTips) {
      return (
        <div>
          {beforeContent && (
            <span key="before" className={style.tip}>
              {beforeContent}
            </span>
          )}
          <span key="tip" className={style.tip}>{`${pointTips}`}</span>
        </div>
      )
    }
    return ''
  }, [])
  return (
    <span className={style.questionTooltip}>
      {mark && <span className={style.content}>{mark}</span>}
      <Tooltip placement="top" title={title} overlayStyle={{ maxWidth: width }}>
        <QuestionCircleOutlined
          onClick={(e) => {
            e.preventDefault()
          }}
          style={{ cursor: 'pointer', ...iconStyle }}
        />
      </Tooltip>
      {colon && <span className={style.colon}>:</span>}
    </span>
  )
}

QuestionTooltip.defaultProps = {
  colon: false,
  width: '480px',
}

export default QuestionTooltip
