import { Tag } from 'antd'
import React from 'react'
import moment from 'moment'
import style from './style.module.less'

export enum TAG {
  TODAY = 'today',
  TOMORROW = 'tomorrow',
  WEEK = 'week',
  MONTH = 'month',
}

const tagConfig = new Map<TAG, { key: TAG; name: string; value: moment.Moment; color?: string }>()
tagConfig.set(TAG.TODAY, {
  key: TAG.TODAY,
  name: '今天',
  color: 'green',
  value: moment(),
})
tagConfig.set(TAG.TOMORROW, {
  key: TAG.TOMORROW,
  name: '明天',
  color: 'green',
  value: moment().add(1, 'day'),
})
tagConfig.set(TAG.WEEK, {
  key: TAG.WEEK,
  name: '7天后',
  color: 'green',
  value: moment().add(7, 'day'),
})
tagConfig.set(TAG.MONTH, {
  key: TAG.MONTH,
  name: '30天后',
  color: 'green',
  value: moment().add(30, 'day'),
})

type Iprops = {
  onClick?: (targetDate: moment.Moment) => void
  tags?: TAG[]
}

const ExtraDateFooter: React.FC<Iprops> = ({ onClick, tags = [] }) => {
  return (
    <div>
      {tags.map((item) => {
        const config = tagConfig.get(item)
        if (config) {
          return (
            <Tag
              className={style.tag}
              key={config.key}
              onClick={() => onClick?.(config.value)}
              color={config.color}
            >
              {config.name}
            </Tag>
          )
        }
        return null
      })}
    </div>
  )
}

ExtraDateFooter.defaultProps = {
  tags: [TAG.TODAY, TAG.TOMORROW, TAG.WEEK, TAG.MONTH],
}

export default ExtraDateFooter
