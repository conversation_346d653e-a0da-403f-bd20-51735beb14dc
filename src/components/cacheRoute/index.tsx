import React, { useMemo, useRef } from 'react'
import {
  autoFixContext,
  AliveScope,
  useAliveController,
  useActivate,
  useUnactivate,
} from 'react-activation'
import keepAlive from './hoc/keepAlive'
import CacheRoute from './CacheRoute'
import ClearCache from './ClearCache'
import CacheContext from './context'
import CacheContainer from './CacheRoute/CacheContainer'

autoFixContext(
  [require('react/jsx-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
  [require('react/jsx-dev-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
)

const CacheScope = (props) => {
  // 用于存储缓存页面与清除缓存方法的映射
  const pathCacheMap = useRef({})

  // 用于设置缓存页面与清除缓存方法的映射
  const setPathCacheMapFunc = (path: string, clearCacheMethod: string): void => {
    pathCacheMap.current[path] = clearCacheMethod
  }

  const contextValue = useMemo(
    () => ({
      pathCacheMap,
      setPathCacheMapFunc,
    }),
    [pathCacheMap],
  )

  return (
    <AliveScope>
      <CacheContext.Provider value={contextValue}>
        <ClearCache>{props.children}</ClearCache>
      </CacheContext.Provider>
    </AliveScope>
  )
}

const CacheRoute5 = CacheRoute

export {
  CacheScope,
  CacheRoute5,
  CacheContainer,
  useActivate,
  useUnactivate,
  useAliveController,
  keepAlive,
}
