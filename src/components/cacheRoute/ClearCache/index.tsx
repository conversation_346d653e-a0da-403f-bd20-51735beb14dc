import { useContext, useLayoutEffect, useRef } from 'react'
import { useLocation } from 'react-router-dom'
import { useAliveController } from 'react-activation'
import CacheContext from '../context'

function isParentRoute(parentRoute, childRoute) {
  return childRoute.startsWith(parentRoute)
}

function isSubRoute(childRoute, parentRoute) {
  return childRoute.startsWith(`${parentRoute}/`)
}

const ClearCache = ({ children }) => {
  const { pathCacheMap } = useContext(CacheContext)
  const location = useLocation()
  const prePathname = useRef<string>(location.pathname)
  const { clear, refreshScope } = useAliveController()

  useLayoutEffect(() => {
    const currentPathCacheMap = pathCacheMap?.current
    if (
      isParentRoute(location.pathname, prePathname.current) ||
      isSubRoute(location.pathname, prePathname.current)
    ) {
      // console.log('不清缓存')
    } else if (currentPathCacheMap?.[location.pathname] === 'DEFAULT') {
      ;(async () => {
        await refreshScope(location.pathname)
        await clear()
      })()
    }
    prePathname.current = location.pathname
  }, [location])

  return children
}

export default ClearCache
