import React, { useContext, useLayoutEffect } from 'react'
import keepAlive from '../hoc/keepAlive'
import CacheContext from '../context'

interface CacheRouteProps {
  path: string
  cache:
    | boolean
    | {
        clearCacheMethod: 'DEFAULT' | 'CUSTOM'
      }
  children?: React.ReactNode
}
const CacheContainer = ({ path, cache, children }: CacheRouteProps) => {
  const { setPathCacheMapFunc } = useContext(CacheContext)
  useLayoutEffect(() => {
    if (cache && path) {
      setPathCacheMapFunc(path, typeof cache === 'boolean' ? 'DEFAULT' : cache?.clearCacheMethod)
    }
  }, [cache, path, setPathCacheMapFunc])

  return <> {children} </>
}

export default keepAlive(CacheContainer)
