import React from 'react'
import { Route } from 'react-router-dom'
import CacheContainer from './CacheContainer'

interface CacheRouteProps {
  path: string
  cache: boolean | { clearCacheMethod: 'DEFAULT' | 'CUSTOM' }
  component?: React.ComponentType<any>
  render?: (props: any) => React.ReactNode
  [key: string]: any
}

const MyRoute = (props) => {
  return <Route {...props} />
}

const CacheRoute = ({ path, cache, component, render, ...rest }: CacheRouteProps) => {
  return (
    <MyRoute
      {...rest}
      path={path}
      render={
        component || render
          ? (props: any) => {
              if (component) {
                const Component = component
                return (
                  <CacheContainer cache={cache} path={path}>
                    <Component {...props} />
                  </CacheContainer>
                )
              }
              if (render) {
                return (
                  <CacheContainer cache={cache} path={path}>
                    {render(props)}
                  </CacheContainer>
                )
              }
              return <div />
            }
          : undefined
      }
    />
  )
}

export default CacheRoute
