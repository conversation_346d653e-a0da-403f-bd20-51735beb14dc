import React, { useEffect } from 'react'
import { Dhr<PERSON><PERSON><PERSON> as Container } from '@galaxy/dhr-style'
import { useActivate } from '@/components/cacheRoute'
import { useStore } from '@/stores'

interface IProps {
  /**
   * 路由组件
   */
  children: any
  /**
   * 返回按钮
   */
  needBack?: any
  /**
   * 通过路由名称传入的默认 title
   */
  defaultTitle?: React.ReactNode
}

const DHRContainer: React.FC<IProps> = function ({ children, defaultTitle, needBack }: IProps) {
  const store = useStore((state) => state)
  const extraElement = useStore((state) => state.extra)
  const { setTitle, setBack, title } = store

  useEffect(() => {
    setBack(needBack)
    setTitle(React.isValidElement(defaultTitle) ? defaultTitle : <span>{defaultTitle}</span>)
  }, [defaultTitle])

  useActivate(() => {
    // 命中缓存
    setTitle(defaultTitle)
  })

  return (
    <Container
      // todo
      title={title}
      needBack={needBack}
      extra={extraElement}
      unableToSticky
      noStyle
    >
      {children}
    </Container>
  )
}

export default DHRContainer
