/**
 * 入职办理地简称组件
 */
import React, { useEffect, useState } from 'react'
import { Select, Input } from 'antd'
import classNames from 'classnames'
import style from './style.module.less'

// 规则抽象
export const EntryPlaceAbbreviationValidator = (
  rule,
  value,
  callback,
  messages = ['请选择', '请输入'],
) => {
  if (!value || (Array.isArray(value) && !value[0])) {
    return callback(messages[0])
  }
  if (Array.isArray(value) && value.length === 2 && !value[1]) {
    return callback(messages[1])
  }
  return callback()
}

type OptionType = {
  label: string
  value: string | number
  isRemark?: boolean
}
interface IProps {
  options: OptionType[] | Function
  onChange?: (value: string[]) => void
  value: string[]
}
// 组件
const EntryPlaceAbbreviation: React.FC<IProps> = ({
  options = [],
  onChange,
  value: passValue = [],
  ...restProps
}) => {
  const [showInput, setShowInput] = useState<boolean>(false)
  const [selfOptions, setSelfOptions] = useState<OptionType[]>([])

  useEffect(() => {
    const determineShowInput = async () => {
      // eslint-disable-next-line no-nested-ternary
      const items = Array.isArray(options)
        ? options
        : typeof options === 'function'
        ? await options()
        : []
      const shouldShowInput = items.some((item) => item.isRemark && item.value === passValue?.[0])
      setShowInput(shouldShowInput)
      setSelfOptions(items)
    }
    determineShowInput()
  }, [options, passValue])

  return (
    <div className={classNames(style.entryPlaceContainer, showInput ? style.hasInput : '')}>
      <Select
        allowClear
        className={style.selectComponent}
        value={passValue?.[0]}
        onChange={(value) => {
          if (onChange) {
            if (selfOptions.some((item) => item.isRemark && item.value === value)) {
              onChange([value, passValue?.[1]])
            } else {
              onChange([value])
            }
          }
        }}
        {...restProps}
      >
        {selfOptions.map((item) => (
          <Select.Option key={item.value} value={item.value}>
            {item.label}
          </Select.Option>
        ))}
      </Select>
      {showInput && (
        <Input
          className={style.inputComponent}
          value={passValue?.[1]}
          placeholder="请输入"
          maxLength={20}
          onChange={(e) => {
            if (onChange) {
              onChange([passValue?.[0], e.target.value])
            }
          }}
        />
      )}
    </div>
  )
}

export default React.memo(EntryPlaceAbbreviation)
