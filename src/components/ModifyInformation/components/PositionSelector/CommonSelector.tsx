/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useState, useEffect, useRef } from 'react'
import { Spin, Select, Tooltip } from 'antd'
import { TooltipPlacement } from 'antd/es/tooltip'
import { inpLimit } from '@/utils/utils'
// import style from './style.less'

interface IProps {
  mode?: string // 表单模式 树形下拉框结构 selectTree 或下拉框 select
  fetchOption: (searchValue, page, size) => any // 数据源获取事件 返回一个 {count:num,data:[]} 翻页由内部控制
  limit?: number // 单页条数
  valueWord?: string // value字段映射
  keyWord?: string // key字段映射
  className?: any // className 对象
  onChange?: (node, e, val) => any // onchange
  expandProps?: any // 拓展属性 -- 直接作用于 表单
  inpRef?: any // ref对象
  delay?: number // 防抖响应延迟
  changeKey?: any // 重新加载数据的key 变化修改
  oneTime?: any // 是否一次性加载完成
  changeFlag?: any // 修改的标志 触发重新拉取新数据
  getListData?: (listData) => any // 传递回数据
  searchMaxLength?: number
  fullNameMap?: string // 显示全称的字段名称 有的话鼠标hover显示全称
  placement?: TooltipPlacement
}
const { Option } = Select
// 未完 --- 目前只有select模式
const OrganizationalSelectorTree = (props: IProps) => {
  const {
    fetchOption,
    limit,
    valueWord,
    keyWord,
    onChange,
    expandProps,
    inpRef,
    delay,
    changeKey,
    oneTime,
    changeFlag,
    getListData,
    searchMaxLength,
    fullNameMap,
    placement,
  } = props
  const timeRef = useRef<any>()
  const [page, setPage] = useState(1)
  const [init, setInit] = useState(false)
  const [size] = useState(limit || 20)
  const [searchValue, setSearchValue] = useState<any>(expandProps.defaultValue || null)
  const [selectedValue, setSelectedValue] = useState<any>(expandProps.defaultValue || null)
  const [loading, setLoading] = useState(false)
  const [listData, setListData] = useState<any>([])
  const [more, setMore] = useState(false)
  const [changeState] = useState(changeKey)

  const SearchMore = async () => {
    // 滚动加载
    setLoading(true)

    const res = await fetchOption(searchValue, page, size)
    if (res.count <= page * size) {
      setMore(false)
    } else {
      setMore(true)
    }
    const data = res.data
      ? res.data.map((item) => ({
          ...item,
          id: keyWord ? item[keyWord] : undefined,
          value: valueWord ? item[valueWord] : undefined,
        }))
      : []
    setListData([...listData, ...data])
    setLoading(false)
  }

  const loadList = async (val, nowPage) => {
    // 数据,状态控制
    setSearchValue(val)
    setLoading(true)
    const res = await fetchOption(val, nowPage, size)
    if (res.count <= nowPage * size) {
      setMore(false)
    } else {
      setMore(true)
    }
    const data = res.data.map((item) => ({
      ...item,
      key: JSON.stringify(item),
      id: keyWord ? item[keyWord] : undefined,
      value: valueWord ? item[valueWord] : undefined,
    }))
    setListData([...data])
    setLoading(false)
  }

  useEffect(() => {
    loadList(null, page)
  }, [changeFlag])
  useEffect(() => {
    getListData?.(listData)
  }, [listData])
  useEffect(() => {
    if (changeKey !== changeState) {
      setSelectedValue(null)
      loadList(null, page)
    }
  }, [changeKey])
  useEffect(() => {
    if (!init) {
      setInit(true)
    } else {
      SearchMore()
    }
  }, [page, size])

  const searchList = (res) => {
    // 搜索防抖
    let val = res
    if (searchMaxLength) {
      val = inpLimit(val, searchValue, searchMaxLength)
    }
    setPage(1)
    if (!val) {
      setSearchValue(null)
      // setTreeData(cacheTree);
      loadList(val, 1)
      clearTimeout(timeRef.current)
      timeRef.current = null
      return
    }
    if (timeRef.current) {
      clearTimeout(timeRef.current)
      timeRef.current = null
    }
    timeRef.current = setTimeout(() => {
      loadList(val, 1)
    }, delay || 500)
    setSearchValue(val)
  }

  const changeFun = (val, e: any) => {
    // change事件
    if (onChange) {
      onChange(e?.node || {}, e, val)
    }
    if (Object.prototype.toString.call(e) === '[object Array]') {
      setSelectedValue(e.map((item) => item.value))
    } else {
      setSelectedValue(val)
    }
    if (!val) {
      if (!oneTime) {
        searchList('')
      }
    }
  }

  const dropdownRender = (
    originNode, // 下拉展示
  ) => (
    <Spin spinning={loading}>
      {originNode}
      {!more && listData.length >= (limit || 20) && (
        <div style={{ textAlign: 'center', cursor: 'pointer', color: '#ccc' }}>没有更多了</div>
      )}
    </Spin>
  )

  const onPopupScroll = (e) => {
    // 滚动事件
    if (oneTime) {
      return
    }
    if (
      more &&
      !loading &&
      e.currentTarget.scrollTop + e.currentTarget.clientHeight + 20 > e.currentTarget.scrollHeight
    ) {
      setPage(page + 1)
    }
  }
  return (
    <Select
      showSearch
      ref={inpRef}
      value={selectedValue}
      // className={`${style.organizationalSelectorTree} ${className}`}
      onSearch={
        oneTime
          ? (_) => {
              /** */
            }
          : (val) => searchList(val)
      }
      searchValue={searchValue}
      placeholder="请选择组织节点属性"
      filterOption={
        oneTime
          ? (input, option: any) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          : false
      }
      allowClear
      onPopupScroll={onPopupScroll}
      onChange={changeFun}
      dropdownRender={dropdownRender}
      {...expandProps}
    >
      {listData.map((item: any) => (
        <Option key={item.key} node={item} value={item.id} disabled={item.disabled}>
          {fullNameMap ? (
            <Tooltip placement={placement} title={item[fullNameMap]}>
              <div style={{ width: '100%' }}>
                {item.value}
                {item.optionExtra ? item.optionExtra : null}
              </div>
            </Tooltip>
          ) : (
            item.value
          )}
        </Option>
      ))}
    </Select>
  )
}
OrganizationalSelectorTree.defaultProps = {
  className: {},
  valueWord: 'label',
  keyWord: 'id',
  limit: 20,
  fullNameMap: '',
  onChange: () => null,
  expandProps: { defaultValue: [] },
  inpRef: null,
  delay: 500,
  changeKey: null,
  oneTime: false,
  getListData: () => false,
  searchMaxLength: Infinity,
  changeFlag: null,
  placement: 'bottom',
}
export default OrganizationalSelectorTree
