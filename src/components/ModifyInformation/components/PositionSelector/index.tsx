import React from 'react'
import commonService from '@/services/common'
import OrganizationalSelector from './CommonSelector'

export default function PostSelector(props: any) {
  // eslint-disable-next-line react/prop-types
  const { departmentId, onChange, getListData } = props
  const getPositionFun = (value, page, size) => {
    return commonService.getPositionList({
      page,
      size,
      nodeIds: departmentId ? [departmentId] : [],
      nameLike: value,
      isDeleted: false,
    })
  }

  return (
    <OrganizationalSelector
      className="position__box"
      mode="select"
      expandProps={{
        placeholder: '请选择实体岗位',
        value: props?.value,
        labelInValue: true,
        disabled: !departmentId,
      }}
      {...props}
      fetchOption={getPositionFun}
      valueWord="name"
      fullNameMap="fullName"
      keyWord="id"
      searchMaxLength={40}
      changeFlag={departmentId}
      onChange={onChange}
      getListData={getListData}
      placement="right"
    />
  )
}
