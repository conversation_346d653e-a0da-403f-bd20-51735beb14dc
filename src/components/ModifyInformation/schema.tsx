/* eslint-disable max-lines-per-function */
/**
 * @name 修改信息
 */
import React from 'react'
import { Observer, NOT_ACTION } from '@amazebird/antd-schema-form'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'
import { TreeSelect as Dict } from '@galaxy/dict'
import moment from 'moment'
import commonService from '@/services/common'
import employService from '@/services/employ'
import { ENTRY_PLACE_NAME_TYPE, FOURTH_DEPARTMENT_CATEGORY } from '@/constants'
import EntryPlaceAbbreviation, {
  EntryPlaceAbbreviationValidator,
} from './components/EntryPlaceAbbreviation'
import PositionSelector from './components/PositionSelector'

const EMPLOYEE_TYPE_MAP = {
  INTERNSHIP: '1', // 实习
  PROBATION_PERIOD: '2', // 试用
  REGULAR: '3', // 正式
  SOCIAL_PART_TIME: '4', // 社会兼职
  STUDENT_PART_TIME: '5', // 学生兼职
  COOPERATIVE_PART_TIME: '6', // 合作兼职
}

type SHCEMA_TYPE = {
  isVirtualPosition?: boolean
  isCampus?: boolean
  fourthDepartmentCategory?: number
  fourthDepartmentName?: string
  formInstance: any
}

interface QueryEntryHandleConfig {
  entryAreaCode: string // 入职办理地区
  entryPlaceAddress: string // 入职办理地点
  entryPlaceName: string // 入职办理地简称
  isStore: 0 | 1 // 是否门店
  id?: number
}

export const schema = ({
  isVirtualPosition,
  isCampus,
  fourthDepartmentCategory,
  fourthDepartmentName,
  formInstance,
}: SHCEMA_TYPE) => ({
  entryDepartmentId: {
    label: '报到部门',
    component: (props) => <OrgSelector {...props} />,
    placeholder: '请选择报到部门',
    rules: [{ required: true, message: '请选择报到部门' }],
    // disabled: Observer({
    //   watch: 'entryPositionId',
    //   action: (watchData: any) => {
    //     return !!watchData?.value
    //   },
    // }),
  },
  entryPositionId: {
    label: '报到岗位',
    source: 'update',
    value: Observer({
      watch: 'entryDepartmentId',
      source: 'update',
      action: () => {
        return null
      },
    }),
    component: Observer({
      watch: 'entryDepartmentId',
      action: (watchData: any) => (props) => {
        return (
          <PositionSelector
            departmentId={watchData?.value || undefined}
            {...props}
            onChange={(val) => {
              if (val?.value) {
                props.onChange({ ...val, value: val.id, label: val.name })
              } else {
                props.onChange(null)
              }
            }}
          />
        )
      },
    }),
    placeholder: '请选择报到岗位',
    rules: [{ required: true, message: '请选择报到岗位' }],
  },
  employmentFormCode: {
    label: '用工形式',
    placeholder: '请选择用工形式',
    rules: [{ required: true, message: '请选择用工形式' }],
    value: Observer({
      watch: 'entryPositionId',
      source: 'update',
      action: (watchData, { form, callback, name }) => {
        if (watchData?.partnerCompany) {
          callback(() => form.setFields([{ name, errors: [] }]))
          return '5'
        }
        return null
      },
    }),
    disabled: Observer({
      watch: ['entryPositionId', 'employeeTypeCode'],
      action: (watchData) => {
        // 报到岗位有【合作公司】时，【用工形式】字段默认选择“自营员工第三方合同”
        if (watchData?.[0]?.partnerCompany) {
          return true
        }
        if (watchData?.[1] || (!watchData?.[0]?.value && watchData?.[0]?.value !== 0)) {
          return true
        }

        return false
      },
    }),
    props: Observer({
      watch: 'entryPositionId',
      action: (watchData: any) => {
        return {
          filter: (item) => {
            // .报到岗位没有【合作公司】时，【用工形式】控件允许操作，同时不允许选择“自营员工第三方合同”（选项置灰）；
            if (!watchData?.partnerCompany && !watchData?.partnerCompany && item?.value === '5') {
              return false
            }
            return true
          },
        }
      },
    }),
    component: (props) => {
      return <Dict allowClear scope="EHR" {...props} code="BM_35" />
    },
  },
  employeeTypeCode: {
    label: '员工类型',
    placeholder: '请选择员工类型',
    rules: [{ required: true, message: '请选择员工类型' }],
    disabled: Observer({
      watch: 'employmentFormCode',
      action: (watchData) => {
        return !watchData
      },
    }),
    value: Observer({
      watch: 'employmentFormCode',
      source: 'update',
      action: () => {
        return null
      },
    }),
    observer: Observer({
      watch: 'employmentFormCode',
      action: (watchData) => {
        return {
          component: (props: any) => (
            <Dict
              scope="EHR"
              allowClear
              {...props}
              filter={({ value }) => {
                // [用工形式]选择“自营员工第三方合同”时
                // [员工类型]可选择“实习”、“试用”、“正式”、“社会兼职”、“学生兼职” “合作兼职”
                if (watchData === '5') {
                  return [
                    EMPLOYEE_TYPE_MAP.INTERNSHIP,
                    EMPLOYEE_TYPE_MAP.PROBATION_PERIOD,
                    EMPLOYEE_TYPE_MAP.REGULAR,
                    EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
                    EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
                    EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
                  ].includes(value)
                }
                // 用工形式】选择“全职”时，【员工类型】可选择“试用”、“正式”，其余选项置灰不允许选择
                if (watchData === '1') {
                  return [EMPLOYEE_TYPE_MAP.PROBATION_PERIOD, EMPLOYEE_TYPE_MAP.REGULAR].includes(
                    value,
                  )
                }
                // 用工形式】选择“兼职”时，【员工类型】可选择“社会兼职”、“学生兼职”，其余选项置灰不允许选择；
                if (watchData === '2') {
                  return [
                    EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
                    EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
                  ].includes(value)
                }
                // 用工形式】选择“实习”时，【员工类型】可选择“实习”，其余选项置灰不允许选择；
                if (watchData === '3') {
                  return [EMPLOYEE_TYPE_MAP.INTERNSHIP].includes(value)
                }
                // 用工形式】选择“项目合作”时，【员工类型】可选择“合作兼职”，其余选项置灰不允许选择
                if (watchData === '4') {
                  return [EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME].includes(value)
                }
                return true
              }}
              code="BM_YGLX"
            />
          ),
        }
      },
    }),
  },
  rankCode: {
    label: '部门职级',
    value: Observer({
      watch: 'employeeTypeCode',
      source: 'update',
      action: (watchData, { form, callback, name }) => {
        let value: string | null = null
        const entryPositionInfo = form.getFieldValue('entryPositionId')
        if (watchData && entryPositionInfo) {
          const areaCode = entryPositionInfo?.areaCode
          const isTech = areaCode === '09'
          const unuseable =
            [
              EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
              EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
              EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
            ].includes(watchData) ||
            isTech ||
            [155, 951].includes(entryPositionInfo?.positionBaseId)

          if (unuseable) {
            callback(() => form.setFields([{ name, errors: [] }]))
            value = '05'
          }
        }

        return value
      },
    }),
    disabled: Observer({
      watch: 'employeeTypeCode',
      action: (watchData, { form }) => {
        const entryPositionInfo = form.getFieldValue('entryPositionId')
        if (watchData && entryPositionInfo) {
          const areaCode = entryPositionInfo?.areaCode
          const isTech = areaCode === '09'
          const unuseable =
            [
              EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
              EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
              EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
            ].includes(watchData) ||
            isTech ||
            [155, 951].includes(entryPositionInfo?.positionBaseId)
          return unuseable
        }
        return !watchData
      },
    }),
    observer: Observer({
      watch: 'employeeTypeCode',
      action: (watchData, { form }) => {
        return {
          component: (props) => {
            return (
              <Dict
                scope="EHR"
                code="BM_ZJ"
                allowClear
                filter={({ value: val }) => {
                  const entryPositionInfo = form.getFieldValue('entryPositionId')
                  if (
                    [
                      EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
                      EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
                      EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
                    ].includes(watchData) ||
                    entryPositionInfo?.areaCode === '09' ||
                    [155, 951].includes(entryPositionInfo?.positionBaseId)
                  ) {
                    return val === '05'
                  }
                  return val !== '05'
                }}
                placeholder="请选择部门职级"
                {...props}
              />
            )
          },
        }
      },
    }),
    rules: [{ required: true, message: '请选择部门职级' }],
  },
  probationPeriod: {
    label: '试用期期限',
    component: 'InputNumber',
    required: true,
    value: Observer({
      watch: 'employeeTypeCode',
      source: 'update',
      action: async (watchData, { form, callback, name }) => {
        const entryPositionInfo = form.getFieldValue('entryPositionId')
        if (
          [
            EMPLOYEE_TYPE_MAP.INTERNSHIP,
            EMPLOYEE_TYPE_MAP.REGULAR,
            EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
            EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
            EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
          ].includes(watchData)
        ) {
          return 0
        }
        let value
        if (watchData === EMPLOYEE_TYPE_MAP.PROBATION_PERIOD) {
          if (!isCampus) {
            if (entryPositionInfo?.value) {
              const data = await commonService.getPeriod(entryPositionInfo?.value)
              value = data ?? 3
            } else {
              value = 3
            }
          } else {
            value = 6
          }
        }
        if (value !== undefined) {
          callback(() => form.setFields([{ name, errors: [] }]))
        }
        return value
      },
    }),
    disabled: Observer({
      watch: ['employeeTypeCode', 'entryPositionId'],
      action: async (watchData) => {
        let disabled = false
        // 【员工类型】为“实习”、“正式”、“社会兼职”、“学生兼职”、“合作兼职”时，【试用期期限】为“0”输入项置灰不允许修改；
        if (
          [
            EMPLOYEE_TYPE_MAP.INTERNSHIP,
            EMPLOYEE_TYPE_MAP.REGULAR,
            EMPLOYEE_TYPE_MAP.SOCIAL_PART_TIME,
            EMPLOYEE_TYPE_MAP.STUDENT_PART_TIME,
            EMPLOYEE_TYPE_MAP.COOPERATIVE_PART_TIME,
          ].includes(watchData?.[0])
        ) {
          disabled = true
        }
        if (!watchData?.[0] || (!watchData?.[1]?.value && watchData?.[1]?.value !== 0)) {
          disabled = true
        }
        return disabled
      },
    }),
    props: {
      min: 0,
      max: 6,
      addonAfter: '个月',
    },
  },
  timeExpectedEntry: {
    label: '预计报到日期',
    component: 'DatePicker',
    rules: [{ required: true, message: '请选择预计报到日期' }],
    placeholder: '请选择预计报到日期',
    props: {
      format: 'YYYY-MM-DD',
      disabledDate: (time) => moment(time).isBefore(moment().startOf('day')),
      style: { width: '100%' },
    },
    value: Observer({
      watch: 'entryPositionId',
      source: 'update',
      action: () => {
        return null
      },
    }),
    disabled: Observer({
      watch: 'entryPositionId',
      action: async (watchData) => {
        if (!isVirtualPosition) {
          return false
        }
        if (!watchData?.value && watchData?.value !== 0) {
          return true
        }
        return false
      },
    }),
  },
  entryCityCode: {
    label: '入职办理地区',
    component: 'Select',
    options: async () => {
      const $value = await commonService.getEhrDictArea()
      return $value
    },
    placeholder: '请选择入职办理地区',
    rules: [{ required: true, message: '请选择入职办理地区' }],
    value: Observer({
      watch: 'entryPositionId',
      source: 'update',
      action: () => {
        return null
      },
    }),
    disabled: Observer({
      watch: 'entryPositionId',
      action: async (watchData) => {
        if (!isVirtualPosition) {
          return false
        }
        if (!watchData) {
          return true
        }
        return false
      },
    }),
  },
  entryPlaceName: {
    label: '入职办理地简称',
    component: (props) => <EntryPlaceAbbreviation {...props} />,
    placeholder: '请选择入职办理地简称',
    options: Observer({
      watch: ['entryCityCode', 'entryPositionId'],
      action: async (watchData) => {
        if (!watchData[0]) {
          return []
        }
        // 其他选项
        const otherItem = {
          label: '其他',
          value: `${ENTRY_PLACE_NAME_TYPE.OTHER}`,
          isRemark: true,
        }
        let data = [] as QueryEntryHandleConfig[]
        let fourthDeptCategory = fourthDepartmentCategory
        let fourthDeptName = fourthDepartmentName
        if (isCampus && watchData[1]) {
          fourthDeptCategory = watchData[1]?.fourthDepartmentCategory
          fourthDeptName = watchData[1]?.fourthDepartmentName
        }
        // 门店
        if (fourthDeptCategory === FOURTH_DEPARTMENT_CATEGORY.STORE) {
          let data2 = []
          const data1 = await employService.getEntryPlace({
            entryAreaCode: watchData[0],
            isStore: 0,
          })
          if (fourthDeptName) {
            data2 = await employService.getEntryPlace({
              entryAreaCode: watchData[0],
              entryPlaceName: fourthDeptName,
            })
          }
          // 合并数据data1, data2并根据entryPlaceName去重
          data = data1
            .concat(data2)
            .filter(
              (item, index, self) =>
                self.findIndex((item1) => item1.entryPlaceName === item.entryPlaceName) === index,
            )
        }

        // 非门店
        if (fourthDeptCategory !== FOURTH_DEPARTMENT_CATEGORY.STORE) {
          data = await employService.getEntryPlace({
            entryAreaCode: watchData[0],
            isStore: 0,
          })
          data = data.filter(
            (item, index, self) =>
              self.findIndex((item1) => item1.entryPlaceName === item.entryPlaceName) === index,
          )
        }

        return data
          .map((item) => ({
            label: item.entryPlaceName,
            value: item.entryPlaceName,
            isRemark: false,
          }))
          .concat([otherItem])
      },
    }),

    rules: [
      { required: true, message: '请选择入职办理地简称' },
      {
        validator: (rule, value, callback) =>
          EntryPlaceAbbreviationValidator(rule, value, callback, [
            '请选择入职办理地简称',
            '请输入办理地简称的具体信息',
          ]),
      },
    ],
    value: Observer({
      watch: ['entryDepartmentId', 'entryPositionId', 'entryCityCode'],
      source: 'update',
      action: () => {
        return null
      },
    }),
    beforeChange: ({ nextValue, preValue, change }) => {
      // 切换前要先清空值
      if (Array.isArray(nextValue) && nextValue.length === 1) {
        formInstance.setFieldsValue({
          entryPlaceAddress: null,
        })
      }
      change(nextValue)
    },
    disabled: Observer({
      watch: 'entryCityCode',
      action: async (watchData) => {
        return !watchData
      },
    }),
  },
  entryPlaceAddress: {
    label: '入职办理地点',
    component: 'Input.TextArea',
    max: 100,
    placeholder: '请输入入职办理地点',
    rules: [{ required: true, message: '请输入入职办理地点' }],
    value: Observer({
      watch: ['entryDepartmentId', 'entryPositionId', 'entryCityCode', 'entryPlaceName'],
      source: 'update',
      action: async (watchData, { form }) => {
        // 其他选项
        if (
          Array.isArray(watchData?.[3]) &&
          watchData[3]?.length === 2 &&
          (watchData[3]?.[1] || watchData[3]?.[1] === '')
        ) {
          return NOT_ACTION
        }
        // 变更入职办理地简称（不包含其他）
        if (
          Array.isArray(watchData[3]) &&
          watchData[3]?.length === 1 &&
          watchData[3]?.[0] &&
          watchData[2]
        ) {
          const data = await employService.getEntryPlace({
            entryAreaCode: watchData[2],
            entryPlaceName: watchData[3]?.[0],
          })
          const $entryPlaceName = form.getFieldValue('entryPlaceName')
          const $entryCityCode = form.getFieldValue('entryCityCode')
          if (
            Array.isArray($entryPlaceName) &&
            $entryPlaceName?.length === 1 &&
            $entryPlaceName?.[0] &&
            $entryCityCode
          ) {
            // 处理异步问题
            return data[0]?.entryPlaceAddress
          }
          return NOT_ACTION
        }
        return null
      },
    }),
    disabled: Observer({
      watch: 'entryPlaceName',
      action: async (watchData) => {
        if (Array.isArray(watchData) && watchData.length === 2) {
          return false
        }
        return true
      },
    }),
    beforeChange: ({ nextValue, preValue, change }) => {
      if (nextValue.length > 100) {
        change(nextValue.slice(0, 100))
      } else {
        change(nextValue)
      }
    },
  },
})
