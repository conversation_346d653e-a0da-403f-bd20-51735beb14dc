/**
 * @name 修改信息
 */
import React, { useEffect, useImperativeHandle, useState } from 'react'
import { Modal, Spin, message } from 'antd'
import employService from '@/services/employ'
import { Item, SchemaForm } from '@amazebird/antd-schema-form'
import { createAction } from '@amazebird/antd-schema-table'
import '@amazebird/antd-field'
import dmsService from '@/services/dms'
import { EDIT_OFFER_INFO } from '@/constants/dms/eventName'
import { ENTRY_PLACE_NAME_TYPE } from '@/constants'
import { schema as getSchema } from './schema'

type DataType = {
  entryDepartmentId?: {
    value: number
    label: string
  }
  entryPositionId?: {
    value: number
    label: string
    positionBaseId: number
    areaCode: string
    partnerCompany: string
    fourthDepartmentCategory: number
    fourthDepartmentName: string
  }
  employmentFormCode?: string
  employeeTypeCode?: string
  rankCode?: string
  probationPeriod?: number
  timeExpectedEntry?: number
  entryCityCode?: string
  recruitTypeCode?: string
  entryPlaceName?: string[]
  entryPlaceAddress?: string
  fourthDepartmentCategory?: number
  fourthDepartmentName?: string
}

export type ActionType = {
  open: () => void
}

interface Iprops {
  action: ReturnType<typeof createAction>
  dataSource: DataType
  entryMaterialId: number
  isCampus: boolean
  isVirtualPosition: boolean
  validateErrorMsg?: string
}

const ModifyInformationModal: React.ForwardRefExoticComponent<
  React.PropsWithoutRef<Iprops> & React.RefAttributes<ActionType>
> = React.forwardRef<ActionType, Iprops>(
  ({ action, dataSource, entryMaterialId, isCampus, isVirtualPosition, validateErrorMsg }, ref) => {
    const [open, setOpen] = useState<boolean>(false)
    const form = SchemaForm.createForm()

    useImperativeHandle(ref, () => ({
      open: () => {
        setOpen(true)
        form?.resetFields?.()
      },
    }))
    const [loading, setLoading] = useState(false)

    const schema = getSchema({
      isVirtualPosition,
      isCampus,
      fourthDepartmentCategory: dataSource.fourthDepartmentCategory,
      fourthDepartmentName: dataSource.fourthDepartmentName,
      formInstance: form,
    })

    const onOk = async () => {
      setLoading(true)
      form
        .validateFields()
        .then((value) => {
          const data = {
            departmentId: value.entryDepartmentId?.value,
            employmentFormCode: value.employmentFormCode,
            employmentTypeCode: value.employeeTypeCode,
            entryCityCode: value.entryCityCode,
            positionId: value.entryPositionId?.value,
            postRankCode: value.rankCode,
            timeEntry: value.timeExpectedEntry.valueOf(),
            trialPeriod: value.probationPeriod,
            entryPlaceName:
              value.entryPlaceName.length === 2 ? value.entryPlaceName[1] : value.entryPlaceName[0],
            entryPlaceNameType:
              value.entryPlaceName.length === 2
                ? ENTRY_PLACE_NAME_TYPE.OTHER
                : ENTRY_PLACE_NAME_TYPE.IN_CONFIGURATION,
            entryPlaceAddress: value.entryPlaceAddress,
          }

          return employService
            .modifyInfo(entryMaterialId, data, {
              noMessage: true,
            })
            .then(() => {
              dmsService.trace(EDIT_OFFER_INFO, {
                recruitType: dataSource.recruitTypeCode,
              })
              message.success('修改成功')
              setOpen(false)
              action?.refresh?.()
            })
            .catch((error) => {
              if (error?.code === 'ENTRY/CHANGE_EMPLOYMENT_EMPLOYMENT_VALID_NOT_PASS') {
                setOpen(false)
                Modal.error({
                  title: '录用信息校验不通过',
                  content: validateErrorMsg,
                })
              } else {
                message.error(error?.msg)
              }
            })
        })
        .finally(() => {
          setLoading(false)
        })
    }
    return (
      <Modal
        // className={styles.ModifyInformationModal}
        title="修改信息"
        // closable={false}
        width="485px"
        destroyOnClose
        maskClosable={false}
        // onCancel={close}
        onOk={onOk}
        open={open}
        confirmLoading={loading}
        onCancel={() => {
          setOpen(false)
        }}
      >
        <Spin spinning={loading}>
          <div style={{ minHeight: 100 }}>
            {dataSource && (
              <SchemaForm
                schema={schema}
                form={form}
                wrapperCol={{ sm: { span: 16 } }}
                initialValues={dataSource}
              >
                {(isVirtualPosition || isCampus) && (
                  <>
                    <Item field="entryDepartmentId" />
                    <Item field="entryPositionId" />
                    <Item field="employmentFormCode" />
                    <Item field="employeeTypeCode" />
                    <Item field="rankCode" />
                    <Item field="probationPeriod" />
                  </>
                )}
                <Item field="timeExpectedEntry" />
                <Item field="entryCityCode" />
                <Item field="entryPlaceName" />
                <Item field="entryPlaceAddress" />
              </SchemaForm>
            )}
          </div>
        </Spin>
      </Modal>
    )
  },
)

export default ModifyInformationModal
