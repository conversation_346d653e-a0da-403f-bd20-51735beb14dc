/**
 * @name 弹窗
 */
import {
  CheckCircleFilled,
  CloseCircleFilled,
  ExclamationCircleFilled,
  InfoCircleFilled,
} from '@ant-design/icons'
import { Modal, ModalFuncProps, ModalProps } from 'antd'
import React, { useState } from 'react'
import classNames from 'classnames'
import { ModalStaticFunctions } from 'antd/es/modal/confirm'
import style from './style.module.less'

const configFn = (config: ModalFuncProps, name: string) => {
  const { className, content, title, ...restConfig } = config
  let icon: React.ReactNode
  switch (name) {
    case 'info':
      icon = <InfoCircleFilled className={style.modal__icon} />
      break
    case 'error':
      icon = <CloseCircleFilled className={style.modal__icon} />
      break
    case 'success':
      icon = <CheckCircleFilled className={style.modal__icon} />
      break
    default:
      icon = <ExclamationCircleFilled className={style.modal__icon} />
      break
  }
  return Modal[name]({
    className: classNames(style.modal, className),
    content: <div className={style.modal__content}>{content}</div>,
    icon,
    title: <span className={style.modal__title}>{title}</span>,
    ...restConfig,
  })
}

const ReModal: React.FC<ModalProps> & ModalStaticFunctions = (props) => {
  const [loading, setLoading] = useState<boolean>(false)
  return (
    <Modal
      maskClosable={false}
      {...props}
      onOk={async (e) => {
        setLoading(true)
        try {
          await props.onOk?.(e)
        } catch (error) {
          console.log(error)
        }
        setLoading(false)
      }}
      okButtonProps={{ loading, ...props.okButtonProps }}
    />
  )
}

ReModal.confirm = (config: ModalFuncProps) => configFn(config, 'confirm')
ReModal.warning = (config: ModalFuncProps) => configFn(config, 'warning')
ReModal.warn = (config: ModalFuncProps) => configFn(config, 'warning')
ReModal.info = (config: ModalFuncProps) => configFn(config, 'info')
ReModal.error = (config: ModalFuncProps) => configFn(config, 'error')
ReModal.success = (config: ModalFuncProps) => configFn(config, 'success')

export default ReModal
