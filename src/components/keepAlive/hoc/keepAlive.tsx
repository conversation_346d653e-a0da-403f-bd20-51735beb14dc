import React from 'react'
import KeepAlive from 'react-activation'

type Iprops = {
  cache?: string | false
  [props: string]: any
}

export default (WrappedComponent, staticCache = false) => {
  const keepAlive = ({ cache, ...restProps }: Iprops) => {
    if (typeof cache === 'string' || staticCache) {
      const cacheKey = typeof cache === 'string' ? cache : undefined
      const name = typeof cache === 'string' ? cache : undefined
      const when = typeof cache === 'string' || staticCache
      return (
        <KeepAlive
          cacheKey={cacheKey}
          name={name}
          when={when}
          style={{ height: '100%' }}
          autoFreeze={false}
        >
          <WrappedComponent {...restProps} />
        </KeepAlive>
      )
    }
    return <WrappedComponent {...restProps} />
  }
  return keepAlive
}
