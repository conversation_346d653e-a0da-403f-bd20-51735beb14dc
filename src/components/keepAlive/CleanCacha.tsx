import { useLayoutEffect, useRef } from 'react'
import { useLocation, Location } from 'react-router-dom'
import { useAliveController } from 'react-activation'
import useHistory from '@/hooks/useHistory'

// 获取pathname 前两个路径
const getFirstPath = (pathname?: string) => {
  if (!pathname) {
    return ''
  }
  const pathArr = pathname.split('/')
  return `/${pathArr[1]}/${pathArr[2]}`
}

const CleanCache = ({ children }) => {
  const location = useLocation()
  const history = useHistory()
  // 保留上一次路由跳转的信息
  const prevLoction = useRef<Location>()

  const { clear, refreshScope } = useAliveController()

  useLayoutEffect(() => {
    if (getFirstPath(prevLoction.current?.pathname) !== getFirstPath(location.pathname)) {
      // 清除缓存
      clear()
    }
    // 更新路由信息
    prevLoction.current = location
  }, [location])

  useLayoutEffect(() => {
    if (history.action === 'PUSH') {
      refreshScope(location.pathname)
    }
  }, [history])

  return children
}

export default CleanCache
