## 使用

```jsx
// 业务代码中，在不会被销毁的位置放置 <AliveScope> 外层，一般为应用入口处
import { AliveScope } from '@/components/keepAlive'
<AliveScope>
    <App />
</AliveScope>

// 由于缓存对 Context 的破坏性影响需手动在入口处打个补丁
// 注：如接主应用需加以判断 window.__POWERED_BY_QIANKUN__
import { autoFixContext } from '@/components/keepAlive'
if (!window.__POWERED_BY_QIANKUN__) {
  autoFixContext(
    [require('react/jsx-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
    [require('react/jsx-dev-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
  )
}

// 场景需求：点击菜单栏需清除全部页面缓存可使用clearn
import { useAliveController } from '@/components/keepAlive'
const { clear } = useAliveController()

<Menu.Item onClick={()=>{clear()}}>菜单</Menu.Item>

// 各模块的使用
// 通过keepAlive包裹的组件暴露useActivate、useUnactivate
import { keepAlive } from '@/components/keepAlive'
function Component({
  useActivate,
  useUnactivate,
}){
  // 缓存页面进入（初始化不会执行，只会在缓存后再次进入执行）
  useActivate(() => {
    ...
  })

  useUnactivate(() => {
   ...
  })
  return (
    <div>模块..</div>
  )
}

export default keepAlive(Component,true)

```

⚠️ 注意
对严格依赖生命周期顺序的功能造成一定影响，再做有销毁操作场景的时候如果问题需排查