import React from 'react'
import classNames from 'classnames'
import { WaterMark as AntdWaterMark } from '@ant-design/pro-layout'
import { generateWaterMaskContent } from '@/utils/utils'

import styles from './style.module.less'

interface FileProps {
  docUrl: string
  docWidth?: number
  docHeight?: number
  openWaterMark?: boolean
  style?: React.CSSProperties
}

const DocPreview: React.FC<FileProps> = ({
  docUrl,
  openWaterMark,
  docWidth = 634,
  docHeight = 890,
  style,
}) => {
  return (
    <div
      className={styles.doc_iframe}
      style={{ ...style, height: docHeight - 23, width: docWidth }}
    >
      {/* @ts-ignore */}
      <AntdWaterMark content={openWaterMark ? generateWaterMaskContent() : null}>
        <iframe
          src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(docUrl)}`}
          width={`${docWidth}px`}
          id="doc-iframe"
          height={`${docHeight}px`}
          title="Document Preview"
          className={classNames(styles.no_border)}
        />
      </AntdWaterMark>
    </div>
  )
}

export default DocPreview
