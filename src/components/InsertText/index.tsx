import React, { useMemo } from 'react'
import { Tag } from 'antd'
import { getZhLen } from '@/utils/utils'
import style from './style.module.less'

// 字符串插入dom，核心方法
const insertText = (dom: HTMLTextAreaElement, str: string): string => {
  const value = dom.value
  const result =
    value.substring(0, dom.selectionStart) + str + value.substring(dom.selectionEnd, value.length)
  return result
}

// 光标位置定位
const locateCursor = (dom: HTMLTextAreaElement, tagText: string): number => {
  if (!dom.value) {
    return tagText.length
  }
  if (typeof dom.selectionStart === 'number') {
    return dom.selectionStart + tagText.length
  }
  return dom.value.length + tagText.length
}

type Iprops = {
  getContainer: () => HTMLTextAreaElement
  tags: { value: string; label: string }[]
  onInsert?: (text: string) => void
}

const InsertText: React.FC<Iprops> = ({ getContainer, tags, onInsert }) => {
  return (
    <div className={style.insertText}>
      {tags.map((item) => (
        <Tag
          className={style.tag}
          key={item.value}
          onClick={() => {
            const container = getContainer()
            if (container) {
              const text = insertText(container, item.value)
              if (getZhLen(text) > 1000) {
                return
              }
              const locatePos = locateCursor(container, item.value)
              container.value = text
              container.selectionStart = locatePos
              container.selectionEnd = locatePos
              onInsert?.(text)
              container.focus()
            }
          }}
        >
          {item.label}
        </Tag>
      ))}
    </div>
  )
}

export default InsertText
