/**
 * PdfPreview pdf预览组件
 */
import React, { useState, useRef, useMemo } from 'react'
import { InputNumber } from 'antd'
import { WaterMark as AntdWaterMark } from '@ant-design/pro-layout'
import { Document, Page, pdfjs } from 'react-pdf'
import { generateWaterMaskContent } from '@/utils/utils'
import {
  LeftOutlined,
  RightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons'
import { useThrottleFn, useFullscreen } from 'ahooks'
import styles from './style.module.less'

// 上传对应pdfjs的worker版本到cdn，升级版本需重新上传对应worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://felib.pupumall.com/fe/libs/pdfjs-dist@3.11.174/pdf.worker.min.js`
interface FileProps {
  pdfUrl: string
  pdfWidth?: number
  openWaterMark?: boolean
}

const PdfPreview: React.FC<FileProps> = ({ pdfUrl, openWaterMark, pdfWidth = 634 }) => {
  const ref = useRef(null)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [numPages, setNumPages] = useState<number>(0)
  const [pageWidth, setPageWidth] = useState<number>(pdfWidth as number)

  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(ref)

  const onDocumentLoadSuccess = (params): void => {
    const { numPages: pages } = params._pdfInfo
    setNumPages(pages)
  }

  const handleParser = (value): number => {
    if (!value) {
      return 1
    }
    const parsedValue = parseInt(value, 10)
    return parsedValue || pageNumber
  }

  const handleFormatter = (value): string => {
    const nValue = Number(value)
    if (nValue <= 1) {
      return '1'
    }
    if (nValue > numPages) {
      return `${numPages}`
    }
    return `${nValue}`
  }

  const { run: handleInputChange } = useThrottleFn(
    (value) => {
      if (value !== undefined) {
        setPageNumber(value)
      }
    },
    { wait: 500 },
  )

  const lastPage = (): void => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1)
    }
  }

  const nextPage = (): void => {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1)
    }
  }

  const pageZoomIn = (): void => {
    setPageWidth((width) => {
      return width + 100
    })
  }

  const pageZoomOut = (): void => {
    setPageWidth((width) => {
      if (width > pdfWidth) {
        return width - 100
      }
      return width
    })
  }

  // pdf options
  const pdfOptions = useMemo(
    () => ({
      cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.7.570/cmaps/', // 解决预览部分字体不展示问题
      cMapPacked: true, // 设置为 true，只加载文档中用到的字体子集
    }),
    [],
  )

  return (
    <div
      ref={ref}
      className={styles.pdf_wrapper}
      style={{ justifyContent: !isFullscreen ? 'flex-start' : 'center' }}
    >
      <div className={styles.page_tool} style={{ width: !isFullscreen ? pdfWidth : '1300px' }}>
        <div className={styles.page_tool_item} onClick={lastPage}>
          <LeftOutlined />
        </div>
        <div>
          <InputNumber
            className={styles.input_number}
            size="small"
            min={1}
            max={numPages}
            defaultValue={1}
            value={pageNumber}
            parser={handleParser}
            formatter={handleFormatter}
            onChange={handleInputChange}
          />
          {'  '}/ {numPages}
        </div>
        <div className={styles.page_tool_item} onClick={nextPage}>
          <RightOutlined />
        </div>
        {!isFullscreen && (
          <div className={styles.page_tool_item} onClick={pageZoomIn}>
            <ZoomInOutlined />
          </div>
        )}
        {!isFullscreen && (
          <div className={styles.page_tool_item} onClick={pageZoomOut}>
            <ZoomOutOutlined />
          </div>
        )}
        {!isFullscreen && (
          <div className={styles.page_tool_item} onClick={enterFullscreen}>
            <FullscreenOutlined />
          </div>
        )}
        {isFullscreen && (
          <div className={styles.page_tool_item} onClick={exitFullscreen}>
            <FullscreenExitOutlined />
          </div>
        )}
      </div>
      <div className={styles.container}>
        <Document
          file={pdfUrl}
          loading="加载中..."
          onLoadSuccess={onDocumentLoadSuccess}
          options={pdfOptions}
        >
          {/* @ts-ignore */}
          <AntdWaterMark content={openWaterMark ? generateWaterMaskContent() : null}>
            <Page
              pageNumber={pageNumber}
              width={!isFullscreen ? pageWidth : 1300}
              loading="加载中..."
              renderAnnotationLayer={false}
              renderTextLayer={false}
            />
          </AntdWaterMark>
        </Document>
      </div>
    </div>
  )
}

PdfPreview.defaultProps = {
  pdfWidth: 634,
}

export default PdfPreview
