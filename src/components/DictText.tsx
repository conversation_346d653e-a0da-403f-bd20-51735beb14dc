import React, { useEffect } from 'react'
import dictService from '@/services/dict'
import { useSafeState } from 'ahooks'

export type DictType =
  | {
      code: string
      dictCode: string
      scope?: string
      fullName?: boolean
    }
  | {
      sets: {
        code: string
        dictCode: string
        scope?: string
        fullName?: boolean
      }[]
      transfer?: (result: string[]) => string | string[]
    }

const emptyText = '--'

type Iprops = {
  extendsText?: string
  text?: DictType
  render?: (text: string | string[]) => React.ReactElement | null
}

const DictText: React.FC<Iprops> = ({ text: value, render, extendsText }) => {
  const [text, setText] = useSafeState<string | string[]>(() => {
    if (typeof value === 'undefined') {
      return emptyText
    }
    if (value && 'sets' in value) {
      return value.sets.length ? value.sets.map((item) => item.code).join('、') : emptyText
    }

    // @ts-ignore
    return value.code
  })

  useEffect(() => {
    if (value && 'sets' in value) {
      if (value.sets.length) {
        ;(async () => {
          const result = await Promise.all(
            value.sets.map((item) => {
              return dictService.codeToName(item.code, item.dictCode, {
                scope: item.scope,
                fullName: item.fullName,
              })
            }),
          )
          setText(value.transfer ? value.transfer(result) : result.join('、'))
        })()
      }
      return
    }
    // @ts-ignore
    if (!value?.code) {
      return
    }
    ;(async () => {
      // @ts-ignore
      const result = await dictService.codeToName(value.code, value.dictCode, {
        // @ts-ignore
        scope: value.scope,
        // @ts-ignore
        fullName: value.fullName,
      })
      setText(result)
    })()
  }, [value])

  if (render) {
    render(`${text}${extendsText || ''}`)
  }

  return (
    <span style={{ width: '100%' }}>
      {text}
      {extendsText || ''}
    </span>
  )
}

export default DictText
