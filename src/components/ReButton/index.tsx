import { Button, ButtonProps } from 'antd'
import React, { useState } from 'react'

type Iprops = ButtonProps & {
  onClick:
    | ButtonProps['onClick']
    | ((e?: React.MouseEvent<HTMLElement, MouseEvent>) => Promise<void>)
}

const ReButton: React.FC<Iprops> = (props) => {
  const { onClick } = props
  const [loading, setLoading] = useState<boolean>(false)

  const handleClick: ButtonProps['onClick'] = async (e) => {
    setLoading(true)
    try {
      await onClick?.(e)
    } catch (error) {
      console.log(error)
    }
    setLoading(false)
  }

  return <Button loading={loading} {...props} onClick={handleClick} />
}

export default ReButton
