import dmsService from '@/services/dms'
import { MODIFY_RESULT } from '@/constants/dms/eventName'
import auditService from '@/services/preEntry/audit'
import { message } from 'antd'
import ReModal from './ReModal'

const ModifyResult = () => {}

ModifyResult.confirm = (config: { id: number; name: string; onOk?: () => void }) => {
  ReModal.confirm({
    title: '修改审核结果',
    content: `确定将${config.name}的审核进度驳回重新初审吗？`,
    onOk: async () => {
      await auditService
        .rejectResult(config.id, {
          noMessage: true,
        })
        .catch((error) => {
          if (error.code === 'ENTRY/STATUS_NOT_ALLOW_OPERATE') {
            message.warn('候选人状态已变化，不可修改审核结果')
            config?.onOk?.()
            return
          }
          message.error(error.msg)
          throw error
        })
      // 提交成功后
      dmsService.trace(MODIFY_RESULT)
      config?.onOk?.()
    },
  })
}

export default ModifyResult
