import React, { useState } from 'react'
import ProLayout, { PageContainer } from '@ant-design/pro-layout'
import config from '@/config'
import { Outlet, useNavigate } from 'react-router-dom'
import RouterClass from '@/services/router'
import WaterMark from './components/WaterMark'
import MenuHeader from './components/menu-header'
import Header from './components/header'
import MenuFooter from './components/menu-footer'
import Breadcrumb from './components/breadcrumb'
import style from './style.module.less'

type Iprops = {
  router: RouterClass
}

const BaseLayout: React.FC<Iprops> = function ({ router }) {
  const navigate = useNavigate()
  const [collapsed, setCollapsed] = useState(false)
  return (
    <div className={style.baseLayout}>
      <ProLayout
        className={style.layout}
        collapsed={collapsed}
        collapsedButtonRender={false}
        fixedHeader
        fixSiderbar
        menuFooterRender={() => <MenuFooter collapsed={collapsed} setCollapsed={setCollapsed} />}
        menuHeaderRender={(logo, title) => <MenuHeader collapsed={collapsed} title={title} />}
        menuItemRender={(item, dom) => (
          <a
            onClick={() => {
              if (item.path) {
                navigate(item.path)
              }
            }}
          >
            {dom}
          </a>
        )}
        onCollapse={setCollapsed}
        rightContentRender={() => <Header />}
        route={router.layoutRoute}
        title={config.projectName}
        contentStyle={{
          margin: 0,
          overflow: 'auto',
        }}
      >
        <PageContainer
          header={{
            title: null,
            breadcrumbRender: () => <Breadcrumb router={router} />,
            style: {
              // padding: 0,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px 24px 16px',
            },
          }}
          className={style.container}
        >
          <WaterMark>
            <div className={style.main}>
              <Outlet />
            </div>
          </WaterMark>
        </PageContainer>
      </ProLayout>
    </div>
  )
}

export default BaseLayout
