import Router, { RouteType } from '@/services/router'
import React from 'react'
import { useLocation } from 'react-router-dom'
import classnames from 'classnames'
import useHistory from '@/hooks/useHistory'
import style from './style.module.less'

type Iprops = {
  router: Router
}

const Breadcurmb: React.FC<Iprops> = function ({ router }) {
  const location = useLocation()
  const history = useHistory()
  const routes: RouteType[] = router.generatorBreadcrumb(location.pathname)

  return (
    <>
      {routes.map((item, index, all) => {
        const name = item?.breadcrumbRender ? item.breadcrumbRender(history) : item?.name
        return (
          <span
            className={classnames({
              [style.breadcurmb__text]: true,
              [style.breadcurmb__text__active]: all.length - 1 === index,
            })}
            // eslint-disable-next-line react/no-array-index-key
            key={index}
          >
            {index !== 0 ? ' / ' : ''}
            {name}
          </span>
        )
      })}
    </>
  )
}

export default Breadcurmb
