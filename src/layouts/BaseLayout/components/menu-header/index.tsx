import Router from '@/services/router'
import React from 'react'
import { useNavigate } from 'react-router-dom'
import Logo from '../logo'

type Iprops = {
  collapsed: boolean
  title: React.ReactNode
}

const MenuHeader: React.FC<Iprops> = function ({ collapsed, title }) {
  const navigate = useNavigate()

  return (
    <div
      onClick={() => {
        navigate(Router.path('/'))
      }}
    >
      {collapsed ? (
        <Logo size="small" />
      ) : (
        <>
          <Logo />
          {title}
        </>
      )}
    </div>
  )
}

export default MenuHeader
