import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import React from 'react'
import style from './style.module.less'

type Iprops = {
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
}

const MenuFooter: React.FC<Iprops> = ({ collapsed, setCollapsed }) => (
  <div
    onClick={() => {
      setCollapsed(!collapsed)
    }}
    className={style.menuFooter}
  >
    {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
  </div>
)

export default MenuFooter
