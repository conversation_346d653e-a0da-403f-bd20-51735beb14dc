import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import Router from '@/services/router'
import userService from '@/services/user'
import style from './style.module.less'

const User: React.FC = function () {
  const navigate = useNavigate()

  const goToUser = () => {
    if (window.location.pathname !== '/user') {
      navigate(Router.path('/user'))
    }
  }

  return (
    <div className={style.user} onClick={goToUser}>
      <UserOutlined className={style.user__icon} />
      <span className={style.user__name}>
        {/* todo 后续做处理 */}
        {userService.info!.name}（{userService.info!.num}）
      </span>
    </div>
  )
}

export default User
