import React, { useMemo } from 'react'
import { WaterMark as AntdWaterMark } from '@ant-design/pro-layout'
import userService from '@/services/user'
import config from '@/config'
import { formatDate } from '@/utils/utils'

interface IProps {
  children: any
}

const WaterMark = (props: IProps) => {
  const { children } = props

  const watermark = useMemo<string | undefined>(() => {
    if (!userService.info) {
      return undefined
    }
    let result = `${userService.info.num || userService.info.userName}-${
      userService.info.name
    }-${formatDate(new Date(), '{y}.{m}.{d} {h}:{i}')}`
    if (config.environment !== 'prod') {
      result += `-${config.environment}`
    }
    return result
  }, [])

  return (
    // @ts-ignore
    <AntdWaterMark content={watermark} fontColor="rgba(0,0,0,.1)">
      {children}
    </AntdWaterMark>
  )
}

export default WaterMark
