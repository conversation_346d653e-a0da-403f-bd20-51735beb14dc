import classNames from 'classnames'
import React from 'react'
import style from './style.module.less'

interface Iprops {
  size?: 'small' | 'normal'
}

const Logo: React.FC<Iprops> = function ({ size }) {
  return (
    <span
      className={classNames({
        [style.logo]: true,
        [style.logo__small]: size === 'small',
        [style.logo__normal]: size === 'normal',
      })}
    >
      pupu
    </span>
  )
}

Logo.defaultProps = {
  size: 'normal',
}

export default Logo
