// import 'core-js/stable';
import './publicPath'
import 'regenerator-runtime/runtime'
import React from 'react'
import { createRoot } from 'react-dom/client'
// import RouteFactory from '@/router/RouteFactory'
// import mainRoutes from '@/router/route/mainRoutes'
import Router from '@/services/router'
import main from '@/router/route/main'
import App from './App'
import { init } from './utils/request/global'
import { isMicroApp } from './constants'

let root: any
function render(props) {
  const { container } = props
  const rootElement = container
    ? container.querySelector('#microRoot')
    : document.querySelector('#microRoot')
  if (!root) {
    root = createRoot(rootElement)
  }
  root.render(<App />)
}

if (!isMicroApp) {
  init()
  render({})
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function bootstrap() {}

const routes = Router.routes(main)

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  const router = new Router(routes)
  props.onGlobalStateChange((props2) => {
    window.microAppProps = props2
    // const info = props2.getUserInfo()
    // userService.info = {
    //   id: info.id,
    //   name: info.username,
    //   num: info.userNumber,
    //   phone: info.userPhone,
    //   email: info.email,
    // }
    props2.registerRoute(router.microRoute)
  }, true)
  render(props)
}

export async function unmount(props) {
  const container = document.querySelector('#microRoot')
  if (container && root) {
    root.unmount()
  }
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function update() {}
