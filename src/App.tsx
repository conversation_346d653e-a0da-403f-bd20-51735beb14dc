// import 'babel-polyfill' // 兼容性代码
import 'normalize.css' // css样式重制
// eslint-disable-next-line @galaxy/no-lib-path
import '@galaxy/dhr-style/lib/constant/var.less'
import React from 'react'
import 'moment/locale/zh-cn'
import uc from '@galaxy/uc'
import rbac from '@galaxy/rbac'
import { init } from '@galaxy/dict'
import { init as uploadInit } from '@galaxy/upload'
import { init as asyncTaskInit } from '@galaxy/async-task-component'
import { init as orgSelectorInit } from '@galaxy/org-selector'
import configProvider from '@galaxy/config-provider'
import ErrorBoundary from '@/components/ErrorBoundary'
import AppRouter from '@/router/router'
import config from '@/config'
import zhCN from 'antd/es/locale/zh_CN'

// Theme
import './themes/theme.less'
// 引入样式文件
import './themes/theme-main.less'
import { ConfigProvider } from 'antd'

const env = config.environment === 'prod' ? 'prod' : 'itPre'

configProvider.config({
  env,
})

init({
  tenantId: config.environment === 'prod' ? '76' : '82',
  env,
})

uc.init({
  env,
})

rbac.init({
  env,
})

orgSelectorInit({
  env,
})

uploadInit({
  env,
})

asyncTaskInit(
  ['dev', 'itTest'].includes(config.environment) // 目前只部署了itTest k8s
    ? {
      origin: config.serverUrl,
    }
    : {
      env,
    },
)

ConfigProvider.config({
  prefixCls: 'onboarding', // 4.13.0+
})

if (!window.__POWERED_BY_QIANKUN__) {
  // 独立访问时加载apm
  import('./utils/apm').then((module) => {
    if (typeof module?.StartApmMonitor === 'function') {
      module?.StartApmMonitor()
    }
  })
}

const APP = function () {
  return (
    <ConfigProvider prefixCls="onboarding" locale={zhCN}>
      <ErrorBoundary>
        <AppRouter />
      </ErrorBoundary>
    </ConfigProvider>
  )
}

export default APP
