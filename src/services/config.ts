import dayjs from 'dayjs'
// 负责配置相关的数据处理
import { getConfig, updateConfig } from '@/apis/config'
import moment from 'moment'

type Type =
  | 1
  | 2
  | 3
  | 4
  | 5 /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置4-入职办理信息配置 5-时间规则配置 */

type GetDataParams = {
  type: Type
}

type DataType = {
  code: string
  /** 具体配置名称 */
  name: string
  /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置4-入职办理信息配置 */
  type: Type
  /** 值 */
  value: any
}

export type UpdateDataParams = {
  code: string
  name?: string
  type: Type
  value?: any
}

enum ValueType {
  Number = 'number',
  Time = 'time',
  StringArray = 'string[]',
}

enum RuleCodeToCode {
  A = '9',
  B = '10',
  C = '11',
  D = '12',
  E = '13',
  F = '14',
  G = '3',
  H = '4',
  I = '5',
  J = '6',
  K = '7',
  L = '8',
}

const ruleCode = new Map<string, { code: string; valueType: ValueType; name: string }>()
ruleCode.set('9', {
  code: 'A',
  valueType: ValueType.Number,
  name: 'O序列-信息补录短信模板',
})
ruleCode.set('10', {
  code: 'B',
  valueType: ValueType.Number,
  name: 'S序列-信息补录短信模板',
})
ruleCode.set('11', {
  code: 'C',
  valueType: ValueType.Number,
  name: '非O/S序列-信息补录短信模板',
})
ruleCode.set('12', {
  code: 'D',
  valueType: ValueType.Number,
  name: 'O序列-审核通过通知短信模板',
})
ruleCode.set('13', {
  code: 'E',
  valueType: ValueType.Number,
  name: 'S序列-审核通过通知短信模板',
})
ruleCode.set('14', {
  code: 'F',
  valueType: ValueType.Number,
  name: '非O/S序列-审核通过通知短信模板',
})
ruleCode.set('3', {
  code: 'G',
  valueType: ValueType.Number,
  name: '信息补录提醒短信模板',
})
ruleCode.set('4', {
  code: 'H',
  valueType: ValueType.Number,
  name: '驳回修改提醒短信模板',
})
ruleCode.set('5', {
  code: 'I',
  valueType: ValueType.Time,
  name: '信息补录提醒时间',
})
ruleCode.set('6', {
  code: 'J',
  valueType: ValueType.Time,
  name: '驳回修改提醒时间',
})
ruleCode.set('7', {
  code: 'K',
  valueType: ValueType.Number,
  name: '超期自动结束招聘天数',
})
ruleCode.set('8', {
  code: 'L',
  valueType: ValueType.StringArray,
  name: '不通知的材料不合规原因',
})

export type EmployInfoType = { objectKey: number; timeUpdate: number }

class ConfigService {
  // 开户银行和复核规则配置，以code值为准
  getData = async (params: GetDataParams): Promise<Record<string, DataType>> => {
    return getConfig(params).then((data) => {
      const result: Record<string, DataType> = {}
      data.forEach((item) => {
        result[item.code] = {
          code: item.code,
          name: item.name,
          type: item.type,
          value: item.value,
        }
      })
      return result
    })
  }

  // 开户银行和复核规则配置，以code值为准
  updateData = (data: UpdateDataParams[]) => {
    return updateConfig(data)
  }

  // 入职办理信息配置，直接返回数据
  getEmploymentInfo = (): Promise<EmployInfoType | null> => {
    return getConfig({ type: 4 }).then((data) => {
      if (data.length > 0) {
        try {
          const { objectKey, time_update: timeUpdate } = JSON.parse(data[0].value)
          return {
            objectKey,
            timeUpdate,
          }
        } catch (err) {
          console.log(err)
        }
      }
      return null
    })
  }

  // 规则配置比较复杂，有数据转换逻辑，单独抽出来写
  getRuleData = async (params: GetDataParams): Promise<Record<string, any>> => {
    const data = await getConfig(params)
    const result: Record<string, any> = {}
    data.forEach((item) => {
      const config = ruleCode.get(item.code)
      const code = config?.code ?? item.code
      const value =
        config?.valueType && item.value
          ? this.formatValue(config?.valueType, item.value)
          : item.value

      result[code] = value
    })
    return result
  }

  // 规则配置比较复杂，有数据转换逻辑，单独抽出来写
  updateRuleData = (data: Omit<UpdateDataParams, 'type'>[]) => {
    const result = data.map((item) => {
      const code = RuleCodeToCode[item.code] ?? item.code
      const config = ruleCode.get(code)
      const value = this.stringify(item.value, config?.valueType)
      return {
        name: config?.name,
        type: 1 as const,
        code,
        value,
      }
    })
    return this.updateData(result)
  }

  private stringify = (value?, valueType?: ValueType): string => {
    if (valueType === ValueType.Number) {
      return JSON.stringify(value)
    }
    if (valueType === ValueType.Time) {
      const result = dayjs(value).format('H:mm')
      return result
    }
    if (valueType === ValueType.StringArray) {
      return value && value.join(',')
    }
    if (typeof value === 'string') {
      return value
    }
    return JSON.stringify(value)
  }

  private formatValue = (valueType: ValueType, value: string): any => {
    if (valueType === ValueType.Number) {
      const result = Number(value)
      if (Number.isNaN(result)) {
        return value
      }
      return result
    }
    if (valueType === ValueType.Time) {
      const result = moment(`2000-1-1 ${value}`)
      return result
    }
    if (valueType === ValueType.StringArray) {
      const result = value.split(',')
      return result
    }
    return value
  }
}

export default new ConfigService()

export { ruleCode }
