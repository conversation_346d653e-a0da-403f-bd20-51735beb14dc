import { getRoutePermission } from '@galaxy/rbac'
import { forEach, cloneDeep, isString } from 'lodash-es'
import AsyncComponent from '@/utils/AsyncComponent'
import { BASE_PATH } from '@/constants'

export interface RouteType<T = React.FC<any>> {
  path: string
  name?: string
  icon?: React.ReactNode
  component?: T
  routes?: RouteType<T>[]
  hideInMenu?: boolean
  permission?: boolean
  needBack?: boolean
  breadcrumbRender?: (history: any) => React.ReactNode
  [props: string]: any
}

class Router {
  static path = (pathname: string, search?: Record<string, string>) => {
    let searchString = ''
    if (search) {
      forEach(search, (value: string, key: string) => {
        if (value) {
          searchString += `&${key}=${value}`
        }
      })
      searchString = searchString.substring(1)
    }

    let path = `${BASE_PATH}${pathname}`

    if (searchString) {
      path += path.indexOf('?') > -1 ? `&${searchString}` : `?${searchString}`
    }

    return path
  }

  // path => basePath + path
  static joinBasePath = (
    routes: RouteType<string | React.FC<any>>[],
  ): RouteType<string | React.FC<any>>[] => {
    const $routes: RouteType<string | React.FC<any>>[] = cloneDeep(routes)
    const deep = (originRoutes) => {
      ;(Array.isArray(originRoutes) ? originRoutes : [originRoutes]).forEach((item) => {
        // eslint-disable-next-line no-param-reassign
        item.path = Router.path(item.path)

        if (item.routes) {
          deep(item.routes)
        }
      })
    }
    deep($routes)
    return $routes
  }

  // 组件地址 -> 组件
  static parseComponent = (routes: RouteType<string | React.FC<any>>[]): RouteType[] => {
    const $routes: RouteType<string | React.FC<any>>[] = cloneDeep(routes)
    const deep = (originRoutes) => {
      ;(Array.isArray(originRoutes) ? originRoutes : [originRoutes]).forEach((item) => {
        const component = item.component
        if (isString(component)) {
          // eslint-disable-next-line no-param-reassign
          item.component = AsyncComponent(() => import(/* @vite-ignore */ `../pages${component}`))
        }

        if (item.routes) {
          deep(item.routes)
        }
      })
    }
    deep($routes)
    return $routes as RouteType<React.FC<any>>[]
  }

  static routes = (routes: RouteType<string | React.FC<any>>[]) =>
    this.parseComponent(this.joinBasePath(routes))

  static toList = (routes: RouteType[]) => {
    const result: RouteType[] = []
    const deep = (originRoutes) => {
      ;(Array.isArray(originRoutes) ? originRoutes : [originRoutes]).forEach((item) => {
        const { routes: children, ...restRoute } = item
        result.push(restRoute)

        if (children) {
          deep(children)
        }
      })
    }
    deep(cloneDeep(routes))
    return result
  }

  static toMap = (routes: RouteType<string | React.FC>[]) => {
    const result: Record<string, RouteType> = {}
    const deep = (originRoutes) => {
      ;(Array.isArray(originRoutes) ? originRoutes : [originRoutes]).forEach((item) => {
        const { routes: children, ...restRoute } = item
        result[item.path] = restRoute

        if (children) {
          deep(children)
        }
      })
    }
    deep(cloneDeep(routes))
    return result
  }

  routes: RouteType[] = []

  private accessRoutes: RouteType[] = []

  constructor(routes: RouteType[]) {
    // 路由配置需要处理，因此保存路由配置
    this.routes = routes
    this.accessRoutes = this.filterByRbac(this.routes)
  }

  // rbac过滤
  private filterByRbac = (routes: RouteType<string | React.FC<any>>[]): RouteType[] => {
    const deep = (originRoutes) => {
      const result: RouteType[] = (
        Array.isArray(originRoutes) ? originRoutes : [originRoutes]
      ).filter((item) => {
        if (!item.permission || getRoutePermission(item.path)) {
          if (item.routes) {
            const children = deep(item.routes)

            if (children.length === 0) {
              // eslint-disable-next-line no-param-reassign
              delete item.routes
            } else {
              // eslint-disable-next-line no-param-reassign
              item.routes = children
            }
          }
          return true
        }
        return false
      })
      return result
    }
    return deep(cloneDeep(routes))
  }

  // 微应用 route
  get microRoute() {
    return [
      {
        path: Router.path(''),
        routes: this.routes,
      },
    ]
  }

  // pro-layout route
  get layoutRoute() {
    return {
      path: Router.path(''),
      routes: this.accessRoutes,
    }
  }

  hasAccess = (path: string) => {
    const routeMap = Router.toMap(this.accessRoutes)
    if (path in routeMap) {
      return true
    }
    return false
  }

  generatorBreadcrumb = (pathname: string): RouteType[] => {
    const result: RouteType[] = []
    const routeMap = Router.toMap(this.routes)
    const pathnameArr = pathname.split('/')
    if (pathname === `${BASE_PATH}/`) {
      // 欢迎页面
      result.push(routeMap[`${BASE_PATH}`] || routeMap[`${BASE_PATH}/`])
    } else {
      pathnameArr.forEach((item, idx) => {
        if (item === '') {
          return
        }
        const breadc = pathnameArr.slice(0, idx + 1).join('/')
        result.push(routeMap[breadc])
      })
    }
    return result
  }

  updateAccessRoutes = () => {
    this.accessRoutes = this.filterByRbac(this.routes)
  }
}

export default Router
