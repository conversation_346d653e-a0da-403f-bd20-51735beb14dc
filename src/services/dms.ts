import {
  delLocalStorage,
  getLocalStorage,
  jsonToUnderline as utilJsonToUnderline,
  setLocalStorage,
} from '@galaxy/utils'
import { Report } from '@pupu/elangsion-web'
import config from '@/config'
import type { CamelCasedProperties, SnakeCasedProperties } from 'type-fest'

declare type TraceEnv = 'pro' | 'qa' | 'dev'
declare type DhrEnv = 'prod' | 'itPre' | 'itTest' | 'dev'

const { dmsConfig, environment } = config
const { appId, appVersion, idHexStr } = dmsConfig

const PREV_PAGE_CONTEXT = 'prevPageContext'

const jsonToUnderline = <T = any>(data: T): SnakeCasedProperties<T> => {
  return utilJsonToUnderline(data)
}

/**
 * env环境判断dev和test都是dev环境
 * pre是qa环境
 * prod是pro环境
 */
const transformLibEnv = (libEnv: DhrEnv) => {
  let libEnvTemp = ''
  switch (libEnv) {
    case 'itTest':
      libEnvTemp = 'qa'
      break
    case 'itPre':
      libEnvTemp = 'qa'
      break
    case 'prod':
      libEnvTemp = 'pro'
      break
    default:
      libEnvTemp = libEnv
  }
  return libEnvTemp
}

/**
 * @param libEnv 环境参数
 * @returns true:非生产环境
 */
const isDebug = (libEnv: DhrEnv) => {
  let isDebugTemp = true
  if (libEnv === 'prod') {
    isDebugTemp = false
  }
  return isDebugTemp
}

type InnerPageType = Parameters<Report['trace']>[1]['page']

type PageType = CamelCasedProperties<InnerPageType>

type InnerFromType = Parameters<Report['trace']>[1]['from']

type FromType = CamelCasedProperties<InnerFromType>

// 同一时间只会处于一个页面环境中
class Service {
  private report: Report

  private pageContext: PageType

  constructor() {
    this.report = new Report({
      appId,
      appVersion,
      env: transformLibEnv(environment) as TraceEnv,
      idHexStr,
    })
    this.report.setStaticParams('device.is_debug', isDebug(environment))
    this.report.setDynamicParams('when.time_report', () => Date.now())
    this.report.done()
  }

  login = (userNum: string) => {
    this.report.login(userNum)
  }

  trace = (eventName: string, context?: Record<string, any>) => {
    try {
      const prevPage = this.getPrevPageContext()
      let page: InnerPageType
      let from: InnerFromType

      if (this.pageContext && 'pageName' in this.pageContext) {
        page = jsonToUnderline(this.pageContext)
      }

      if (prevPage && 'pageName' in prevPage) {
        from = jsonToUnderline({
          fromPageName: prevPage.pageName,
        })
      }

      this.report.trace(eventName, {
        context: jsonToUnderline(context),
        page,
        from,
      })
    } catch (error) {
      console.log('埋点上报失败', error)
    }
  }

  injectPageContext = (context: PageType) => {
    this.pageContext = context
  }

  setPrevPageContext = () => {
    // todo expire一定要设置暂时先这样
    if (this.pageContext) {
      setLocalStorage(PREV_PAGE_CONTEXT, JSON.stringify(this.pageContext), 10000)
    } else {
      delLocalStorage(PREV_PAGE_CONTEXT)
    }
    this.pageContext = undefined
  }

  private getPrevPageContext = (): PageType => {
    const contextStr = getLocalStorage(PREV_PAGE_CONTEXT)
    try {
      if (contextStr) {
        const context: PageType = JSON.parse(contextStr)
        return context
      }
      return undefined
    } catch (error) {
      console.log(error)
      return undefined
    }
  }
}

export default new Service()
