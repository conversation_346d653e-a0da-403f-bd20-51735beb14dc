// 字典服务，主要实现数据缓存、查询映射
import { service } from '@galaxy/dict'
import Debounce from '@/utils/Debounce'

class DictService {
  cache: Record<string, Record<string, string>> = {}

  debounce: Record<string, Debounce> = {}

  codeToName = async (
    code: string,
    dictCode: string,
    dictConfig?: {
      scope?: string
      fullName?: boolean
    },
  ): Promise<string> => {
    const { scope = 'EHR', fullName = false } = dictConfig || {}
    // 根据字典code和字典配置生成唯一key
    const key = dictCode + scope + fullName
    if (key in this.cache) {
      return this.cache[key][code] ?? code
    }
    // 为每个scope类型+fullName，构造防抖函数
    const debounceKey = scope + fullName
    if (!(debounceKey in this.debounce)) {
      this.debounce[debounceKey] = new Debounce(
        (args) =>
          fullName
            ? service.getDictionaryItemMapFullName(args, scope)
            : service.getDictionaryItemMap(args, scope),
        100,
      )
    }
    // 为service.getDictionaryItemMap创建防抖能力
    const req = this.debounce[debounceKey].debounce(dictCode)
    try {
      const itemMap = await req
      // @ts-ignore
      this.cache[key] = itemMap[dictCode] ?? {}
      return this.cache[key][code] ?? code
    } catch {
      return code
    }
  }
}

// 无初始化，且有缓存需求，直接单实例
export default new DictService()
