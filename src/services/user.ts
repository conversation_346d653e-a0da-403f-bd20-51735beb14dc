type DataType = {
  id: string // 账号ID
  name: string // 员工姓名
  num?: string // 工号
  phone: string // 手机号码
  email?: string // 电子邮箱
  departmentName?: string // 所属部门
  positionName?: string // 岗位
  userName?: string // 用户名
}

class Service {
  private store?: DataType = undefined

  get info() {
    return this.store
  }

  set info(data: DataType | undefined) {
    this.store = data
  }
}

export default new Service()
