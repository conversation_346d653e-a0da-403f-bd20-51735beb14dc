// 负责模版数据处理

import {
  GetTemplateListParams,
  addTemplate,
  deleteTemplate,
  getTemplate,
  getTemplateList,
  putTemplate,
} from '@/apis/template'

import type { TemplateData as OriginTemplateData } from '@/apis/template'

export type DataType = {
  id?: number
  name: string
  typeCode: string
  content: string
}

export type TemplateData = {
  /** 正在使用的配置ID */
  configureId: number
  /** 正在使用的配置名称 */
  configureName: string
  /** 模板ID */
  id: number
  /** 模板是否正在被使用 */
  isUsing: boolean
  /** 短信模板 */
  content: string
  /** 模板名称 */
  name: string
  /** 创建时间 */
  timeCreate?: number
  /** 模板类型-取值于码表（OMS_TEMPLATE_TYPE） */
  typeCode: string
  typeName?: {
    code: string
    dictCode: string
    scope: string
  }
}

type GetListParams = {
  /** 模板名称，长度限制40字符 */
  name?: string
  /** 模板类型，取值于码表（OMS_TEMPLATE_TYPE） */
  typeCode?: string
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  // 不分页
  noPage?: boolean
}

class TemplateService {
  getList = async (params: GetListParams): Promise<{ data: TemplateData[]; total: number }> => {
    const { name, typeCode, page, size, noPage } = params
    if (noPage) {
      let total: number = 0
      const getOptions = async (options: TemplateData[] = [], current: number = 1) => {
        const { data, count } = await getTemplateList({
          name,
          typeCode,
          page: current,
          size: 200,
        })
        total = count
        let result: TemplateData[] = [...data.map(this.format), ...options]
        // data.length !== 0 判断是为了防止服务端缺数据场景，避免不断循环
        if (data.length !== 0 && result.length < total) {
          result = await getOptions(result, current + 1)
        }
        return result
      }

      const data = await getOptions()
      return {
        data,
        total,
      }
    }
    return getTemplateList({ name, typeCode, page, size }).then((res) => ({
      data: res.data.map(this.format),
      total: res.count,
    }))
  }

  get = (id: number): Promise<TemplateData> => {
    return getTemplate({ id }).then(this.format)
  }

  delete = (id: number) => {
    return deleteTemplate({ id })
  }

  update = (data: DataType): Promise<TemplateData> => {
    if (typeof data.id !== 'undefined') {
      return putTemplate({
        id: data.id,
        msgTemplate: data.content,
        name: data.name,
        typeCode: data.typeCode,
      }).then(this.format)
    }
    return addTemplate({
      msgTemplate: data.content,
      name: data.name,
      typeCode: data.typeCode,
    }).then(this.format)
  }

  private format = (data: OriginTemplateData): TemplateData => {
    return {
      configureId: data.configureId,
      configureName: data.configureName,
      id: data.id,
      isUsing: data.isUsing,
      content: data.msgTemplate,
      name: data.name,
      timeCreate: data.timeCreate,
      typeCode: data.typeCode,
      typeName: {
        code: data.typeCode,
        dictCode: 'OMS_TEMPLATE_TYPE',
        scope: 'ENTRY',
      },
    }
  }
}

export default new TemplateService()
