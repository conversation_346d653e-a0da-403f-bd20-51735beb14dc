import UploadSDK from '@galaxy/upload'
import { getDminEntryV10AsyncTaskById } from '@/apis/task'

// 异步状态
enum AsyncStatus {
  // 待执行
  CREATED = 0,
  // 执行中
  PENDING = 1,
  // 执行成功
  COMPLETED = 2,
  // 执行失败
  PRE_CONDITION_FAILED = 3,
  // 处理异常
  EXCEPTION = 4,
  // 拒绝执行
  REJECTED = 5,
  // 执行异常
  ERROR = 6,
}

class Service {
  upload = new UploadSDK({
    oss: {
      service: '/admin/entry',
    },
  })

  pollTaskStatus = async (id: number): Promise<string> => {
    try {
      const data = await getDminEntryV10AsyncTaskById({ id })
      const { percentage, countTotal, countOk, status, objectKey, errorMessage } = data

      // 如果小于等于 PENDING 状态，继续轮询
      if (data.status <= AsyncStatus.PENDING) {
        await new Promise((resolve) => {
          setTimeout(resolve, 1000)
        })
        return this.pollTaskStatus(id)
      }

      if (objectKey) {
        const res = await this.upload.download(objectKey)
        console.log(res)
        return res.url
      }
      return Promise.reject(new Error('任务轮询错误'))
    } catch (error) {
      return Promise.reject(new Error('任务轮询错误'))
    }
  }
}

export default new Service()
