import {
  ModifyEmploymentInfoType,
  modifyEmploymentInfo,
  GetEntryPlaceType,
  getEntryPlace,
} from '@/apis/employ'

class EmployService {
  modifyInfo = (entryMaterialId: number, data: ModifyEmploymentInfoType, config?) => {
    return modifyEmploymentInfo(entryMaterialId, data, config)
  }

  getEntryPlace = async (datas: GetEntryPlaceType, config?) => {
    const { data } = await getEntryPlace(datas, config)
    return data
  }
}

export default new EmployService()
