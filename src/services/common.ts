import {
  getPersonnelStandard,
  getEhrDict<PERSON><PERSON>,
  getPositionList,
  GetPositionListDataType,
  postRecord,
} from '@/apis/common'

type EhrDictAreaValues = {
  value: string
  id: number
  label: string
  parentId: number
}

// 公共基础服务域
class CommonService {
  ehrDictArea: EhrDictAreaValues[] = []

  getPeriod = (positionId?: number): Promise<number | undefined> => {
    return getPersonnelStandard(positionId).then((data) => {
      return data.probationPeriod
    })
  }

  getEhrDictArea = (): Promise<EhrDictAreaValues[]> => {
    if (this.ehrDictArea.length > 0) {
      return Promise.resolve(this.ehrDictArea)
    }
    return getEhrDictArea().then((res) => {
      const { data } = res
      const $data = []
      data.forEach((item) => {
        $data.push({
          id: item.id,
          parentId: item.parentId,
          label: item.name,
          value: item.code,
        })
      })
      this.ehrDictArea = $data
      return $data
    })
  }

  postRecord = async (requestParams) => {
    const { data } = await postRecord(requestParams)
    return data
  }

  getPositionList = async (data: GetPositionListDataType) => getPositionList(data)
}

export default new CommonService()
