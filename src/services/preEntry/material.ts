import {
  getOperationLogList,
  resendModifySMS,
  resendSMS,
  GetOperationLogListParams,
} from '@/apis/onboardingMaterial'
import { getEntryMaterialAuditInfo } from '@/apis/review'
import { PageResponse } from '@/types/common'
import { SpecialApprovalInfoType } from '@/types/entryMaterialsAudit'

export { EntryMaterialAuditInfo } from '@/types/entryMaterialsAudit'

type ResendModifySMSDataType = {
  id: number
  templateId: number
  endTime: number
}

export type LogData = {
  time?: number
  content?: string
  user?: string
}

class Material {
  resendSMS = (id: number, options: any) => {
    return resendSMS({ id }, options)
  }

  resendModifySMS = (data: ResendModifySMSDataType, options?: any) => {
    return resendModifySMS(
      {
        id: data.id,
        templateId: data.templateId,
        timeComplete: data.endTime,
      },
      options,
    )
  }

  getOperationLogList = (data: GetOperationLogListParams): Promise<PageResponse<LogData[]>> => {
    return getOperationLogList(data).then((res) => ({
      count: res.count,
      data: res.data.map((item) => {
        const { content, operateTime, operator, operatorName } = item
        return {
          time: operateTime,
          content,
          user: `${operatorName || ''}（${operator || '-'}）`,
        }
      }),
    }))
  }

  getSepcialApprovalInfo = (id: number): Promise<SpecialApprovalInfoType> => {
    return getEntryMaterialAuditInfo(id).then((res) => {
      return res.data?.specialApprovalInfo
    })
  }
}

export default new Material()
