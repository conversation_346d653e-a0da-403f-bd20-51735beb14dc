import {
  batchReview,
  review,
  ReviewRoObject,
  BatchReviewRoObject,
  rejectResult,
  pushResult,
} from '@/apis/audit'

export type ReviewParams = {
  id: number
  reviewResult: 1 | 2
  refuseCodes?: string
  note?: string
}

export type BatchReviewParams = {
  reviewResult: 1 | 2
  ids: number[]
}

class Service {
  review = (data: ReviewParams) => {
    return review({
      id: data.id,
      reviewResult: data.reviewResult,
      noPassReasonCodes: data.refuseCodes,
      rejectReason: data.note,
    })
  }

  batchReview = (data: BatchReviewParams): Promise<{ success: number; fail: number }> => {
    return batchReview({
      reviews: data.ids.map((id) => ({
        entryMaterialId: id,
        reviewResult: data.reviewResult,
      })),
    }).then((res) => {
      let success = 0
      let fail = 0

      res.forEach((item) => {
        if (item.data) {
          success += 1
        } else {
          fail += 1
        }
      })

      return {
        success,
        fail,
      }
    })
  }

  pushResult = (data) => {
    return pushResult(data)
  }

  rejectResult = (id: number, options?: any) => {
    return rejectResult(id, options)
  }
}

export default new Service()
