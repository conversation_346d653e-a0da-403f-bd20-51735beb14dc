import {
  postDminEntryV1EntryMaterialsExport,
  postDminEntryV1EntryMaterialsTimeStatisticsExport,
} from '@/apis/export'

export type ExportParams = {
  areaCode?: string // 所属区域，取值于EHR码表（BM_BZGSQY）
  completeSituation?: 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
  departmentId?: number // 部门ID
  ehrCandidateId?: number // EHR人才ID
  ehrProgress?: number // EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功
  employmentFormCode?: string[] // 用工形式，取值EHR码表（BM_35）
  enterEntryTimeBegin: number
  enterEntryTimeEnd: number
  name?: string // 姓名
  positionCandidateId?: number // DHR候选人ID
  positionId?: number // 实体岗位ID
  processProgresses?: number[] // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘
  recruitResponsibleUserNum?: string // 招聘负责人工号
  status?: number // 0-进行中 1-已完成
  timeEntryBegin?: number // 预计报到开始时间
  timeEntryEnd?: number // 预计报到结束时间
  timeoutStatuses?: number[] // 超时状态 0-未超时 1-即将超时 2-已超时
}

class Service {
  exportTimeDetail = (data: ExportParams) => {
    return postDminEntryV1EntryMaterialsTimeStatisticsExport(data)
  }

  exportData = (data: ExportParams) => {
    return postDminEntryV1EntryMaterialsExport(data)
  }
}

export default new Service()
