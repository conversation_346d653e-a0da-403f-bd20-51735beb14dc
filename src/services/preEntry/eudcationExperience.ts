/* eslint-disable no-else-return */
import dayjs from 'dayjs'
import { sortBy, filter, difference, map } from 'lodash-es'
import { EducationExperienceItem } from '@/types/entryMaterialsAudit'
import { EDU_TYPES } from '@/constants'

class EduInfoService {
  eduSort(a: EducationExperienceItem, b: EducationExperienceItem) {
    // 1.关于时间可能为0的情况（毕业时间和入学时间传入进来是字符串不存在）
    // 2.教育经历可能为空的情况(判断是否存在时不用转换后的值)
    const educationCodeA = Number(a.educationCode)
    const educationCodeB = Number(b.educationCode)
    const timeGraduateA = dayjs(a.timeGraduate).valueOf()
    const timeGraduateB = dayjs(b.timeGraduate).valueOf()
    const timeEnrollmentA = dayjs(a.timeEnrollment).valueOf()
    const timeEnrollmentB = dayjs(b.timeEnrollment).valueOf()
    const timeCreateA = dayjs(a.timeCreate).valueOf()
    const timeCreateB = dayjs(b.timeCreate).valueOf()

    // 首先按学历从高到低排序，学历为空时排在最顶部
    if (!a.educationCode && b.educationCode) {
      return -1
    } else if (a.educationCode && !b.educationCode) {
      return 1
    } else if (a.educationCode && b.educationCode) {
      if (educationCodeA > educationCodeB) {
        return -1
      } else if (educationCodeA < educationCodeB) {
        return 1
      }
    }

    // 其次学历相同时，按结束时间倒序排列，结束时间为空时排在最顶部
    if (!a.timeGraduate && b.timeGraduate) {
      return -1
    } else if (a.timeGraduate && !b.timeGraduate) {
      return 1
    } else if (a.timeGraduate && b.timeGraduate) {
      if (timeGraduateA > timeGraduateB) {
        return -1
      } else if (timeGraduateA < timeGraduateB) {
        return 1
      }
    }

    // 其次结束时间相同时，按开始时间倒序排列，开始时间为空时排在最顶部
    if (!a.timeEnrollment && b.timeEnrollment) {
      return -1
    } else if (a.timeEnrollment && !b.timeEnrollment) {
      return 1
    } else if (a.timeEnrollment && b.timeEnrollment) {
      if (timeEnrollmentA > timeEnrollmentB) {
        return -1
      } else if (timeEnrollmentA < timeEnrollmentB) {
        return 1
      }
    }

    // 其次开始时间相同时，按数据插入数据库时间倒序排列，时间为空时排在最顶部
    if (!a.timeCreate && b.timeCreate) {
      return -1
    } else if (a.timeCreate && !b.timeCreate) {
      return 1
    } else if (a.timeCreate && b.timeCreate) {
      if (timeCreateA > timeCreateB) {
        return -1
      } else if (timeCreateA < timeCreateB) {
        return 1
      }
    }

    return 0
  }

  getSortEducationExperience(list: EducationExperienceItem[]) {
    return list?.sort(this.eduSort)
  }

  getMaxHighEducationCode(list: EducationExperienceItem[]) {
    const $list = sortBy(list, function (o: EducationExperienceItem) {
      return Number(o.educationCode)
    })
    return {
      maxEducationCode: $list?.[$list.length - 1]?.educationCode || '',
      ascendingList: $list,
    }
  }

  // 教育经历链路校验
  linkCheck(list: EducationExperienceItem[]): {
    check: boolean
    missWord: string
  } {
    // 使用 _.difference() 方法找出 A 数组中缺少的 B 数组中的元素
    let $linkCheck = true // 判断各段学历填写是否满足学历链条
    let $missValues = []
    const { maxEducationCode } = this.getMaxHighEducationCode(list)
    const $minJuniorColleges = map(
      filter(list, function (o: EducationExperienceItem) {
        return Number(o.educationCode) >= Number(EDU_TYPES.JUNIOR_COLLEGE)
      }),
      'educationCode',
    )
    switch (maxEducationCode) {
      case EDU_TYPES.DOCTORAL_CANDIDATE:
        $missValues = difference(
          [EDU_TYPES.MASTER_DEGREE_CANDIDATE, EDU_TYPES.UNDERGRADUATE, EDU_TYPES.JUNIOR_COLLEGE],
          $minJuniorColleges,
        )
        // 1.缺失一段工作经历，且工作经历是本科或大专
        // 2.未缺失工作经历
        $linkCheck =
          ($missValues.length === 1 &&
            ($missValues?.[0] === EDU_TYPES.UNDERGRADUATE ||
              $missValues?.[0] === EDU_TYPES.JUNIOR_COLLEGE)) ||
          $missValues.length === 0
        break
      case EDU_TYPES.MASTER_DEGREE_CANDIDATE:
        $missValues = difference(
          [EDU_TYPES.UNDERGRADUATE, EDU_TYPES.JUNIOR_COLLEGE],
          $minJuniorColleges,
        )
        // 1.缺失一段工作经历，且工作经历是本科或大专
        // 2.未缺失工作经历
        $linkCheck =
          ($missValues.length === 1 &&
            ($missValues?.[0] === EDU_TYPES.UNDERGRADUATE ||
              $missValues?.[0] === EDU_TYPES.JUNIOR_COLLEGE)) ||
          $missValues.length === 0
        break
      default:
        break
    }
    return {
      check: $linkCheck,
      missWord: this.getLinkWord($missValues),
    }
  }

  getLinkWord(list) {
    let word = '缺失'
    const $missCondition =
      list.includes(EDU_TYPES.UNDERGRADUATE) && list.includes(EDU_TYPES.JUNIOR_COLLEGE)
    if (list.includes(EDU_TYPES.MASTER_DEGREE_CANDIDATE)) {
      word += `硕士${$missCondition ? '、' : ''}`
    }
    if ($missCondition) {
      word += '本科/大专'
    }
    return `${word}教育经历，请修改或驳回给候选人修改`
  }
}

export default new EduInfoService()
