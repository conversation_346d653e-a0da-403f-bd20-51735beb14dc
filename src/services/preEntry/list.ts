// 预入职列表
import {
  getInProcessingMaterialList,
  getCompletedMaterialList,
  MaterialData,
  endRecruit,
  endReview,
} from '@/apis/onboardingMaterial'
import { DictType } from '@/components/DictText'

type ListParams = {
  areaCode?: string // 所属区域，取值于EHR码表（BM_BZGSQY）
  completeSituation?: 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
  departmentId?: number // 部门ID
  ehrCandidateId?: number // EHR人才ID
  ehrProgress?: number // EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功
  employmentFormCodes?: string // 用工形式，取值EHR码表（BM_35）
  enterEntryTimeBegin?: number
  enterEntryTimeEnd?: number
  name?: string // 姓名
  positionCandidateId?: number // DHR候选人ID
  positionId?: number // 实体岗位ID
  positionName?: string // 实体岗位名称
  processProgresses?: string // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘
  recruitResponsibleUserNums?: string // 招聘负责人工号
  // status?: number // 0-进行中 1-已完成
  timeEntryBegin?: number // 预计报到开始时间
  timeEntryEnd?: number // 预计报到结束时间
  timeoutStatuses?: string // 超时状态 0-未超时 1-即将超时 2-已超时
  sort?: string
  page?: number
  size?: number // 分页数量，最大200,示例值(20)
}

type Data = {
  id: number //
  ehrCandidateId: number // EHR人才ID
  name: string // 姓名
  departmentFullName: string // 部门全路径
  departmentName: string // 报到部门名称
  departmentId: number // 报到部门ID
  positionCandidateId: number // DHR候选人ID
  positionName: string // 报到岗位
  positionId: number // 报到岗位id
  positionBaseId: number
  recruitResponsiblePerson?: string // 招聘负责人
  areaCode: string
  areaName?: DictType // 所属区域
  contractEntityName?: DictType // 合同主体
  offerTemplateName?: DictType // offer适用模板
  employmentFormName?: DictType // 用工形式
  employmentTypeName?: DictType // 员工类型
  employmentStateName?: DictType // 人员类别
  entryCityName?: string // 入职办理地区
  entryPlaceName?: string // 入职办理地点
  entryPlaceNameType?: number // 入职办理地简称
  entryPlaceAddress?: string // 入职办理地简称类型
  fourthDepartmentName?: string // 【v1.5】报到部门链路上四级部门机构名称
  fourthDepartmentCategory?: number // 【v1.5】报到部门链路上四级部门机构类别
  postLevelName?: DictType // 职位
  postRankName?: DictType // 职级
  trialPeriodName?: DictType // 试用期期限
  residenceAddress: string // 身份证地址
  age: number // 年龄
  timeIdCardStart: number // 身份证开始日期
  timeIdCardEnd: number // 身份证到期日期
  bankTypeName?: DictType // 银行卡类型
  bankName: string // 开户银行
  educationName?: DictType // 学历
  graduateSchool: string // 毕业院校
  major: string // 专业
  timeGraduate: number // 毕业日期
  studyModeName?: DictType // 学习形式
  degreeName?: DictType // 学位
  isHealthCertificateName?: DictType // 是否有健康证
  timeHealthEnd: number // 健康证到期日期
  emergencyContactName: string // 紧急联系人姓名
  emergencyContactPhone: string // 紧急联系人电话
  memberRelationName?: DictType // 与本人关系
  memberRelationRemark?: string // 【v2.0.0】与本人关系备注
  isDisabledName?: DictType // 是否残障人士
  isVeteranName?: DictType // 是否退伍军人
  timeEntry: number // 预计报到日期
  timeoutStatus: number // 超时状态 0-未超时 1-即将超时 2-已超时
  timeRecentSubmit: number // 最近提交时间
  processProgress: ProcessProgress // 处理进度 0-待提交 1-待再次提交 2-待审核 3-待复核 4-待重审 5-审核通过 6-审核不通过 7-特批通过 8-结束招聘
  auditResult: number // 初审结果 0-无 1-审核通过 2-审核不通过 3-驳回修改
  noPassReason?: DictType // 初审不通过原因
  rejectReason?: DictType // 复核驳回原因
  rejectReasonRemark?: string // 复核驳回原因说明
  ehrProgress: EhrProgress // EHR流转状态 0-无 1-待下发 2-自动重试中 3-下发失败 4-下发成功
  recentAuditPerson?: string // 最近初审人
  recentReviewPerson?: string //  最近复核人
  jobDuration: number // 作业净时长
  spentTotalTime: number // 耗费总时长
  isEntry: IsEntry // 是否入职
  currentAddressName?: string // 现居住地
  isNeedReview: IsNeedReview // 是否需要复核
  endRecruitReasonName?: DictType // 结束招聘原因
  endRecruitReasonRemark?: string // 结束招聘原因说明
  isNeedAudit: IsNeedAudit // 是否需要审核
  timeAuditComplete: number // 审批完成时间
  completeSituation: 0 | 1 | 2 // 完成情况 0-无 1-按时完成 2-超时完成
  isCanNoticeModifyEntryMaterial: boolean
  contactPhone: string // 联系电话
  recruitTypeName?: DictType // 招聘类型，取值于DHR码表（RECRUIT_TYPE）
  timeCreate: number // 进入预入职日期，时间戳
  positionCandidateStatus: string // 候选人状态
  dataSource: 'ehr' | 'employment' // 数据来源，通过ehr确定该条数据是试运行外
  isPositionCandidateNewestEntryMaterial: boolean // 是否该预入职条目是同候选人id的最新条目
  isVirtualPosition: boolean
  entryCityCode: string
  employmentFormCode: string
  employmentTypeCode: string
  postRankCode: string
  trialPeriodCode: string
  isCampus: boolean
  cooperateCompanyCode: string
  entryInvestigationResult: number // 背调状态
  entryMaterialProcessTrackInfo?: string // 过程跟踪记录
  isPpTrainee?: number // 是否朴培生
}

export enum ProcessProgress {
  ToBeSubmitted = 0, // 待提交
  ToBeResubmitted, // 待再次提交
  Audit, // 待审核
  ToBeReviewed, // 待复核
  PendingReview, // 待重审
  Approved, // 审核通过
  ReviewFailed, // 审核不通过
  SpecialApproval, // 特批通过
  EndRecruitment, // 结束招聘
}

export enum IsEntry {
  NO = 0,
  YES,
}

export enum IsNeedReview {
  NO = 0,
  YES,
}

export enum IsNeedAudit {
  NO = 0,
  YES,
}

export enum EhrProgress {
  No = 0, // 无
  ToBeIssued, // 1-待下发
  AutoTry, // 2-自动重试中
  Failed, // 3-下发失败
  Success, // 4-下发成功
  ToBeArchived, // 5-待代缴流程归档
}

export type EndRecruitParams = {
  id: number
  preserve: boolean
  reason: string
  tip: string
}

export type EndReviewParams = EndRecruitParams

class ListService {
  getInProcessList = (params: ListParams): Promise<{ total: number; data: Data[] }> => {
    return getInProcessingMaterialList(params).then(async (res) => {
      return {
        total: res.count,
        data: res.data.map(this.format),
      }
    })
  }

  getCompletedList = (params: ListParams): Promise<{ total: number; data: Data[] }> => {
    return getCompletedMaterialList(params).then(async (res) => {
      return {
        total: res.count,
        data: res.data.map(this.format),
      }
    })
  }

  // 字典数据判空处理
  private isEmpty = (value?: any) => {
    if (typeof value === 'undefined') {
      return true
    }
    if (typeof value === 'string' && value === '') {
      return true
    }
    return false
  }

  // eslint-disable-next-line max-lines-per-function
  format = (data: MaterialData): Data => {
    // todo 原先是想避免冗余字段传入，明确传入字段，但是后续服务端加字段，需要在这里补充，比较麻烦，考虑是否直接透传
    const result: Data = {
      id: data.id,
      ehrCandidateId: data.ehrCandidateId,
      name: data.name,
      departmentFullName: data.departmentFullName,
      departmentName: data.departmentName,
      departmentId: data.departmentId,
      positionCandidateId: data.positionCandidateId,
      positionName: data.positionName,
      positionId: data.positionId,
      positionBaseId: data.positionBaseId,
      recruitResponsiblePerson:
        data.recruitResponsiblePerson &&
        (data.recruitResponsiblePerson.userName || data.recruitResponsiblePerson.userNum)
          ? `${data.recruitResponsiblePerson.userName ?? ''}（${
              data.recruitResponsiblePerson.userNum ?? '-'
            }）`
          : undefined,
      areaCode: data.areaCode,
      areaName: !this.isEmpty(data.areaCode)
        ? {
            code: data.areaCode,
            dictCode: 'BM_BZGSQY',
          }
        : undefined,
      contractEntityName: !this.isEmpty(data.contractEntityCode)
        ? {
            code: data.contractEntityCode,
            dictCode: 'BM_HTZT',
          }
        : undefined,
      offerTemplateName: !this.isEmpty(data.offerTemplateCode)
        ? {
            code: data.offerTemplateCode,
            dictCode: 'BM_69',
          }
        : undefined,
      employmentFormName: !this.isEmpty(data.employmentFormCode)
        ? {
            code: data.employmentFormCode,
            dictCode: 'BM_35',
          }
        : undefined,
      employmentTypeName: !this.isEmpty(data.employmentTypeCode)
        ? {
            code: data.employmentTypeCode,
            dictCode: 'BM_YGLX',
          }
        : undefined,
      employmentStateName: !this.isEmpty(data.employmentStateCode)
        ? {
            code: data.employmentStateCode,
            dictCode: 'BM_KK',
          }
        : undefined,
      entryCityName: data.entryCityCode,
      entryPlaceName: data.entryPlaceName,
      entryPlaceNameType: data.entryPlaceNameType,
      entryPlaceAddress: data.entryPlaceAddress,
      fourthDepartmentName: data.fourthDepartmentName,
      fourthDepartmentCategory: data.fourthDepartmentCategory,
      postLevelName: !this.isEmpty(data.postLevelCode)
        ? {
            code: data.postLevelCode,
            dictCode: 'BM_ZWDJ',
          }
        : undefined,
      postRankName: !this.isEmpty(data.postRankCode)
        ? {
            code: data.postRankCode,
            dictCode: 'BM_ZJ',
          }
        : undefined,
      trialPeriodName: !this.isEmpty(data.trialPeriodCode)
        ? {
            code: data.trialPeriodCode,
            dictCode: 'BM_63',
          }
        : undefined,
      residenceAddress: data.residenceAddress,
      timeIdCardStart: data.timeIdCardStart,
      timeIdCardEnd: data.timeIdCardEnd,
      bankTypeName: !this.isEmpty(data.bankCardTypeCode)
        ? {
            code: data.bankCardTypeCode,
            dictCode: 'BM_Banktype',
          }
        : undefined,
      bankName: data.branchBankInfo,
      educationName: !this.isEmpty(data.educationCode)
        ? {
            code: data.educationCode,
            dictCode: 'BM_XL',
          }
        : undefined,
      graduateSchool: data.graduateSchool,
      major: data.major,
      timeGraduate: data.timeGraduate,
      studyModeName: !this.isEmpty(data.studyModeCode)
        ? {
            code: data.studyModeCode,
            dictCode: 'BM_studymode',
          }
        : undefined,
      degreeName: !this.isEmpty(data.degreeCode)
        ? {
            code: data.degreeCode,
            dictCode: 'BM_XW',
          }
        : undefined,
      isHealthCertificateName: !this.isEmpty(data.isHealthCertificateCode)
        ? {
            code: data.isHealthCertificateCode,
            dictCode: 'BM_YN',
          }
        : undefined,
      timeHealthEnd: data.timeHealthEnd,
      emergencyContactName: data.emergencyContactName,
      emergencyContactPhone: data.emergencyContactPhone,
      memberRelationName: !this.isEmpty(data.memberRelationCode)
        ? {
            code: data.memberRelationCode,
            dictCode: 'BM_GX',
          }
        : undefined,
      memberRelationRemark: data.memberRelationRemark,
      isDisabledName: !this.isEmpty(data.isDisabledCode)
        ? {
            code: data.isDisabledCode,
            dictCode: 'BM_YN',
          }
        : undefined,
      isVeteranName: !this.isEmpty(data.isVeteranCode)
        ? {
            code: data.isVeteranCode,
            dictCode: 'BM_YN',
          }
        : undefined,
      timeEntry: data.timeEntry,
      timeoutStatus: data.timeoutStatus,
      timeRecentSubmit: data.timeRecentSubmit,
      processProgress: data.processProgress,
      auditResult: data.auditResult,
      noPassReason:
        data.noPassReasonCodes && data.noPassReasonCodes.length !== 0
          ? {
              sets:
                data.noPassReasonCodes.map((item) => {
                  return {
                    code: item,
                    dictCode: 'OMS_INFORMATION_FAILED_REASON',
                    scope: 'ENTRY',
                    fullName: true,
                  }
                }) ?? [],
              transfer(res) {
                return res.join('、')
              },
            }
          : undefined,
      rejectReason:
        data.rejectReasonCodes && data.rejectReasonCodes.length !== 0
          ? {
              sets:
                data.rejectReasonCodes.map?.((item) => {
                  return {
                    code: item,
                    dictCode: 'OMS_INFORMATION_FAILED_REASON',
                    scope: 'ENTRY',
                    fullName: true,
                  }
                }) ?? [],
              transfer(res) {
                return res.join('、')
              },
            }
          : undefined,
      rejectReasonRemark: data.rejectReason,
      ehrProgress: data.ehrProgress,
      recentAuditPerson:
        data.recentAuditPerson &&
        (data.recentAuditPerson.userName || data.recentAuditPerson.userNum)
          ? `${data.recentAuditPerson.userName ?? ''}（${data.recentAuditPerson.userNum ?? '-'}）`
          : undefined,
      recentReviewPerson:
        data.recentReviewPerson &&
        (data.recentReviewPerson.userName || data.recentReviewPerson.userNum)
          ? `${data.recentReviewPerson.userName ?? ''}（${data.recentReviewPerson.userNum ?? '-'}）`
          : undefined,
      jobDuration: data.jobDuration,
      age: data.age,
      spentTotalTime: data.spentTotalTime,
      isEntry: data.isEntry,
      currentAddressName:
        (data.currentAddressName ?? '') + (data.currentAddress ?? '') || undefined,
      isNeedReview: data.isNeedReview,
      endRecruitReasonName: !this.isEmpty(data.endRecruitReasonCode)
        ? {
            code: data.endRecruitReasonCode,
            dictCode: 'OMS_END_RECRUIT_REASON',
            scope: 'ENTRY',
          }
        : undefined,
      endRecruitReasonRemark: data.endRecruitReasonRemark,
      isNeedAudit: data.isNeedAudit,
      timeAuditComplete: data.timeAuditComplete,
      completeSituation: data.completeSituation,
      isCanNoticeModifyEntryMaterial: data.isCanNoticeModifyEntryMaterial,
      contactPhone: data.contactPhone,
      recruitTypeName: !this.isEmpty(data.recruitTypeCode)
        ? {
            code: data.recruitTypeCode,
            dictCode: 'RECRUIT_TYPE',
            scope: 'GLOBAL',
          }
        : undefined,
      timeCreate: data.timeCreate,
      positionCandidateStatus: data.positionCandidateStatus,
      dataSource: data.dataSource,
      isPositionCandidateNewestEntryMaterial: data.isPositionCandidateNewestEntryMaterial,
      isVirtualPosition: !!data.isVirtualPosition,
      entryCityCode: data.entryCityCode,
      employmentFormCode: data.employmentFormCode,
      employmentTypeCode: data.employmentTypeCode,
      postRankCode: data.postRankCode,
      trialPeriodCode: data.trialPeriodCode,
      isCampus: data.recruitTypeCode === 'ATS_RT_002', // 校园招聘
      cooperateCompanyCode: data.cooperateCompanyCode,
      entryInvestigationResult: data.entryInvestigationResult,
      entryMaterialProcessTrackInfo: data.entryMaterialProcessTrackInfo,
      isPpTrainee: data.isPpTrainee,
    }

    return result
  }

  endRecruit = (data: EndRecruitParams) => {
    return endRecruit({
      id: data.id,
      endRecruitCode: data.reason,
      remark: data.tip,
      isNoticeEhr: data.preserve,
    })
  }

  endReview = (data: EndReviewParams) => {
    return endReview({
      id: data.id,
      endRecruitCode: data.reason,
      remark: data.tip,
      isAllowReSendEmployment: data.preserve,
    })
  }
}

export default new ListService()
