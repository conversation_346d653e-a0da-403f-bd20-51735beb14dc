// 负责材料审核流程中的数据处理
import { cloneDeep, debounce } from 'lodash-es'
import dayjs from 'dayjs'
import { getLocalStorage, setLocalStorage, delLocalStorage, reportBizError } from '@galaxy/utils'
import { message } from 'antd'
import {
  getEntryMaterialDetail,
  getEntryMaterialAuditInfo,
  submitEntryMaterialAuditInfo,
  getAuditingPersonList,
} from '@/apis/review'

import { EntryDraftContent, SaveDraftParams } from '@/types/entryMaterialsAudit'
import { WATCH_MODES } from '@/constants/review'
import businessAuthor from '@/pages/entryMaterialsAudit/businessAuthor'
import { getUrlParam } from '@/utils/utils'
import userService from '@/services/user'
import { handleAuditResultSubmitData } from '@/pages/entryMaterialsAudit/Footer.tsx/submitUtils'
import { ProcessProgress } from '@/services/preEntry/list'
import ReModal from '@/components/ReModal'
import { comRegexp } from '@/constants/index'

const Day30Expires = 60 * 60 * 24 * 30 // 30天
const durationTime = 4 // 4s
class ReviewService {
  private draftData: EntryDraftContent = null

  get getDraftData() {
    // 有权限且入职材料id一致
    const { hasCanEditInfoAuth } = businessAuthor
    const entryMaterialId = getUrlParam('id', window.location.href)
    if (
      hasCanEditInfoAuth() &&
      this.draftData &&
      Number(this.draftData.entryMaterialId) === Number(entryMaterialId)
    ) {
      return this.draftData
    }
    return null
  }

  setDraftData = (data) => {
    this.draftData = data
  }

  // 获取入职材料详情
  getEntryMaterialDetail = async (entryMaterialId: number, config?: Record<string, any>) => {
    const { data } = await getEntryMaterialDetail(entryMaterialId, config)
    return data
  }

  // 获取入职材料审核信息
  getEntryMaterialAuditInfo = async (entryMaterialId: number) => {
    const { data } = await getEntryMaterialAuditInfo(entryMaterialId)
    return data
  }

  // 提交入职材料和入职材料审核信息
  submitEntryMaterialAuditInfo = async (
    entryMaterialId: number,
    version: number | string,
    params,
  ) => {
    const copyParams = params
    const timeRequireComplete = params.entryMaterialAudit?.timeRequireComplete
    if (timeRequireComplete) {
      copyParams.entryMaterialAudit.timeRequireComplete = timeRequireComplete.valueOf()
    }
    const {
      timeRequireComplete: _,
      rejectModifyTemplateId: __,
      auditResult: ___,
      ...rest
    } = copyParams.entryMaterialAudit

    Object.keys(rest).forEach((key) => {
      const item = rest[key]
      if (item.noPassReasonCodes) {
        const noPassReasonCodes = item.noPassReasonCodes.map((arr) => arr[arr.length - 1])
        rest[key].noPassReasonCodes = noPassReasonCodes
      }
    })
    const { data } = await submitEntryMaterialAuditInfo({
      entryMaterialId,
      version,
      params,
    })
    return data
  }

  // 获取正在审核该入职材料的人员名单列表
  getAuditingPersonList = async (entryMaterialId: number) => {
    const { data } = await getAuditingPersonList({
      entryMaterialId,
    })
    return data
  }

  // 获取草稿的key值
  getDraftKey = (entryMaterialId) => {
    return `draft_v3_${userService.info?.phone}_${userService.info?.num}_${entryMaterialId}`
  }

  // 加载草稿数据
  loadDraft = async (formData) => {
    try {
      // 草稿存储的key值根据用户工号， 入职材料id，来存储
      const entryMaterialId = formData.entryInfo.id
      const version = formData.processInfo?.version
      const processProgress = formData?.processInfo?.processProgress
      const $draftKey = this.getDraftKey(entryMaterialId)
      const $draftDataString = getLocalStorage($draftKey)
      const $draftData: EntryDraftContent = JSON.parse($draftDataString)
      if (
        $draftData &&
        $draftData.version === version &&
        $draftData.processProgress === processProgress
      ) {
        const { userInfo } = $draftData
        this.setDraftData($draftData)
        message.warning(
          `当前显示${userInfo?.nameAndNum}在 ${dayjs(userInfo?.updateTime).format(
            'YYYY-MM-DD HH:mm:ss',
          )} 保存的草稿。我们将为您实时保存草稿。`,
          durationTime,
        )
        return
      }
      this.setDraftData(null)
      message.warning(`我们将为您实时保存草稿。`, durationTime)
    } catch (error) {
      console.log(error)
    }
  }

  /**
   * @params SaveDraftParams
   */
  saveDraft = async ({
    formInstance,
    formData,
    updateCallback,
    updateFormData,
  }: SaveDraftParams) => {
    // 校验不通过不缓存
    if (
      !this.judgeDraft({
        formData,
        updateCallback,
        updateFormData,
      })
    ) {
      return
    }
    const { name, num } = userService.info
    const entryMaterialId = formData.entryInfo?.id
    const version = formData.processInfo?.version
    const processProgress = formData?.processInfo?.processProgress
    const $draftKey = this.getDraftKey(entryMaterialId)
    const values = formInstance.getFieldsValue()
    const cloneFieldsValue = cloneDeep(values)
    Object.keys(cloneFieldsValue).forEach((key) => {
      if (
        key.includes('_itemAuditResult') ||
        key.includes('_noPassReason') ||
        key.includes('_noPassReasonCodes')
      ) {
        delete cloneFieldsValue[key]
      }
    })
    const finalFieldsValues = {
      ...cloneFieldsValue,
      ...values,
    }
    const submitData = await handleAuditResultSubmitData(finalFieldsValues)
    const $content: EntryDraftContent = {
      ...submitData,
      entryMaterialMode: formInstance.getState(),
      entryMaterialId,
      version,
      processProgress,
      userInfo: {
        name,
        num,
        nameAndNum: `${name} (${num})`,
        updateTime: new Date().getTime(),
      },
    }
    this.setDraftData($content)
    const $contentStringify = JSON.stringify($content, (key, value) => {
      // 处理undefined丢失问题
      if (typeof value === 'undefined') {
        return null
      }
      return value
    })
    try {
      setLocalStorage($draftKey, $contentStringify, Day30Expires)
    } catch (err) {
      if (this.isOutOfSpace(err)) {
        // 清空所有缓存 todo: 清空过期缓存，当前清除过期缓存代价大，先清空所有草稿缓存(v2.0.0版本上线后会清空旧的草稿缓存)
        this.deleteAllDraftStorage()
        // 重新设置当前缓存
        setLocalStorage($draftKey, $contentStringify, Day30Expires)
      }
      reportBizError('缓存报错', {
        errorMsg: JSON.stringify(err),
      })
    }
  }

  /**
   * 判断草稿
   * @params Omit<SaveDraftParams, 'formInstance'>
   */
  judgeDraft = ({
    formData,
    updateCallback,
    updateFormData,
  }: Omit<SaveDraftParams, 'formInstance'>) => {
    const that = this
    let flag = true
    const { version, processProgress } = updateFormData.processInfo
    if (![ProcessProgress.Audit, ProcessProgress.PendingReview].includes(processProgress)) {
      message.error('草稿保存失败，当前候选人不在可审核状态下', durationTime)
      that.setDraftData(null)
      updateCallback?.()
      // 清空当前缓存
      that.deleteDraft(formData?.entryInfo?.id)
      return false
    }
    if (formData.processInfo?.version !== version) {
      flag = false
      ReModal.warning({
        title: '数据已更新',
        content: `检测到有新提交的数据，请点击“更新”查看最新数据后重新审核`,
        onOk: () => {
          that.setDraftData(null)
          updateCallback?.()
          // 清空当前缓存
          that.deleteDraft(formData?.entryInfo?.id)
        },
        okText: '更新',
      })
    }
    return flag
  }

  handleFormAndDraftData = (formData) => {
    if (this.getDraftData) {
      const { entryMaterialSubmit } = this.getDraftData
      const formDataCopy = cloneDeep(formData)
      Object.keys(entryMaterialSubmit).forEach((key) => {
        if (Array.isArray(formData[key])) {
          formDataCopy[key] = [...(entryMaterialSubmit?.[key] ?? [])]
        } else {
          formDataCopy[key] = {
            ...formDataCopy[key],
            ...(entryMaterialSubmit?.[key] ?? {}),
          }
        }
      })
      return formDataCopy
    }
    return formData
  }

  handleStatusAndDraftData = (statusData) => {
    if (this.getDraftData) {
      const { entryMaterialAudit } = this.getDraftData
      const statusDataCopy = {
        ...statusData,
        ...entryMaterialAudit,
      }
      return statusDataCopy
    }
    return statusData
  }

  handleStateAndDraftData = () => {
    if (this.getDraftData) {
      const { entryMaterialMode } = this.getDraftData
      const $modeObj = {}
      const WATCH_MODES_VALUES = Object.values(WATCH_MODES)
      Object.keys(entryMaterialMode).forEach((key) => {
        if (WATCH_MODES_VALUES.includes(key)) {
          $modeObj[key] = entryMaterialMode[key]
        }
      })
      return $modeObj
    }
    return null
  }

  getDraftStorageKeys = (): string[] => {
    // 获取 localStorage 中所有键的数量
    const len = localStorage.length
    // 创建一个数组，用于存储 localStorage 中的所有draft键
    const keys: string[] = []
    // 遍历 localStorage 中的所有键，并将它们存储到数组中
    for (let i = 0; i < len; i++) {
      const $curKey = localStorage.key(i)
      if (comRegexp?.draftReg?.test($curKey)) {
        keys.push($curKey)
      }
    }
    // 打印所有键
    return keys
  }

  deleteDraft = (entryMaterialId) => {
    const $draftKey = this.getDraftKey(entryMaterialId)
    delLocalStorage($draftKey)
  }

  deleteAllDraftStorage = () => {
    const $draftKeys = this.getDraftStorageKeys()
    $draftKeys?.forEach?.((key) => {
      delLocalStorage(key)
    })
  }

  isOutOfSpace = (e) => {
    return (
      e &&
      (e.name === 'QUOTA_EXCEEDED_ERR' ||
        e.name === 'NS_ERROR_DOM_QUOTA_REACHED' ||
        e.name === 'QuotaExceededError')
    )
  }
}

export default new ReviewService()
