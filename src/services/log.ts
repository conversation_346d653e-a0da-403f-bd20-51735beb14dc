import { GetDminEntryV1LogsConfigureParams, getDminEntryV1LogsConfigure } from '@/apis/log'
import { PageResponse } from '@/types/common'

type GetConfigLogListParams = {
  timeOperateBegin?: number
  timeOperateEnd?: number
  type?: number[]
  operatorUserNumOrName?: string
  page?: number
  size?: number
}

type ConfigLogData = {
  /** 操作内容 */
  content?: string
  /** 操作时间 */
  operateTime?: number
  /** 操作人 */
  operator?: string
  /** 配置类型 */
  type?: 1 | 2 | 3
}

class Service {
  getConfigLogList = (data: GetConfigLogListParams): Promise<PageResponse<ConfigLogData[]>> => {
    const { timeOperateBegin, timeOperateEnd, type, operatorUserNumOrName, page, size } = data
    const params: GetDminEntryV1LogsConfigureParams = {
      timeOperateBegin,
      timeOperateEnd,
      types: type && type.join(','),
      operatorUserNumOrName,
      page,
      size,
    }

    return getDminEntryV1LogsConfigure(params).then((res) => ({
      count: res.count,
      data: res.data.map((item) => {
        const { content, id, operateTime, type: $type, operator, operatorName } = item

        return {
          content,
          id,
          operateTime,
          type: $type,
          operator: `${operatorName || ''}（${operator || '-'}）`,
        }
      }),
    }))
  }
}

export default new Service()
