import '@pupu/apm-tracing/dist/global.patch'
import { nameDesensitization } from '@/utils/utils'
import rbac from '@galaxy/rbac'
import config from '@/config'
import userService from '@/services/user'

const { environment } = config

export const StartApmMonitor = () => {
  try {
    window.Apm?.onLoad?.(() => {
      const Integrations = window?.Apm?.Integrations

      window?.Apm?.init?.({
        appId: '10001000', // 应用id  朴朴DHR平台
        environment: environment === 'prod' ? 'prod' : 'test', // 当前应用的环境, 支持.prod,test
        ignoreErrors: [], // 你要忽略的错误
        tracesSampleRate: 0.2,
        release: '1.0.1', // 标识当前应用的版本包
        beforeSend(event) {
          // DOC: https://doc.weixin.qq.com/doc/w3_AaEA0gYEAGgIqgki02kQtyVii3vWo?scode=AJ0ARQd1AAwyaM3JOhAaEA0gYEAGg
          // [qiankun]: Target container with #root not existed while recruitment loading
          try {
            const regexps = [
              new RegExp('Target container with (.*?) not existed while'),
              new RegExp('(.*?)[全埋点](.*?)'),
              new RegExp('(.*?)__gCrWeb(.*?)'),
            ]
            const $exceptionType3 = event?.exception?.exception_type_3
            for (let i = 0; i < regexps.length; i++) {
              if ($exceptionType3?.match(regexps[i])) {
                return null
              }
            }
          } catch (e) {
            return event
          }

          return event
        },
        beforeBreadcrumb: (data) => {
          // 防止版本更新轮破坏线上错误堆栈
          if (data?.category === 'fetch' && data?.data?.url?.includes('/version.json')) {
            return null
          }
          return data
        },
        integrations: !Integrations
          ? []
          : [
              new Integrations.BrowserTracing(), // 页面性能监控
              new Integrations.RequestTracing({
                allowUrls: [],
                // 2022.12.12 针对接口返回的业务处理做转化，之后再apm中需要使用接口错误这个分类来进行搜索。只会搜索errmsg字段。
                // @ts-ignore
                getResponseResult: (json) => {
                  const { code, msg } = json
                  if (code === 0) {
                    return null
                  }
                  return { errcode: code, errmsg: msg, name: msg }
                },
              }), // 接口监控
            ],
      })
    })

    // 事件处理回调. 支持在上报前对上报日志修改
    Apm?.addGlobalEventProcessor?.((event) => {
      // 上报用户信息
      const userInfo = userService.info
      if (userInfo) {
        try {
          /* eslint no-param-reassign: "error" */
          event.user = {
            ...event.user,
            name: nameDesensitization(userInfo.name),
            num: userInfo.num,
            id: userInfo.num,
            positionName: userInfo.positionName,
          }
        } catch (e) {
          console.log('用户信息获取失败', e)
        }
      }

      // 上报当前用户租户和组件信息
      const currentEntity = rbac.getEntity()

      if (event.contexts && !event.contexts.currentEntity) {
        event.contexts.currentEntity = {}
      }

      if (event.contexts && currentEntity) {
        try {
          currentEntity.id =
            typeof currentEntity.id === 'number' || typeof currentEntity.id === 'string'
              ? String(currentEntity.id)
              : ''
          event.contexts.currentEntity = { ...currentEntity }
        } catch (e) {
          console.log('租户相关信息获取失败', e)
        }
      }

      // 上报应用信息
      if (!event.tags) {
        event.tags = {}
      }
      try {
        event.tags.microapp = 'entry'
      } catch (e) {
        console.log('分析所属微应用失败', e)
      }
      return event
    }) // 必须有返回, 如果不想上报这个日志,返回null
  } catch (error) {
    console.log(error)
  }
}
