class Debounce {
  fn: Function

  delay: number

  args: Set<string> = new Set()

  resolves: any = []

  rejects: any = []

  timeout: any = null

  constructor(fn, delay) {
    this.fn = fn
    this.delay = delay
  }

  debounce(args: string) {
    this.args.add(args)
    const promise = new Promise((resolve, reject) => {
      this.resolves.push(resolve)
      this.rejects.push(reject)
    })

    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    this.timeout = setTimeout(() => {
      this.fn([...this.args])
        .then((result) => {
          this.resolves.forEach((resolve) => resolve(result))
          this.resolves = []
        })
        .catch((error) => {
          this.rejects.forEach((reject) => reject(error))
          this.rejects = []
        })
      this.args.clear()
      this.timeout = null
    }, this.delay)

    return promise
  }
}

export default Debounce
