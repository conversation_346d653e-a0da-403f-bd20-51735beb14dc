import { isEqual, isUndefined, isPlainObject } from 'lodash-es'
import { toUnderline } from '@galaxy/utils'
import config from '@/config'
import userService from '@/services/user'

export function getUrlParam(name, url) {
  return (
    decodeURIComponent(
      (new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`).exec(url) || ['', ''])[1].replace(
        /\+/g,
        '%20',
      ),
    ) || null
  )
}

// 时间格式转换
export function formatDate(cTime, cFormat) {
  // 判空
  if (arguments.length === 0 || typeof cTime === 'undefined' || !cTime) {
    return null
  }
  let date = cTime
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  // 如果不是Date对象
  if (typeof date !== 'object') {
    if (typeof date === 'string') {
      if (/^[0-9]+$/.test(date)) {
        // support "1548221490638"
        date = Number(date)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        date = date.replace(/-/gm, '/')
      }
    }

    if (typeof time === 'number' && date.toString().length === 10) {
      date *= 1000
    }
    date = new Date(date)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}

export function getZhLen(value) {
  let num = 0
  const wordLen = value?.length
  const mailzh = /^[\u3400-\u4DBF\u4e00-\u9fa5]{0,}$/
  for (let i = 0; i < wordLen; i++) {
    // 使用正则表达式，判断字符是否为汉字编码
    const matches = mailzh.test(value[i])
    if (matches) {
      // 如果是汉字
      num += 1 // 则累加
    }
  }
  return num + wordLen
}

// eslint-disable-next-line max-params
export function listToTree(
  data,
  id = 'id',
  parentId = 'parentId',
  children = 'children',
  rootId = 0,
) {
  // 对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data))
  // 循环所有项
  const treeData = cloneData.filter((item) => {
    const father = item
    const branchArr = cloneData.filter((child) => father[id] === child[parentId])
    father[children] = branchArr.length > 0 ? branchArr : null
    // 返回第一层
    return Number(father[parentId]) === rootId
  })
  return treeData.length !== 0 ? treeData : data
}

export function treeToList(data) {
  const result = []
  data.forEach((item) => {
    const loop = (tmp) => {
      result.push({
        ...tmp,
        children: null,
      })
      const child = tmp.children
      if (child && child.length) {
        for (let i = 0; i < child.length; i++) {
          loop(child[i])
        }
      }
    }
    loop(item)
  })
  return result
}

/**
 * 字典项目处理成树形结构
 * @param list
 * @returns
 */
export function handleTreeOptions(list) {
  return list.map((item) => {
    const copyItem = item
    if (item.children) {
      copyItem.children = handleTreeOptions(item.children)
    }
    return {
      label: item.name,
      value: item.code,
      children: item.children,
    }
  })
}

// 根据url下载文件
export const downloadFile = (url, fileName = '') => {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName || ''
  link.click()
  link.remove()
}

/**
 * 比较两个新旧对象值是否不相等
 * @param oldObj
 * @param newObj
 * @returns boolean
 */
export function compare(oldObj, newObj) {
  return Object.keys(newObj).some((key) => {
    if (Object.prototype.hasOwnProperty.call(newObj, key)) {
      if (newObj[key] !== '' && !isEqual(newObj[key], oldObj?.[key])) {
        return true
      }
    }
    return false
  })
}

/**
 * 通用脱敏处理方法
 * @param {*} str
 * @param {*} beginLen
 * @param {*} endLen
 * @returns
 */
export function desensitization(str, beginLen, endLen) {
  const len = str.length
  const firstStr = str.substr(0, beginLen)
  const lastStr = str.substr(endLen)
  const middleStr = str.substring(beginLen, len - Math.abs(endLen)).replace(/[\s\S]/gi, '*')
  const tempStr = firstStr + middleStr + lastStr
  return tempStr
}

export function nameDesensitization(str) {
  if (str === null || str === undefined || str.length === 0) {
    return ''
  }
  if (str.length === 1) {
    return str
  }
  if (str.length === 2) {
    return `${str.substring(0, 1)}*`
  }

  let starStr = ''
  for (let index = 0; index < str.length - 2; index++) {
    starStr += '*'
  }
  return str.substring(0, 1) + starStr + str.substring(str.length - 1, str.length)
}

// 多列排序，默认排序
// 升级table后sorter参数变化，需要调整
export const handleSort = (sorter) => {
  const sort = []
  const orderMap = {
    ascend: 'asc',
    descend: 'desc',
  }

  if (Array.isArray(sorter)) {
    sorter.forEach((item) => {
      if (!isUndefined(item.order)) {
        sort.push(`${toUnderline(item.field)},${orderMap[item.order]}`)
      }
    })
  } else if (isPlainObject(sorter)) {
    if (sorter.field) {
      !isUndefined(sorter.order) &&
        sort.push(`${toUnderline(sorter.field)},${orderMap[sorter.order]}`)
    } else if (Object.keys(sorter).length === 1) {
      // 默认，第一次的默认和单个排序的返回对象格式不一样
      const key = Object.keys(sorter)[0]
      !isUndefined(sorter[key]) && sort.push(`${toUnderline(key)},${orderMap[sorter[key]]}`)
    } else {
      // 默认排序
      Object.keys(sorter).forEach((key) => {
        sort.push(`${toUnderline(key)},${orderMap[sorter[key]]}`)
      })
    }
  }
  return sort
}

export function inpLimit(value, preValue = '', len = 0) {
  if (!value) {
    return ''
  }
  const preLen = preValue ? getZhLen(preValue) : 0
  const valueLen = getZhLen(value)
  if (preLen >= len && preLen < valueLen) {
    return preValue
  }
  return value
}

export function generateWaterMaskContent() {
  if (!userService.info) {
    return undefined
  }
  let result = `${userService.info.num || userService.info.userName}-${
    userService.info.name
  }-${formatDate(new Date(), '{y}.{m}.{d} {h}:{i}')}`
  if (config.environment !== 'prod') {
    result += `-${config.environment}`
  }
  return result
}
