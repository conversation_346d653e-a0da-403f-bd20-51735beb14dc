import axios, { dataAssignmentResponse } from '@galaxy/business-request'
import config from '@/config'

const instance = axios.inheritCreate({
  ppCheckToken: true,
})

const { environment, serverUrl } = config

const request = (_config) => {
  const matchRegExp = /http|https/
  const localUrl = `${window.location.origin}/mock`
  const { isMock = false } = _config
  // 仅允许本地环境使用Mock功能；
  const SERVER_URL = environment === 'local' && isMock ? localUrl : serverUrl
  const url = `${matchRegExp.test(_config.url) ? '' : SERVER_URL}${_config.url}${
    _config.url.indexOf('?') > -1 ? '&t=' : '?t='
  }${Date.now()}`
  return {
    ..._config,
    url,
  }
}

instance.interceptors.response.use(dataAssignmentResponse)
instance.interceptors.request.use(request)

export default instance
