import { message } from 'antd'
import request, { setNamespace } from '@galaxy/business-request'
import { reportHttpError, reportBizError } from '@galaxy/utils'
import { logout } from '@/mainApp/services/authService'
import conf from '@/config'
import { BIZ_ERRORS } from '@/constants'

setNamespace(conf.publicPath)

/**
 * 1. token为空被ppCheckToken拦截
 * 2. 401错误拦截
 * 3. 提前标记了错误不上报
 * @param error
 */
function needReport(error) {
  return (
    !['TokenError/checkToken', 'UNAUTHORIZED', 'AbortError'].includes(error.code) &&
    !error.apmIgnore
  )
}

const responseMessageError = (error) => {
  const { status, msg, config } = error
  if (!config?.noMessage) {
    let errMessage = msg || `服务器开小差，请稍后重试。错误状态码：${status}`
    if (status === 401) {
      errMessage = '登录超时，重新登录跳转中...'
    }
    message.error(errMessage, 3)
  }

  return Promise.reject(error)
}

const response401Error = (error) => {
  const { status, msg, config } = error
  if (status === 401) {
    setTimeout(() => {
      // 登出
      logout()
    }, 3000)
  }
  return Promise.reject(error)
}

const responseApmError = (error) => {
  const { msg: $msg, response, body } = error || {}
  const isTimeout = error?.message?.indexOf('timeout') > -1
  let msg = $msg
  if (isTimeout || body?.code === 'ECONNABORTED') {
    msg = '请求超时，请稍后重试。'
  }

  try {
    if (needReport(error)) {
      if (BIZ_ERRORS.includes(msg)) {
        reportBizError(msg, {
          errorMsg: JSON.stringify(response),
        })
      } else {
        reportHttpError(msg || '服务器开小差，请稍后重试。', response, {})
      }
    }
    error.apmIgnore = true
  } catch (err) {
    console.error('reportHttpError上报失败，原因为：')
    console.error(err)
  }
  return Promise.reject(error)
}

// qiankun和独立访问时候都要执行
request.interceptors.response.use(undefined, responseApmError, 'globalApm')

export function init() {
  request.interceptors.response.use(undefined, responseMessageError)
  request.interceptors.response.use(undefined, response401Error)
}
