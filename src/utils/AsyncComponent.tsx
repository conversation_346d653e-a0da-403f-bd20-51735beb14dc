import React, { Component } from 'react'
import { Skeleton } from 'antd'

export default function asyncComponent(importComponent) {
  class AsyncComponent extends Component {
    // @ts-ignore
    state: {
      component: React.FC | null
    }

    constructor(props) {
      super(props)

      this.state = {
        component: null,
      }
    }

    async componentDidMount() {
      const { default: component } = await importComponent()

      this.setComponent(component)
    }

    setComponent = (component) => {
      this.setState({
        component,
      })
    }

    render() {
      const C = this.state.component

      return C ? (
        <C {...this.props} />
      ) : (
        <div
          style={{
            padding: '20px',
          }}
        >
          <Skeleton active />
          <Skeleton
            active
            paragraph={{
              rows: 15,
            }}
          />
        </div>
      )
    }
  }

  return AsyncComponent
}
