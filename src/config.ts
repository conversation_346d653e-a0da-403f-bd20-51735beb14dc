import defaultConfig from '../config'
import { isMicroApp } from './constants/index'

const isMultiVersion = window.location.pathname.indexOf('/multi-version') > -1
const prefix = isMultiVersion ? '/multi-version' : ''
const suffix = isMultiVersion ? `/${process.env.BUILD_VERSION || ''}` : ''

const config = {
  ...defaultConfig,
  baseRoute: `${prefix}${isMicroApp ? '' : defaultConfig.baseRoute}${suffix}`,
  openBaseRoute: prefix + (isMicroApp ? '' : defaultConfig.baseRoute) + suffix, // window.open时项目路径
}
export default config
