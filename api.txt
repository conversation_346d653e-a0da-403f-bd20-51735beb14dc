{dictHttp}/open/basic/v1/dicts/query	POST	--	--	src/apis/review.ts
{mockHttp}/admin/entry/v1/entry_materials/{entryMaterialId}	GET	获取入职材料审核信息	--	src/apis/review.ts
{mockHttp}/admin/entry/v1/entry_materials/{entryMaterialId}/audit	GET	提交入职材料和入职材料审核信息	--	src/apis/review.ts
{mockHttp}/admin/entry/v1/entry_materials/{entryMaterialId}/audit	POST	获取正在审核该入职材料的人员名单列表	--	src/apis/review.ts
{mockHttp}/admin/entry/v1/entry_materials/{entryMaterialId}/audit/person	POST	获取STS临时授权	--	src/apis/review.ts
/admin/entry/v1.0/async_task/{id}	GET	--	--	src/apis/task.ts
/admin/entry/v1.1/cs/sts_token	GET	--	--	src/apis/review.ts
/admin/entry/v1/configures	GET	doc.weixin.qq.com/doc/w3_AA4A4wZmACU9rp1rCOpQb0FdQNVvP?scode=AJ0ARQd1AAw0219N01AA4A4wZmACU */ code:string /** 具体配置名称 */ name?:string /** 排序字段 */ orderNum?:number /** 配置类型 1-规则配置 2-开户银行配置3-复核规则配置 */ type:number /** 值 */ value?:string}/** 4002:编辑配置 */	--	src/apis/config.ts
/admin/entry/v1/configures	POST	--	--	src/apis/config.ts
/admin/entry/v1/ehr_dict/area	GET	10000403:根据条件查询实体岗位信息列表	--	src/apis/common.ts
/admin/entry/v1/entry_materials/{entryMaterialId}/employment	GET	【v1.5】查询入职办理地	--	src/apis/employ.ts
/admin/entry/v1/entry_materials/{id}/audit/end	POST	--	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/{id}/audit/push	POST	--	--	src/apis/audit.ts
/admin/entry/v1/entry_materials/{id}/audit/reject	POST	--	--	src/apis/audit.ts
/admin/entry/v1/entry_materials/{id}/end	POST	ip:port?sort=id,asc&sort=user_name,desc */ sort?:string}/** 1001:获取入职材料的操作记录列表 */	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/{id}/logs	GET	1010:【v1.3】结束审核	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/{id}/message/entry	POST	--	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/{id}/message/modify	POST	1007:结束招聘	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/{id}/review	POST	--	--	src/apis/audit.ts
/admin/entry/v1/entry_materials/completed	GET	--	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/export	POST	--	--	src/apis/export.ts
/admin/entry/v1/entry_materials/processing	GET	1008:获取入职材料列表-已完成	--	src/apis/onboardingMaterial.ts
/admin/entry/v1/entry_materials/review/batch	POST	--	--	src/apis/audit.ts
/admin/entry/v1/entry_materials/time_statistics_export	POST	--	--	src/apis/export.ts
/admin/entry/v1/entry_place	GET	--	--	src/apis/employ.ts
/admin/entry/v1/logs/configure	GET	--	--	src/apis/log.ts
/admin/entry/v1/personnel_standard	GET	--	--	src/apis/common.ts
/admin/entry/v1/position	GET	【v1.7】保存通用记录	--	src/apis/common.ts
/admin/entry/v1/record	POST	--	--	src/apis/common.ts
/admin/entry/v1/templates	GET	--	--	src/apis/template.ts
/admin/entry/v1/templates	POST	--	--	src/apis/template.ts
/admin/entry/v1/templates/{id}	GET	--	--	src/apis/template.ts
/admin/entry/v1/templates/{id}	PUT	--	--	src/apis/template.ts
/admin/entry/v1/templates/{id}	DELETE	--	--	src/apis/template.ts