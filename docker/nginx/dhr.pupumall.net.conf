server {
    listen 80;
    server_name dhr.pupumall.net;

    location /static {
        root /webapp;
    }

    location /entry-webapp {
        #### no cache
        if ($request_filename ~* \.html$) {
            add_header Last-Modified $date_gmt;
            add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
            expires off;
        }
        root /webapp;
        index index.html;
        try_files $uri /entry-webapp/index.html;
    }

    location ~* ^.+\.(jpg|jpeg|gif|png|ico|css|js)$ {
        root   /webapp;
        access_log off;
        expires 30d;
    }
}
