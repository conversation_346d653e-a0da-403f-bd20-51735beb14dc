const fs = require('fs')
const path = require('path')

const API_PATH = 'src/apis'

function extractApiFunctions(code) {
  // 匹配以 export const 开头的函数声明，使用非贪婪匹配直到遇到下一个 export 或文件结束
  const functionRegex = /export const\s+(\w+)\s*=([\s\S]*?)(?=\n\s*export const|$)/g

  const functions = []
  let match

  while ((match = functionRegex.exec(code)) !== null) {
    // 获取完整的函数表达式
    const functionContent = match[0].trim()

    // 清理函数内容：移除连续空行、行首尾空格，保留必要空格
    const cleanedFunction = functionContent
      .split('\n')
      .map((line) => line.trim())
      .join(' ')
      .replace(/\s*([=():>{},])/g, '$1')   // 清理符号前多余空格
      .replace(/([=():<{},])\s*/g, '$1') // 清理符号后多余空格

    functions.push(cleanedFunction)
  }

  return functions
}



// 专用的API解析方法
function extractApiDetails(apiDeclarations) {
  return apiDeclarations.map((declaration) => {
    // 提取中文名称
    const nameMatch = declaration.match(/\/\/\s*(.+)/)
    const name = nameMatch ? nameMatch[1].trim() : '--'

    // 提取HTTP方法
    let method = 'GET'
    if (declaration.includes('.post')) {
      method = 'POST'
    } else if (declaration.includes('.put')) {
      method = 'PUT'
    } else if (declaration.includes('.delete')) {
      method = 'DELETE'
    } else if (declaration.includes('.request')) {
      const methodMatch = declaration.match(/method\s*:\s*'(\w+)'/)
      method = methodMatch ? methodMatch[1].toUpperCase() : 'GET'
    }

    // 提取URL并统一参数格式
    let url = ''

    // 处理模板字符串
    const templateMatch = declaration.match(/`((?:\\`|.)*?)`/)
    if (templateMatch) {
      url = templateMatch[1]
        // 统一处理所有参数格式为{param}
        .replace(/\${(\w+\.)?(\w+)}|:(\w+)/g, (_, prefix, name1, name2) =>
          `{${name1 || name2}}`)
        // 简化条件表达式（仅保留参数部分）
        .replace(/\${.*?`([^`]*)`.*?}/g, '$1')
    }
    // 处理普通字符串
    else {
      const standardMatch = declaration.match(/['"]([^"']+)['"]/)
      if (standardMatch) { url = standardMatch[1] }
    }

    // 移除查询参数（保留路径部分）
    url = url.split('?')[0]

    return { method, url, name }
  })
}
/**
 * 从代码文本中提取 service 属性名
 * @param {string} codeText 代码文本
 * @returns {string|null} 找到的 service 属性名，未找到返回 null
 */
function extractServiceName(codeText) {
  // 匹配两种写法：
  // 1. const services: ActionContext = service.talentReview
  // 2. const services = service.talentReview
  const regex = /const\s+services\s*(?::\s*\w+)?\s*=\s*service\.(\w+)/

  const match = codeText.match(regex)
  return match ? match[1] : null
}

function extractApiInfo(content) {
  const apiArray = extractApiFunctions(content)
  const apiDetails = extractApiDetails(apiArray)
  return apiDetails
}

function sortAndDeduplicateApis(apiArray) {
  // 1. 创建URL清理函数
  const cleanUrl = (url) => {
    // 移除URL中的查询参数和变量模板部分
    return url
      .split('?')[0]     // 移除查询参数
      .replace(/\${.*/, '') // 移除JS模板部分
      .trim()
  }

  // 2. 双重排序：先按serviceName，再按清理后的URL
  const sortedApis = [...apiArray].sort((a, b) => {
    // 第一级排序：serviceName
    if (a.serviceName < b.serviceName) { return -1 }
    if (a.serviceName > b.serviceName) { return 1 }

    // 第二级排序：URL（使用清理后的版本）
    const cleanA = cleanUrl(a.url)
    const cleanB = cleanUrl(b.url)

    return cleanA.localeCompare(cleanB)
  })

  const uniqueMap = new Map()
  const duplicates = []
  const result = []

  // 3. 遍历排序后的API，进行去重处理
  sortedApis.forEach((api) => {
    const cleanURL = cleanUrl(api.url)
    const key = `${api.method}:${cleanURL}`

    if (uniqueMap.has(key)) {
      duplicates.push({
        duplicate: api,
        original: uniqueMap.get(key)
      })
    } else {
      uniqueMap.set(key, api)
      result.push(api)  // 只添加唯一API到结果
    }
  })

  // 4. 打印重复项信息（按服务名分组）
  if (duplicates.length > 0) {
    console.log(`发现 ${duplicates.length} 个重复接口：`)

    // 按服务名分组重复项
    const grouped = duplicates.reduce((groups, { duplicate, original }) => {
      const service = duplicate.serviceName
      groups[service] = groups[service] || []
      groups[service].push({ duplicate, original })
      return groups
    }, {})

    // 打印分组后的重复项
    Object.entries(grouped).forEach(([service, entries]) => {
      console.log(`\n【服务分组: ${service}】`)

      entries.forEach(({ duplicate, original }, i) => {
        console.log(`├─ 重复项 ${i + 1}: ${duplicate.name || '未命名接口'}`)
        console.log(`│  ├─ 方法: ${duplicate.method}`)
        console.log(`│  ├─ URL: ${duplicate.url}`)
        console.log(`│  └─ 文件: ${duplicate.sourceFile}`)

        console.log(`└─ 匹配到原始接口: ${original.name || '未命名接口'}`)
        console.log(`   ├─ 方法: ${original.method}`)
        console.log(`   ├─ URL: ${original.url}`)
        console.log(`   └─ 文件: ${original.sourceFile}\n`)
      })
    })
  } else {
    console.log('未发现重复接口')
  }

  return result
}

function generateExcelString(dataArray) {
  return dataArray.map((item) =>
    [item.url, item.method, item.name, item.serviceName, item.sourceFile].join('\t')
  ).join('\n')
}



// 获取目录下所有文件（包括子目录）
function getAllFiles(dirPath, fileList = []) {
  const files = fs.readdirSync(dirPath)
  if (files.length === 0) {
    console.error(`目录[${API_PATH}]下没有内容`)
    return []
  }
  // 避免使用迭代器
  for (let i = 0; i < files.length; i++) {
    const filePath = path.join(dirPath, files[i])
    const stat = fs.statSync(filePath)

    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList)
    } else if (filePath.endsWith('.js') || filePath.endsWith('.ts')) {
      fileList.push(filePath)
    }
  }

  return fileList
}

function main() {
  // 主处理流程
  const apiDir = path.join(process.cwd(), API_PATH)
  const outputFile = path.join(process.cwd(), 'api.txt')

  const apiEntries = []
  let files
  try {
    files = getAllFiles(apiDir)
  } catch (e) {
    console.error(`读取[${API_PATH}]失败`)
    return
  }

  // 避免使用迭代器
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const relativePath = path.relative(process.cwd(), file)

    try {
      const content = fs.readFileSync(file, 'utf-8')
      const fileEntries = extractApiInfo(content)
      const serviceName = extractServiceName(content)
      console.log(`读取文件：${relativePath}`)
      console.log(`解析API ${fileEntries.length} 个`)

      // 为每个条目添加来源文件信息和服务名
      for (let j = 0; j < fileEntries.length; j++) {
        fileEntries[j].sourceFile = relativePath
        fileEntries[j].serviceName = serviceName || "--"
      }

      apiEntries.push(...fileEntries)
      // console.info(apiEntries)
    } catch (e) {
      console.error(`解析文件错误: ${file}`, e)
    }
  }

  const result = sortAndDeduplicateApis(apiEntries)
  // 生成并保存文档
  if (result.length > 0) {
    const markdown = generateExcelString(result)
    fs.writeFileSync(outputFile, markdown)
    console.log(`API文档已生成: ${outputFile}`)
    console.log(`共找到 ${result.length} 个API端点`)
  } else {
    console.error('未找到有效的API定义')
  }
}

main()
