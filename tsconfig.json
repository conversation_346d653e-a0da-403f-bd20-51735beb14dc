{"compilerOptions": {"experimentalDecorators": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "react", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "paths": {"@/*": ["./src/*"]}, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "strictNullChecks": false, "noImplicitAny": false, "resolveJsonModule": true, "skipLibCheck": true, "plugins": [{"transform": "ts-auto-mock/transformer"}], "forceConsistentCasingInFileNames": false}, "include": ["./src/**/*"]}