module.exports = {
  alias: { fd: 'docs: fix typos' }, // TODO: 这个是做啥的
  messages: {
    type: '请选择你要提交的类型：',
    scope: '请选择一个 scope',
    customScope: '请输入自定义的 scope：',
    subject: '填写简短精炼的变更描述：\n',
    confirmCommit: '确认以上内容的提交？',
  },
  types: [
    { value: 'docs', name: 'docs:文档变更' },
    { value: 'feat', name: 'feat:新增功能' },
    { value: 'fix', name: 'fix:修复 bug' },
    { value: 'style', name: 'style:代码格式（不影响功能，例如空格、分号等格式修正）' },
    { value: 'refactor', name: 'refactor:代码重构（不包括 bug 修复、功能新增）' },
    { value: 'build', name: 'build: 变更项目构建或外部依赖' },
    { value: 'ci', name: 'ci:修改 CI 配置、脚本' },
    { value: 'revert', name: 'revert:代码回退' },
    { value: 'test', name: 'test:添加、修改测试用例' },
    { value: 'chore', name: 'chore:对构建过程或辅助工具和库的更改' },
  ],
  scopeOverrides: {
    docs: [
      { name: 'api修改' },
      { name: '文档修改' },
    ],
    feat: [
      { name: '业务功能' },
      { name: '组件功能' },
    ],
    fix: [
      { name: 'tapd缺陷' }, // 能不能选择这个之后，再让用户输入缺陷号
      { name: '组件功能' },
    ],
    style: [],
    refactor: [
      { name: '性能优化' },
      { name: '逻辑优化' },
      { name: '可读性优化' },
    ],
    build: [
      { name: '构建' },
      { name: '依赖' },
    ],
    ci: [
      { name: 'nginx' },
      { name: 'docker' },
      { name: 'git' },
      { name: 'lint' },
    ],
    revert: [],
    test: [],
    chore: [],
  },
  useEmoji: false,
  emojiAlign: 'center',
  themeColorCode: '',
  // scopes: [
  //   ['业务逻辑', '业务逻辑相关'],
  //   ['样式', '样式相关'],
  //   ['类型', '类型定义相关'],
  //   ['依赖', '项目依赖'],
  //   ['构建', '构建相关'],
  //   ['deps', '项目依赖'],
  //   ['config', '项目配置'],
  //   ['other', '其他修改'],
  // ].map(([value, description]) => {
  //   return {
  //     value,
  //     name: `${value.padEnd(30)} (${description})`,
  //   }
  // }),

  // issuePrefixs: [{ value: 'closed', name: 'closed:   BUG 的解决或关闭' }],
  allowCustomScopes: true, // 允许自定义 scope
  allowEmptyScopes: true, // 允许不填写 scope
  customScopesAlign: 'top', // 自定义 scope 排放位置
  emptyScopesAlias: '跳过', // 空 scope 别名
  customScopesAlias: '自定义', // 自定义 scope 别名
  // emptyIssuePrefixsAlias: '跳过', // 空 issue(bug) 前缀别名
  customIssuePrefixsAlias: '自定义', // 自定义 issue(bug) 前缀别名
  upperCaseSubject: false, // 简单变更描述首字母是否大写
  markBreakingChangeMode: false, // 重大变更模式：y - 会在 type(scope) 后加上 !
  allowBreakingChanges: [], // 需要输入重大变更的 type 数组
  skipQuestions: [
    // 'body',
    'footerPrefix',
    'footer',
  ], // 跳过某个选项
  customIssuePrefixsAlign: 'top', // 自定义 Issue(BUG)前缀 排放位置
  allowCustomIssuePrefixs: true, // 允许自定义 Issue(BUG) 前缀
  allowEmptyIssuePrefixs: true, // 允许空 Issue(BUG) 前缀
  subjectLength: 50, // 简单变更描述最大长度限制 (type + scope + subject)
  minSubjectLength: 0, // 简单变更描述最小长度限制
  defaultBody: '',
  defaultIssues: '',
  defaultScope: '',
  defaultSubject: '',
}
